/* Import fonts first */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

/* Import TailwindCSS */
@import 'tailwindcss';

/* AppExtera Global Styles */
:root {
    /* AppExtera Brand Colors - Now integrated with Tailwind */
    --color-primary: theme('colors.primary.500');
    --color-primary-dark: theme('colors.primary.600');
    --color-primary-light: theme('colors.primary.400');
    --color-secondary: theme('colors.secondary.500');
    --color-accent: theme('colors.accent.500');
    --color-success: theme('colors.success.500');
    --color-warning: theme('colors.warning.500');
    --color-error: theme('colors.error.500');

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, theme('colors.primary.500') 0%, theme('colors.accent.500') 100%);
    --gradient-secondary: linear-gradient(135deg, theme('colors.blue.500') 0%, theme('colors.purple.600') 100%);

    /* Shadows */
    --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
    --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-large: 0 10px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-colored: 0 10px 40px -10px rgba(14, 165, 233, 0.3);

    /* Typography */
    --font-family-sans: theme('fontFamily.sans');
    --font-family-mono: theme('fontFamily.mono');
}

/* Dark mode variables */
@media (prefers-color-scheme: dark) {
    :root {
        --color-primary: #38bdf8;
        --color-primary-dark: #0ea5e9;
    }
}

/* Base styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-sans);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Custom utility classes */
.gradient-primary {
    background: var(--gradient-primary);
}

.gradient-secondary {
    background: var(--gradient-secondary);
}

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: var(--shadow-soft);
}

.shadow-colored {
    box-shadow: var(--shadow-colored);
}

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .rtl\:text-right {
    text-align: right;
}

[dir="rtl"] .rtl\:text-left {
    text-align: left;
}

/* Modern Button Components */
.btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 focus:bg-primary-600 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 border-none cursor-pointer shadow-soft hover:shadow-medium focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.btn-secondary {
    @apply bg-secondary-100 hover:bg-secondary-200 focus:bg-secondary-200 text-secondary-900 font-medium px-6 py-3 rounded-lg transition-all duration-200 border-none cursor-pointer shadow-soft hover:shadow-medium focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
}

.btn-outline {
    @apply bg-transparent hover:bg-primary-500 focus:bg-primary-500 text-primary-500 hover:text-white focus:text-white font-medium px-6 py-3 rounded-lg border-2 border-primary-500 transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.btn-ghost {
    @apply bg-transparent hover:bg-secondary-100 focus:bg-secondary-100 text-secondary-700 hover:text-secondary-900 focus:text-secondary-900 font-medium px-6 py-3 rounded-lg transition-all duration-200 border-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
}

.btn-gradient {
    @apply bg-gradient-primary hover:opacity-90 focus:opacity-90 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 border-none cursor-pointer shadow-colored hover:shadow-large focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

/* Modern Card Components */
.card {
    @apply bg-white rounded-xl shadow-soft border border-secondary-200 overflow-hidden;
}

.card-hover {
    @apply bg-white rounded-xl shadow-soft border border-secondary-200 transition-all duration-300 hover:shadow-medium hover:border-secondary-300 overflow-hidden;
}

.card-interactive {
    @apply bg-white rounded-xl shadow-soft border border-secondary-200 transition-all duration-300 hover:shadow-medium hover:border-secondary-300 hover:-translate-y-1 cursor-pointer overflow-hidden;
}

.card-gradient {
    @apply bg-gradient-primary rounded-xl shadow-colored border-0 text-white overflow-hidden;
}

/* Animation utilities */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading spinner */
.spinner {
    animation: spin 1s linear infinite;
    border-radius: 50%;
    border: 2px solid #d1d5db;
    border-top-color: var(--color-primary);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Modern Typography System */
.text-hero {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight;
}

.text-section {
    @apply text-2xl md:text-3xl lg:text-4xl font-bold leading-tight;
}

.text-subsection {
    @apply text-xl md:text-2xl font-semibold leading-snug;
}

.text-gradient {
    @apply bg-gradient-primary bg-clip-text text-transparent;
}

.text-balance {
    text-wrap: balance;
}

/* Modern Layout Utilities */
.container-wide {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.container-narrow {
    @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
}

.section-padding {
    @apply py-16 lg:py-24;
}

.section-padding-sm {
    @apply py-12 lg:py-16;
}