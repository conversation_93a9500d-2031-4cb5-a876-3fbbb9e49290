{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/Sponsors.tsx"], "sourcesContent": ["/* eslint-disable react-dom/no-unsafe-target-blank */\r\nimport Image from 'next/image';\r\n\r\nexport const Sponsors = () => (\r\n  <table className=\"border-collapse\">\r\n    <tbody>\r\n      <tr className=\"h-56\">\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a\r\n            href=\"https://clerk.com?utm_source=github&utm_medium=sponsorship&utm_campaign=nextjs-boilerplate\"\r\n            target=\"_blank\"\r\n            rel=\"noopener\"\r\n          >\r\n            <Image\r\n              src=\"/assets/images/clerk-logo-dark.png\"\r\n              alt=\"Clerk – Authentication & User Management for Next.js\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a href=\"https://www.coderabbit.ai?utm_source=next_js_starter&utm_medium=github&utm_campaign=next_js_starter_oss_2025\" target=\"_blank\" rel=\"noopener\">\r\n            <Image\r\n              src=\"/assets/images/coderabbit-logo-light.svg\"\r\n              alt=\"CodeRabbit\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a\r\n            href=\"https://sentry.io/for/nextjs/?utm_source=github&utm_medium=paid-community&utm_campaign=general-fy25q1-nextjs&utm_content=github-banner-nextjsboilerplate-logo\"\r\n            target=\"_blank\"\r\n            rel=\"noopener\"\r\n          >\r\n            <Image\r\n              src=\"/assets/images/sentry-dark.png\"\r\n              alt=\"Sentry\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n      </tr>\r\n      <tr className=\"h-56\">\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a href=\"https://launch.arcjet.com/Q6eLbRE\">\r\n            <Image\r\n              src=\"/assets/images/arcjet-light.svg\"\r\n              alt=\"Arcjet\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a href=\"https://sevalla.com/\">\r\n            <Image\r\n              src=\"/assets/images/sevalla-light.png\"\r\n              alt=\"Sevalla\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a href=\"https://l.crowdin.com/next-js\" target=\"_blank\" rel=\"noopener\">\r\n            <Image\r\n              src=\"/assets/images/crowdin-dark.png\"\r\n              alt=\"Crowdin\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n      </tr>\r\n      <tr className=\"h-56\">\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a\r\n            href=\"https://posthog.com/?utm_source=github&utm_medium=sponsorship&utm_campaign=next-js-boilerplate\"\r\n            target=\"_blank\"\r\n            rel=\"noopener\"\r\n          >\r\n            <Image\r\n              src=\"https://posthog.com/brand/posthog-logo.svg\"\r\n              alt=\"PostHog\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a\r\n            href=\"https://betterstack.com/?utm_source=github&utm_medium=sponsorship&utm_campaign=next-js-boilerplate\"\r\n            target=\"_blank\"\r\n            rel=\"noopener\"\r\n          >\r\n            <Image\r\n              src=\"/assets/images/better-stack-dark.png\"\r\n              alt=\"Better Stack\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a\r\n            href=\"https://www.checklyhq.com/?utm_source=github&utm_medium=sponsorship&utm_campaign=next-js-boilerplate\"\r\n            target=\"_blank\"\r\n            rel=\"noopener\"\r\n          >\r\n            <Image\r\n              src=\"/assets/images/checkly-logo-light.png\"\r\n              alt=\"Checkly\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n      </tr>\r\n      <tr className=\"h-56\">\r\n        <td className=\"border-2 border-gray-300 p-3\">\r\n          <a href=\"https://nextjs-boilerplate.com/pro-saas-starter-kit\">\r\n            <Image\r\n              src=\"/assets/images/nextjs-boilerplate-saas.png\"\r\n              alt=\"Next.js SaaS Boilerplate\"\r\n              width={260}\r\n              height={224}\r\n            />\r\n          </a>\r\n        </td>\r\n      </tr>\r\n    </tbody>\r\n  </table>\r\n);\r\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;AACnD;;;AAEO,MAAM,WAAW,kBACtB,8OAAC;QAAM,WAAU;kBACf,cAAA,8OAAC;;8BACC,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;0CAEJ,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAE,MAAK;gCAA+G,QAAO;gCAAS,KAAI;0CACzI,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;0CAEJ,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAE,MAAK;0CACN,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAE,MAAK;0CACN,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAE,MAAK;gCAAgC,QAAO;gCAAS,KAAI;0CAC1D,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;0CAEJ,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;0CAEJ,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;0CAEJ,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;;;;;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC;oBAAG,WAAU;8BACZ,cAAA,8OAAC;wBAAG,WAAU;kCACZ,cAAA,8OAAC;4BAAE,MAAK;sCACN,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ", "debugId": null}}]}