!function(){"use strict";function e(e,t,r,n,i,o,a){try{var s=e[o](a),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}function t(t){return function(){var r=this,n=arguments;return new Promise((function(i,o){var a=t.apply(r,n);function s(t){e(a,i,o,s,u,"next",t)}function u(t){e(a,i,o,s,u,"throw",t)}s(void 0)}))}}function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},r.apply(null,arguments)}var n,i=["type"],o=Object.defineProperty,a=(e,t,r)=>((e,t,r)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),s=Object.defineProperty,u=(e,t,r)=>((e,t,r)=>t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),l=(e=>(e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment",e))(l||{}),c={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]},d={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]};var h={};function f(e){if(h[e])return h[e];var t=function(e){var t,r=null==globalThis||null==(t=globalThis.Zone)||null==t.__symbol__?void 0:t.__symbol__(e);return r&&globalThis[r]?globalThis[r]:void 0}(e)||globalThis[e],r=t.prototype,n=e in c?c[e]:void 0,i=Boolean(n&&n.every((e=>{var t,n;return Boolean(null==(n=null==(t=Object.getOwnPropertyDescriptor(r,e))?void 0:t.get)?void 0:n.toString().includes("[native code]"))}))),o=e in d?d[e]:void 0,a=Boolean(o&&o.every((e=>{var t;return"function"==typeof r[e]&&(null==(t=r[e])?void 0:t.toString().includes("[native code]"))})));if(i&&a)return h[e]=t.prototype,t.prototype;try{var s=document.createElement("iframe");document.body.appendChild(s);var u=s.contentWindow;if(!u)return t.prototype;var l=u[e].prototype;return document.body.removeChild(s),l?h[e]=l:t.prototype}catch(e){return t.prototype}}var m={};function v(e,t,r){var n,i=e+"."+String(r);if(m[i])return m[i].call(t);var o=f(e),a=null==(n=Object.getOwnPropertyDescriptor(o,r))?void 0:n.get;return a?(m[i]=a,a.call(t)):t[r]}var p={};function g(e,t,r){var n=e+"."+String(r);if(p[n])return p[n].bind(t);var i=f(e)[r];return"function"!=typeof i?t[r]:(p[n]=i,i.bind(t))}var I={childNodes:function(e){return v("Node",e,"childNodes")},parentNode:function(e){return v("Node",e,"parentNode")},parentElement:function(e){return v("Node",e,"parentElement")},textContent:function(e){return v("Node",e,"textContent")},contains:function(e,t){return g("Node",e,"contains")(t)},getRootNode:function(e){return g("Node",e,"getRootNode")()},host:function(e){return e&&"host"in e?v("ShadowRoot",e,"host"):null},styleSheets:function(e){return e.styleSheets},shadowRoot:function(e){return e&&"shadowRoot"in e?v("Element",e,"shadowRoot"):null},querySelector:function(e,t){return v("Element",e,"querySelector")(t)},querySelectorAll:function(e,t){return v("Element",e,"querySelectorAll")(t)},mutationObserver:function(){return f("MutationObserver").constructor}};function y(e){return e.nodeType===e.ELEMENT_NODE}function C(e){var t=e&&"host"in e&&"mode"in e&&I.host(e)||null;return Boolean(t&&"shadowRoot"in t&&I.shadowRoot(t)===e)}function b(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function w(e){try{var t=e.rules||e.cssRules;if(!t)return null;var r=Array.from(t,(t=>A(t,e.href))).join("");return(n=r).includes(" background-clip: text;")&&!n.includes(" -webkit-background-clip: text;")&&(n=n.replace(/\sbackground-clip:\s*text;/g," -webkit-background-clip: text; background-clip: text;")),n}catch(e){return null}var n}function A(e,t){if(function(e){return"styleSheet"in e}(e)){var r;try{r=w(e.styleSheet)||function(e){var{cssText:t}=e;if(t.split('"').length<3)return t;var r=["@import","url("+JSON.stringify(e.href)+")"];return""===e.layerName?r.push("layer"):e.layerName&&r.push("layer("+e.layerName+")"),e.supportsText&&r.push("supports("+e.supportsText+")"),e.media.length&&r.push(e.media.mediaText),r.join(" ")+";"}(e)}catch(t){r=e.cssText}return e.styleSheet.href?x(r,e.styleSheet.href):r}var n,i=e.cssText;return function(e){return"selectorText"in e}(e)&&e.selectorText.includes(":")&&(n=/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm,i=i.replace(n,"$1\\$2")),t?x(i,t):i}function S(e,t){return Array.from(e.styleSheets).find((e=>e.href===t))}let k=class{constructor(){u(this,"idNodeMap",new Map),u(this,"nodeMetaMap",new WeakMap)}getId(e){var t;if(!e)return-1;var r=null==(t=this.getMeta(e))?void 0:t.id;return null!=r?r:-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){var t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach((e=>this.removeNodeFromMap(e)))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){var r=t.id;this.idNodeMap.set(r,e),this.nodeMetaMap.set(e,t)}replace(e,t){var r=this.getNode(e);if(r){var n=this.nodeMetaMap.get(r);n&&this.nodeMetaMap.set(t,n)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}};function N(e){var{element:t,maskInputOptions:r,tagName:n,type:i,value:o,maskInputFn:a}=e,s=o||"",u=i&&R(i);return(r[n.toLowerCase()]||u&&r[u])&&(s=a?a(s,t):"*".repeat(s.length)),s}function R(e){return e.toLowerCase()}var T="__rrweb_original__";function F(e){var t=e.type;return e.hasAttribute("data-rr-is-password")?"password":t?R(t):null}function M(e,t){var r,n;try{n=new URL(e,null!=t?t:window.location.href)}catch(e){return null}var i=n.pathname.match(/\.([0-9a-z]+)(?:$)/i);return null!==(r=null==i?void 0:i[1])&&void 0!==r?r:null}var B=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,O=/^(?:[a-z+]+:)?\/\//i,Z=/^www\..*/i,Y=/^(data:)([^,]*),(.*)/i;function x(e,t){return(e||"").replace(B,((e,r,n,i,o,a)=>{var s,u=n||o||a,l=r||i||"";if(!u)return e;if(O.test(u)||Z.test(u))return"url("+l+u+l+")";if(Y.test(u))return"url("+l+u+l+")";if("/"===u[0])return"url("+l+(((s=t).indexOf("//")>-1?s.split("/").slice(0,3).join("/"):s.split("/")[0]).split("?")[0]+u)+l+")";var c=t.split("/"),d=u.split("/");for(var h of(c.pop(),d))"."!==h&&(".."===h?c.pop():c.push(h));return"url("+l+c.join("/")+l+")"}))}var G,E,W=1,L=new RegExp("[^a-z0-9-_:]"),X=-2;function V(){return W++}var D=/^[^ \t\n\r\u000c]+/,K=/^[, \t\n\r\u000c]+/;var J=new WeakMap;function z(e,t){return t&&""!==t.trim()?j(e,t):t}function H(e){return Boolean("svg"===e.tagName||e.ownerSVGElement)}function j(e,t){var r=J.get(e);if(r||(r=e.createElement("a"),J.set(e,r)),t){if(t.startsWith("blob:")||t.startsWith("data:"))return t}else t="";return r.setAttribute("href",t),r.href}function _(e,t,r,n){return n?"src"===r||"href"===r&&("use"!==t||"#"!==n[0])||"xlink:href"===r&&"#"!==n[0]?z(e,n):"background"!==r||"table"!==t&&"td"!==t&&"th"!==t?"srcset"===r?function(e,t){if(""===t.trim())return t;var r=0;function n(e){var n,i=e.exec(t.substring(r));return i?(n=i[0],r+=n.length,n):""}for(var i=[];n(K),!(r>=t.length);){var o=n(D);if(","===o.slice(-1))o=z(e,o.substring(0,o.length-1)),i.push(o);else{var a="";o=z(e,o);for(var s=!1;;){var u=t.charAt(r);if(""===u){i.push((o+a).trim());break}if(s)")"===u&&(s=!1);else{if(","===u){r+=1,i.push((o+a).trim());break}"("===u&&(s=!0)}a+=u,r+=1}}}return i.join(", ")}(e,n):"style"===r?x(n,j(e)):"object"===t&&"data"===r?z(e,n):n:z(e,n):n}function U(e,t,r){return("video"===e||"audio"===e)&&"autoplay"===t}function P(e,t,r){if(!e)return!1;if(e.nodeType!==e.ELEMENT_NODE)return!!r&&P(I.parentNode(e),t,r);for(var n=e.classList.length;n--;){var i=e.classList[n];if(t.test(i))return!0}return!!r&&P(I.parentNode(e),t,r)}function q(e,t,r,n){var i;if(y(e)){if(i=e,!I.childNodes(i).length)return!1}else{if(null===I.parentElement(e))return!1;i=I.parentElement(e)}try{if("string"==typeof t){if(n){if(i.closest("."+t))return!0}else if(i.classList.contains(t))return!0}else if(P(i,t,n))return!0;if(r)if(n){if(i.closest(r))return!0}else if(i.matches(r))return!0}catch(e){}return!1}function Q(e,t){var{doc:r,mirror:n,blockClass:i,blockSelector:o,needsMask:a,inlineStylesheet:s,maskInputOptions:u={},maskTextFn:c,maskInputFn:d,dataURLOptions:h={},inlineImages:f,recordCanvas:m,keepIframeSrcFn:v,newlyAddedElement:p=!1}=t,g=function(e,t){if(!t.hasNode(e))return;var r=t.getId(e);return 1===r?void 0:r}(r,n);switch(e.nodeType){case e.DOCUMENT_NODE:return"CSS1Compat"!==e.compatMode?{type:l.Document,childNodes:[],compatMode:e.compatMode}:{type:l.Document,childNodes:[]};case e.DOCUMENT_TYPE_NODE:return{type:l.DocumentType,name:e.name,publicId:e.publicId,systemId:e.systemId,rootId:g};case e.ELEMENT_NODE:return function(e,t){for(var r,{doc:n,blockClass:i,blockSelector:o,inlineStylesheet:a,maskInputOptions:s={},maskInputFn:u,dataURLOptions:c={},inlineImages:d,recordCanvas:h,keepIframeSrcFn:f,newlyAddedElement:m=!1,rootId:v}=t,p=function(e,t,r){try{if("string"==typeof t){if(e.classList.contains(t))return!0}else for(var n=e.classList.length;n--;){var i=e.classList[n];if(t.test(i))return!0}if(r)return e.matches(r)}catch(e){}return!1}(e,i,o),g=function(e){if(e instanceof HTMLFormElement)return"form";var t=R(e.tagName);return L.test(t)?"div":t}(e),y={},C=e.attributes.length,b=0;b<C;b++){var A=e.attributes[b];U(g,A.name,A.value)||(y[A.name]=_(n,g,R(A.name),A.value))}if("link"===g&&a){var k=function(e){return null==e?void 0:e.href}(e);if(k){var M=S(n,k);if(!M&&k.includes(".css"))M=S(n,window.location.origin+"/"+k.replace(window.location.href,""));var B=null;M&&(B=w(M)),B&&(delete y.rel,delete y.href,y._cssText=B)}}if("style"===g&&e.sheet&&!(e.innerText||I.textContent(e)||"").trim().length){var O=w(e.sheet);O&&(y._cssText=O)}if("input"===g||"textarea"===g||"select"===g){var Z=e.value,Y=e.checked;"radio"!==y.type&&"checkbox"!==y.type&&"submit"!==y.type&&"button"!==y.type&&Z?y.value=N({element:e,type:F(e),tagName:g,value:Z,maskInputOptions:s,maskInputFn:u}):Y&&(y.checked=Y)}"option"===g&&(e.selected&&!s.select?y.selected=!0:delete y.selected);if("dialog"===g&&e.open)try{y.rr_open_mode=e.matches("dialog:modal")?"modal":"non-modal"}catch(e){y.rr_open_mode="modal",y.ph_rr_could_not_detect_modal=!0}if("canvas"===g&&h)if("2d"===e.__context)(function(e){var t=e.getContext("2d");if(!t)return!0;for(var r=0;r<e.width;r+=50)for(var n=0;n<e.height;n+=50){var i=t.getImageData,o=T in i?i[T]:i;if(new Uint32Array(o.call(t,r,n,Math.min(50,e.width-r),Math.min(50,e.height-n)).data.buffer).some((e=>0!==e)))return!1}return!0})(e)||(y.rr_dataURL=e.toDataURL(c.type,c.quality));else if(!("__context"in e)){var x=e.toDataURL(c.type,c.quality),W=n.createElement("canvas");W.width=e.width,W.height=e.height,x!==W.toDataURL(c.type,c.quality)&&(y.rr_dataURL=x)}if("img"===g&&d){G||(G=n.createElement("canvas"),E=G.getContext("2d"));var X=e,V=X.currentSrc||X.getAttribute("src")||"<unknown-src>",D=X.crossOrigin,K=()=>{X.removeEventListener("load",K);try{G.width=X.naturalWidth,G.height=X.naturalHeight,E.drawImage(X,0,0),y.rr_dataURL=G.toDataURL(c.type,c.quality)}catch(e){if("anonymous"!==X.crossOrigin)return X.crossOrigin="anonymous",void(X.complete&&0!==X.naturalWidth?K():X.addEventListener("load",K));console.warn("Cannot inline img src="+V+"! Error: "+e)}"anonymous"===X.crossOrigin&&(D?y.crossOrigin=D:X.removeAttribute("crossorigin"))};X.complete&&0!==X.naturalWidth?K():X.addEventListener("load",K)}if("audio"===g||"video"===g){var J=y;J.rr_mediaState=e.paused?"paused":"played",J.rr_mediaCurrentTime=e.currentTime,J.rr_mediaPlaybackRate=e.playbackRate,J.rr_mediaMuted=e.muted,J.rr_mediaLoop=e.loop,J.rr_mediaVolume=e.volume}m||(e.scrollLeft&&(y.rr_scrollLeft=e.scrollLeft),e.scrollTop&&(y.rr_scrollTop=e.scrollTop));if(p){var{width:z,height:j}=e.getBoundingClientRect();y={class:y.class,rr_width:z+"px",rr_height:j+"px"}}"iframe"!==g||f(y.src)||(e.contentDocument||(y.rr_src=y.src),delete y.src);try{customElements.get(g)&&(r=!0)}catch(e){}return{type:l.Element,tagName:g,attributes:y,childNodes:[],isSVG:H(e)||void 0,needBlock:p,rootId:v,isCustom:r}}(e,{doc:r,blockClass:i,blockSelector:o,inlineStylesheet:s,maskInputOptions:u,maskInputFn:d,dataURLOptions:h,inlineImages:f,recordCanvas:m,keepIframeSrcFn:v,newlyAddedElement:p,rootId:g});case e.TEXT_NODE:return function(e,t){var r,{needsMask:n,maskTextFn:i,rootId:o}=t,a=I.parentNode(e),s=a&&a.tagName,u=I.textContent(e),c="STYLE"===s||void 0,d="SCRIPT"===s||void 0;if(c&&u){try{e.nextSibling||e.previousSibling||(null==(r=a.sheet)?void 0:r.cssRules)&&(u=w(a.sheet))}catch(t){console.warn("Cannot get CSS styles from text's parentNode. Error: "+t,e)}u=x(u,j(t.doc))}d&&(u="SCRIPT_PLACEHOLDER");!c&&!d&&u&&n&&(u=i?i(u,I.parentElement(e)):u.replace(/[\S]/g,"*"));return{type:l.Text,textContent:u||"",isStyle:c,rootId:o}}(e,{doc:r,needsMask:a,maskTextFn:c,rootId:g});case e.CDATA_SECTION_NODE:return{type:l.CDATA,textContent:"",rootId:g};case e.COMMENT_NODE:return{type:l.Comment,textContent:I.textContent(e)||"",rootId:g};default:return!1}}function $(e){return null==e?"":e.toLowerCase()}function ee(e,t){var{doc:r,mirror:n,blockClass:i,blockSelector:o,maskTextClass:a,maskTextSelector:s,skipChild:u=!1,inlineStylesheet:c=!0,maskInputOptions:d={},maskTextFn:h,maskInputFn:f,slimDOMOptions:m,dataURLOptions:v={},inlineImages:p=!1,recordCanvas:g=!1,onSerialize:w,onIframeLoad:A,iframeLoadTimeout:S=5e3,onStylesheetLoad:k,stylesheetLoadTimeout:N=5e3,keepIframeSrcFn:R=(()=>!1),newlyAddedElement:T=!1}=t,{needsMask:F}=t,{preserveWhiteSpace:B=!0}=t;F||(F=q(e,a,s,void 0===F));var O,Z=Q(e,{doc:r,mirror:n,blockClass:i,blockSelector:o,needsMask:F,inlineStylesheet:c,maskInputOptions:d,maskTextFn:h,maskInputFn:f,dataURLOptions:v,inlineImages:p,recordCanvas:g,keepIframeSrcFn:R,newlyAddedElement:T});if(!Z)return console.warn(e,"not serialized"),null;O=n.hasNode(e)?n.getId(e):!function(e,t){if(t.comment&&e.type===l.Comment)return!0;if(e.type===l.Element){if(t.script&&("script"===e.tagName||"link"===e.tagName&&("preload"===e.attributes.rel||"modulepreload"===e.attributes.rel)&&"script"===e.attributes.as||"link"===e.tagName&&"prefetch"===e.attributes.rel&&"string"==typeof e.attributes.href&&"js"===M(e.attributes.href)))return!0;if(t.headFavicon&&("link"===e.tagName&&"shortcut icon"===e.attributes.rel||"meta"===e.tagName&&($(e.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===$(e.attributes.name)||"icon"===$(e.attributes.rel)||"apple-touch-icon"===$(e.attributes.rel)||"shortcut icon"===$(e.attributes.rel))))return!0;if("meta"===e.tagName){if(t.headMetaDescKeywords&&$(e.attributes.name).match(/^description|keywords$/))return!0;if(t.headMetaSocial&&($(e.attributes.property).match(/^(og|twitter|fb):/)||$(e.attributes.name).match(/^(og|twitter):/)||"pinterest"===$(e.attributes.name)))return!0;if(t.headMetaRobots&&("robots"===$(e.attributes.name)||"googlebot"===$(e.attributes.name)||"bingbot"===$(e.attributes.name)))return!0;if(t.headMetaHttpEquiv&&void 0!==e.attributes["http-equiv"])return!0;if(t.headMetaAuthorship&&("author"===$(e.attributes.name)||"generator"===$(e.attributes.name)||"framework"===$(e.attributes.name)||"publisher"===$(e.attributes.name)||"progid"===$(e.attributes.name)||$(e.attributes.property).match(/^article:/)||$(e.attributes.property).match(/^product:/)))return!0;if(t.headMetaVerification&&("google-site-verification"===$(e.attributes.name)||"yandex-verification"===$(e.attributes.name)||"csrf-token"===$(e.attributes.name)||"p:domain_verify"===$(e.attributes.name)||"verify-v1"===$(e.attributes.name)||"verification"===$(e.attributes.name)||"shopify-checkout-api-token"===$(e.attributes.name)))return!0}}return!1}(Z,m)&&(B||Z.type!==l.Text||Z.isStyle||Z.textContent.replace(/^\s+|\s+$/gm,"").length)?V():X;var Y=Object.assign(Z,{id:O});if(n.add(e,Y),O===X)return null;w&&w(e);var x=!u;if(Y.type===l.Element){x=x&&!Y.needBlock,delete Y.needBlock;var G=I.shadowRoot(e);G&&b(G)&&(Y.isShadowHost=!0)}if((Y.type===l.Document||Y.type===l.Element)&&x){m.headWhitespace&&Y.type===l.Element&&"head"===Y.tagName&&(B=!1);var E={doc:r,mirror:n,blockClass:i,blockSelector:o,needsMask:F,maskTextClass:a,maskTextSelector:s,skipChild:u,inlineStylesheet:c,maskInputOptions:d,maskTextFn:h,maskInputFn:f,slimDOMOptions:m,dataURLOptions:v,inlineImages:p,recordCanvas:g,preserveWhiteSpace:B,onSerialize:w,onIframeLoad:A,iframeLoadTimeout:S,onStylesheetLoad:k,stylesheetLoadTimeout:N,keepIframeSrcFn:R};if(Y.type===l.Element&&"textarea"===Y.tagName&&void 0!==Y.attributes.value);else for(var W of Array.from(I.childNodes(e))){var L=ee(W,E);L&&Y.childNodes.push(L)}var D=null;if(y(e)&&(D=I.shadowRoot(e)))for(var K of Array.from(I.childNodes(D))){var J=ee(K,E);J&&(b(D)&&(J.isShadow=!0),Y.childNodes.push(J))}}var z=I.parentNode(e);return z&&C(z)&&b(z)&&(Y.isShadow=!0),Y.type===l.Element&&"iframe"===Y.tagName&&function(e,t,r){var n=e.contentWindow;if(n){var i,o=!1;try{i=n.document.readyState}catch(e){return}if("complete"===i){var a="about:blank";if(n.location.href!==a||e.src===a||""===e.src)return setTimeout(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}else{var s=setTimeout((()=>{o||(t(),o=!0)}),r);e.addEventListener("load",(()=>{clearTimeout(s),o=!0,t()}))}}}(e,(()=>{var t=e.contentDocument;if(t&&A){var r=ee(t,{doc:t,mirror:n,blockClass:i,blockSelector:o,needsMask:F,maskTextClass:a,maskTextSelector:s,skipChild:!1,inlineStylesheet:c,maskInputOptions:d,maskTextFn:h,maskInputFn:f,slimDOMOptions:m,dataURLOptions:v,inlineImages:p,recordCanvas:g,preserveWhiteSpace:B,onSerialize:w,onIframeLoad:A,iframeLoadTimeout:S,onStylesheetLoad:k,stylesheetLoadTimeout:N,keepIframeSrcFn:R});r&&A(e,r)}}),S),Y.type===l.Element&&"link"===Y.tagName&&"string"==typeof Y.attributes.rel&&("stylesheet"===Y.attributes.rel||"preload"===Y.attributes.rel&&"string"==typeof Y.attributes.href&&"css"===M(Y.attributes.href))&&function(e,t,r){var n,i=!1;try{n=e.sheet}catch(e){return}if(!n){var o=setTimeout((()=>{i||(t(),i=!0)}),r);e.addEventListener("load",(()=>{clearTimeout(o),i=!0,t()}))}}(e,(()=>{if(k){var t=ee(e,{doc:r,mirror:n,blockClass:i,blockSelector:o,needsMask:F,maskTextClass:a,maskTextSelector:s,skipChild:!1,inlineStylesheet:c,maskInputOptions:d,maskTextFn:h,maskInputFn:f,slimDOMOptions:m,dataURLOptions:v,inlineImages:p,recordCanvas:g,preserveWhiteSpace:B,onSerialize:w,onIframeLoad:A,iframeLoadTimeout:S,onStylesheetLoad:k,stylesheetLoadTimeout:N,keepIframeSrcFn:R});t&&k(e,t)}}),N),Y}let te=class e{constructor(){__publicField2(this,"parentElement",null),__publicField2(this,"parentNode",null),__publicField2(this,"ownerDocument"),__publicField2(this,"firstChild",null),__publicField2(this,"lastChild",null),__publicField2(this,"previousSibling",null),__publicField2(this,"nextSibling",null),__publicField2(this,"ELEMENT_NODE",1),__publicField2(this,"TEXT_NODE",3),__publicField2(this,"nodeType"),__publicField2(this,"nodeName"),__publicField2(this,"RRNodeType")}get childNodes(){for(var e=[],t=this.firstChild;t;)e.push(t),t=t.nextSibling;return e}contains(t){if(!(t instanceof e))return!1;if(t.ownerDocument!==this.ownerDocument)return!1;if(t===this)return!0;for(;t.parentNode;){if(t.parentNode===this)return!0;t=t.parentNode}return!1}appendChild(e){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")}insertBefore(e,t){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")}removeChild(e){throw new Error("RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.")}toString(){return"RRNode"}};var re={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]},ne={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]};var ie={};function oe(e){if(ie[e])return ie[e];var t=function(e){var t,r=null==globalThis||null==(t=globalThis.Zone)||null==t.__symbol__?void 0:t.__symbol__(e);return r&&globalThis[r]?globalThis[r]:void 0}(e)||globalThis[e],r=t.prototype,n=e in re?re[e]:void 0,i=Boolean(n&&n.every((e=>{var t,n;return Boolean(null==(n=null==(t=Object.getOwnPropertyDescriptor(r,e))?void 0:t.get)?void 0:n.toString().includes("[native code]"))}))),o=e in ne?ne[e]:void 0,a=Boolean(o&&o.every((e=>{var t;return"function"==typeof r[e]&&(null==(t=r[e])?void 0:t.toString().includes("[native code]"))})));if(i&&a)return ie[e]=t.prototype,t.prototype;try{var s=document.createElement("iframe");document.body.appendChild(s);var u=s.contentWindow;if(!u)return t.prototype;var l=u[e].prototype;return document.body.removeChild(s),l?ie[e]=l:r}catch(e){return r}}var ae={};function se(e,t,r){var n,i=e+"."+String(r);if(ae[i])return ae[i].call(t);var o=oe(e),a=null==(n=Object.getOwnPropertyDescriptor(o,r))?void 0:n.get;return a?(ae[i]=a,a.call(t)):t[r]}var ue={};function le(e,t,r){var n=e+"."+String(r);if(ue[n])return ue[n].bind(t);var i=oe(e)[r];return"function"!=typeof i?t[r]:(ue[n]=i,i.bind(t))}function ce(){return oe("MutationObserver").constructor}var de={childNodes:function(e){return se("Node",e,"childNodes")},parentNode:function(e){return se("Node",e,"parentNode")},parentElement:function(e){return se("Node",e,"parentElement")},textContent:function(e){return se("Node",e,"textContent")},contains:function(e,t){return le("Node",e,"contains")(t)},getRootNode:function(e){return le("Node",e,"getRootNode")()},host:function(e){return e&&"host"in e?se("ShadowRoot",e,"host"):null},styleSheets:function(e){return e.styleSheets},shadowRoot:function(e){return e&&"shadowRoot"in e?se("Element",e,"shadowRoot"):null},querySelector:function(e,t){return se("Element",e,"querySelector")(t)},querySelectorAll:function(e,t){return se("Element",e,"querySelectorAll")(t)},mutationObserver:ce};function he(e,t,r){void 0===r&&(r=document);var n={capture:!0,passive:!0};return r.addEventListener(e,t,n),()=>r.removeEventListener(e,t,n)}var fe="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",me={map:{},getId:()=>(console.error(fe),-1),getNode:()=>(console.error(fe),null),removeNodeFromMap(){console.error(fe)},has:()=>(console.error(fe),!1),reset(){console.error(fe)}};function ve(e,t,r){void 0===r&&(r={});var n=null,i=0;return function(){for(var o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];var u=Date.now();i||!1!==r.leading||(i=u);var l=t-(u-i),c=this;l<=0||l>t?(n&&(clearTimeout(n),n=null),i=u,e.apply(c,a)):n||!1===r.trailing||(n=setTimeout((()=>{i=!1===r.leading?0:Date.now(),n=null,e.apply(c,a)}),l))}}function pe(e,t,r,n,i){void 0===i&&(i=window);var o=i.Object.getOwnPropertyDescriptor(e,t);return i.Object.defineProperty(e,t,n?r:{set(e){setTimeout((()=>{r.set.call(this,e)}),0),o&&o.set&&o.set.call(this,e)}}),()=>pe(e,t,o||{},!0)}function ge(e,t,r){try{if(!(t in e))return()=>{};var n=e[t],i=r(n);return"function"==typeof i&&(i.prototype=i.prototype||{},Object.defineProperties(i,{__rrweb_original__:{enumerable:!1,value:n}})),e[t]=i,()=>{e[t]=n}}catch(e){return()=>{}}}"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(me=new Proxy(me,{get:(e,t,r)=>("map"===t&&console.error(fe),Reflect.get(e,t,r))}));var Ie=Date.now;function ye(e){var t,r,n,i,o=e.document;return{left:o.scrollingElement?o.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:o.documentElement.scrollLeft||(null==o?void 0:o.body)&&(null==(t=de.parentElement(o.body))?void 0:t.scrollLeft)||(null==(r=null==o?void 0:o.body)?void 0:r.scrollLeft)||0,top:o.scrollingElement?o.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:(null==o?void 0:o.documentElement.scrollTop)||(null==o?void 0:o.body)&&(null==(n=de.parentElement(o.body))?void 0:n.scrollTop)||(null==(i=null==o?void 0:o.body)?void 0:i.scrollTop)||0}}function Ce(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function be(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function we(e){return e?e.nodeType===e.ELEMENT_NODE?e:de.parentElement(e):null}function Ae(e,t,r,n){if(!e)return!1;var i=we(e);if(!i)return!1;try{if("string"==typeof t){if(i.classList.contains(t))return!0;if(n&&null!==i.closest("."+t))return!0}else if(P(i,t,n))return!0}catch(e){}if(r){if(i.matches(r))return!0;if(n&&null!==i.closest(r))return!0}return!1}function Se(e,t,r){return!("TITLE"!==e.tagName||!r.headTitleMutations)||t.getId(e)===X}function ke(e,t){if(C(e))return!1;var r=t.getId(e);if(!t.has(r))return!0;var n=de.parentNode(e);return(!n||n.nodeType!==e.DOCUMENT_NODE)&&(!n||ke(n,t))}function Ne(e){return Boolean(e.changedTouches)}function Re(e,t){return Boolean("IFRAME"===e.nodeName&&t.getMeta(e))}function Te(e,t){return Boolean("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))}function Fe(e){return!!e&&(e instanceof te&&"shadowRoot"in e?Boolean(e.shadowRoot):Boolean(de.shadowRoot(e)))}/[1-9][0-9]{12}/.test(Date.now().toString())||(Ie=()=>(new Date).getTime());let Me=class{constructor(){a(this,"id",1),a(this,"styleIDMap",new WeakMap),a(this,"idStyleMap",new Map)}getId(e){var t;return null!==(t=this.styleIDMap.get(e))&&void 0!==t?t:-1}has(e){return this.styleIDMap.has(e)}add(e,t){return this.has(e)?this.getId(e):(r=void 0===t?this.id++:t,this.styleIDMap.set(e,r),this.idStyleMap.set(r,e),r);var r}getStyle(e){return this.idStyleMap.get(e)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}};function Be(e){var t,r=null;return"getRootNode"in e&&(null==(t=de.getRootNode(e))?void 0:t.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&de.host(de.getRootNode(e))&&(r=de.host(de.getRootNode(e))),r}function Oe(e){var t=e.ownerDocument;if(!t)return!1;var r=function(e){for(var t,r=e;t=Be(r);)r=t;return r}(e);return de.contains(t,r)}function Ze(e){var t=e.ownerDocument;return!!t&&(de.contains(t,e)||Oe(e))}var Ye=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(Ye||{}),xe=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(xe||{}),Ge=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))(Ge||{}),Ee=(e=>(e[e.Mouse=0]="Mouse",e[e.Pen=1]="Pen",e[e.Touch=2]="Touch",e))(Ee||{}),We=(e=>(e[e["2D"]=0]="2D",e[e.WebGL=1]="WebGL",e[e.WebGL2=2]="WebGL2",e))(We||{}),Le=(e=>(e[e.Play=0]="Play",e[e.Pause=1]="Pause",e[e.Seeked=2]="Seeked",e[e.VolumeChange=3]="VolumeChange",e[e.RateChange=4]="RateChange",e))(Le||{});function Xe(e){return"__ln"in e}class Ve{constructor(){a(this,"length",0),a(this,"head",null),a(this,"tail",null)}get(e){if(e>=this.length)throw new Error("Position outside of list range");for(var t=this.head,r=0;r<e;r++)t=(null==t?void 0:t.next)||null;return t}addNode(e){var t={value:e,previous:null,next:null};if(e.__ln=t,e.previousSibling&&Xe(e.previousSibling)){var r=e.previousSibling.__ln.next;t.next=r,t.previous=e.previousSibling.__ln,e.previousSibling.__ln.next=t,r&&(r.previous=t)}else if(e.nextSibling&&Xe(e.nextSibling)&&e.nextSibling.__ln.previous){var n=e.nextSibling.__ln.previous;t.previous=n,t.next=e.nextSibling.__ln,e.nextSibling.__ln.previous=t,n&&(n.next=t)}else this.head&&(this.head.previous=t),t.next=this.head,this.head=t;null===t.next&&(this.tail=t),this.length++}removeNode(e){var t=e.__ln;this.head&&(t.previous?(t.previous.next=t.next,t.next?t.next.previous=t.previous:this.tail=t.previous):(this.head=t.next,this.head?this.head.previous=null:this.tail=null),e.__ln&&delete e.__ln,this.length--)}}var De,Ke=(e,t)=>e+"@"+t;class Je{constructor(){a(this,"frozen",!1),a(this,"locked",!1),a(this,"texts",[]),a(this,"attributes",[]),a(this,"attributeMap",new WeakMap),a(this,"removes",[]),a(this,"mapRemoves",[]),a(this,"movedMap",{}),a(this,"addedSet",new Set),a(this,"movedSet",new Set),a(this,"droppedSet",new Set),a(this,"mutationCb"),a(this,"blockClass"),a(this,"blockSelector"),a(this,"maskTextClass"),a(this,"maskTextSelector"),a(this,"inlineStylesheet"),a(this,"maskInputOptions"),a(this,"maskTextFn"),a(this,"maskInputFn"),a(this,"keepIframeSrcFn"),a(this,"recordCanvas"),a(this,"inlineImages"),a(this,"slimDOMOptions"),a(this,"dataURLOptions"),a(this,"doc"),a(this,"mirror"),a(this,"iframeManager"),a(this,"stylesheetManager"),a(this,"shadowDomManager"),a(this,"canvasManager"),a(this,"processedNodeManager"),a(this,"unattachedDoc"),a(this,"processMutations",(e=>{e.forEach(this.processMutation),this.emit()})),a(this,"emit",(()=>{if(!this.frozen&&!this.locked){for(var e=[],t=new Set,r=new Ve,n=e=>{for(var t=e,r=X;r===X;)r=(t=t&&t.nextSibling)&&this.mirror.getId(t);return r},i=i=>{var o=de.parentNode(i);if(o&&Ze(i)&&"TEXTAREA"!==o.tagName){var a=C(o)?this.mirror.getId(Be(i)):this.mirror.getId(o),s=n(i);if(-1===a||-1===s)return r.addNode(i);var u=ee(i,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskTextClass:this.maskTextClass,maskTextSelector:this.maskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:e=>{Re(e,this.mirror)&&this.iframeManager.addIframe(e),Te(e,this.mirror)&&this.stylesheetManager.trackLinkElement(e),Fe(i)&&this.shadowDomManager.addShadowRoot(de.shadowRoot(i),this.doc)},onIframeLoad:(e,t)=>{this.iframeManager.attachIframe(e,t),this.shadowDomManager.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{this.stylesheetManager.attachLinkElement(e,t)}});u&&(e.push({parentId:a,nextId:s,node:u}),t.add(u.id))}};this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(var o of this.movedSet)He(this.removes,o,this.mirror)&&!this.movedSet.has(de.parentNode(o))||i(o);for(var a of this.addedSet)je(this.droppedSet,a)||He(this.removes,a,this.mirror)?je(this.movedSet,a)?i(a):this.droppedSet.add(a):i(a);for(var s=null;r.length;){var u=null;if(s){var l=this.mirror.getId(de.parentNode(s.value)),c=n(s.value);-1!==l&&-1!==c&&(u=s)}if(!u)for(var d=r.tail;d;){var h=d;if(d=d.previous,h){var f=this.mirror.getId(de.parentNode(h.value));if(-1===n(h.value))continue;if(-1!==f){u=h;break}var m=h.value,v=de.parentNode(m);if(v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE){var p=de.host(v);if(-1!==this.mirror.getId(p)){u=h;break}}}}if(!u){for(;r.head;)r.removeNode(r.head.value);break}s=u.previous,r.removeNode(u.value),i(u.value)}var g={texts:this.texts.map((e=>{var t=e.node,r=de.parentNode(t);return r&&"TEXTAREA"===r.tagName&&this.genTextAreaValueMutation(r),{id:this.mirror.getId(t),value:e.value}})).filter((e=>!t.has(e.id))).filter((e=>this.mirror.has(e.id))),attributes:this.attributes.map((e=>{var{attributes:t}=e;if("string"==typeof t.style){var r=JSON.stringify(e.styleDiff),n=JSON.stringify(e._unchangedStyles);r.length<t.style.length&&(r+n).split("var(").length===t.style.split("var(").length&&(t.style=e.styleDiff)}return{id:this.mirror.getId(e.node),attributes:t}})).filter((e=>!t.has(e.id))).filter((e=>this.mirror.has(e.id))),removes:this.removes,adds:e};(g.texts.length||g.attributes.length||g.removes.length||g.adds.length)&&(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(g))}})),a(this,"genTextAreaValueMutation",(e=>{var t=this.attributeMap.get(e);t||(t={node:e,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(t),this.attributeMap.set(e,t)),t.attributes.value=Array.from(de.childNodes(e),(e=>de.textContent(e)||"")).join("")})),a(this,"processMutation",(e=>{if(!Se(e.target,this.mirror,this.slimDOMOptions))switch(e.type){case"characterData":var t=de.textContent(e.target);Ae(e.target,this.blockClass,this.blockSelector,!1)||t===e.oldValue||this.texts.push({value:q(e.target,this.maskTextClass,this.maskTextSelector,!0)&&t?this.maskTextFn?this.maskTextFn(t,we(e.target)):t.replace(/[\S]/g,"*"):t,node:e.target});break;case"attributes":var r=e.target,n=e.attributeName,i=e.target.getAttribute(n);if("value"===n){var o=F(r);i=N({element:r,maskInputOptions:this.maskInputOptions,tagName:r.tagName,type:o,value:i,maskInputFn:this.maskInputFn})}if(Ae(e.target,this.blockClass,this.blockSelector,!1)||i===e.oldValue)return;var a=this.attributeMap.get(e.target);if("IFRAME"===r.tagName&&"src"===n&&!this.keepIframeSrcFn(i)){if(r.contentDocument)return;n="rr_src"}if(a||(a={node:e.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(a),this.attributeMap.set(e.target,a)),"type"===n&&"INPUT"===r.tagName&&"password"===(e.oldValue||"").toLowerCase()&&r.setAttribute("data-rr-is-password","true"),!U(r.tagName,n))if(a.attributes[n]=_(this.doc,R(r.tagName),R(n),i),"style"===n){if(!this.unattachedDoc)try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch(e){this.unattachedDoc=this.doc}var s=this.unattachedDoc.createElement("span");for(var u of(e.oldValue&&s.setAttribute("style",e.oldValue),Array.from(r.style))){var l=r.style.getPropertyValue(u),c=r.style.getPropertyPriority(u);l!==s.style.getPropertyValue(u)||c!==s.style.getPropertyPriority(u)?a.styleDiff[u]=""===c?l:[l,c]:a._unchangedStyles[u]=[l,c]}for(var d of Array.from(s.style))""===r.style.getPropertyValue(d)&&(a.styleDiff[d]=!1)}else"open"===n&&"DIALOG"===r.tagName&&(r.matches("dialog:modal")?a.attributes.rr_open_mode="modal":a.attributes.rr_open_mode="non-modal");break;case"childList":if(Ae(e.target,this.blockClass,this.blockSelector,!0))return;if("TEXTAREA"===e.target.tagName)return void this.genTextAreaValueMutation(e.target);e.addedNodes.forEach((t=>this.genAdds(t,e.target))),e.removedNodes.forEach((t=>{var r=this.mirror.getId(t),n=C(e.target)?this.mirror.getId(de.host(e.target)):this.mirror.getId(e.target);Ae(e.target,this.blockClass,this.blockSelector,!1)||Se(t,this.mirror,this.slimDOMOptions)||!function(e,t){return-1!==t.getId(e)}(t,this.mirror)||(this.addedSet.has(t)?(ze(this.addedSet,t),this.droppedSet.add(t)):this.addedSet.has(e.target)&&-1===r||ke(e.target,this.mirror)||(this.movedSet.has(t)&&this.movedMap[Ke(r,n)]?ze(this.movedSet,t):this.removes.push({parentId:n,id:r,isShadow:!(!C(e.target)||!b(e.target))||void 0})),this.mapRemoves.push(t))}))}})),a(this,"genAdds",((e,t)=>{if(!this.processedNodeManager.inOtherBuffer(e,this)&&!this.addedSet.has(e)&&!this.movedSet.has(e)){if(this.mirror.hasNode(e)){if(Se(e,this.mirror,this.slimDOMOptions))return;this.movedSet.add(e);var r=null;t&&this.mirror.hasNode(t)&&(r=this.mirror.getId(t)),r&&-1!==r&&(this.movedMap[Ke(this.mirror.getId(e),r)]=!0)}else this.addedSet.add(e),this.droppedSet.delete(e);Ae(e,this.blockClass,this.blockSelector,!1)||(de.childNodes(e).forEach((e=>this.genAdds(e))),Fe(e)&&de.childNodes(de.shadowRoot(e)).forEach((t=>{this.processedNodeManager.add(t,this),this.genAdds(t,e)})))}}))}init(e){["mutationCb","blockClass","blockSelector","maskTextClass","maskTextSelector","inlineStylesheet","maskInputOptions","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach((t=>{this[t]=e[t]}))}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function ze(e,t){e.delete(t),de.childNodes(t).forEach((t=>ze(e,t)))}function He(e,t,r){return 0!==e.length&&function(e,t,r){var n,i=de.parentNode(t),o=function(){var t=r.getId(i);if(e.some((e=>e.id===t)))return{v:!0};i=de.parentNode(i)};for(;i;)if(n=o())return n.v;return!1}(e,t,r)}function je(e,t){return 0!==e.size&&_e(e,t)}function _e(e,t){var r=de.parentNode(t);return!!r&&(!!e.has(r)||_e(e,r))}var Ue=e=>{if(!De)return e;return function(){try{return e(...arguments)}catch(e){if(De&&!0===De(e))return;throw e}}},Pe=[];function qe(e){try{if("composedPath"in e){var t=e.composedPath();if(t.length)return t[0]}else if("path"in e&&e.path.length)return e.path[0]}catch(e){}return e&&e.target}function Qe(e,t){var r=new Je;Pe.push(r),r.init(e);var n=new(ce())(Ue(r.processMutations.bind(r)));return n.observe(t,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),n}function $e(e){var{mouseInteractionCb:t,doc:n,mirror:i,blockClass:o,blockSelector:a,sampling:s}=e;if(!1===s.mouseInteraction)return()=>{};var u=!0===s.mouseInteraction||void 0===s.mouseInteraction?{}:s.mouseInteraction,l=[],c=null;return Object.keys(Ge).filter((e=>Number.isNaN(Number(e))&&!e.endsWith("_Departed")&&!1!==u[e])).forEach((e=>{var s=R(e),u=(e=>n=>{var s=qe(n);if(!Ae(s,o,a,!0)){var u=null,l=e;if("pointerType"in n){switch(n.pointerType){case"mouse":u=Ee.Mouse;break;case"touch":u=Ee.Touch;break;case"pen":u=Ee.Pen}u===Ee.Touch?Ge[e]===Ge.MouseDown?l="TouchStart":Ge[e]===Ge.MouseUp&&(l="TouchEnd"):Ee.Pen}else Ne(n)&&(u=Ee.Touch);null!==u?(c=u,(l.startsWith("Touch")&&u===Ee.Touch||l.startsWith("Mouse")&&u===Ee.Mouse)&&(u=null)):Ge[e]===Ge.Click&&(u=c,c=null);var d=Ne(n)?n.changedTouches[0]:n;if(d){var h=i.getId(s),{clientX:f,clientY:m}=d;Ue(t)(r({type:Ge[l],id:h,x:f,y:m},null!==u&&{pointerType:u}))}}})(e);if(window.PointerEvent)switch(Ge[e]){case Ge.MouseDown:case Ge.MouseUp:s=s.replace("mouse","pointer");break;case Ge.TouchStart:case Ge.TouchEnd:return}l.push(he(s,u,n))})),Ue((()=>{l.forEach((e=>e()))}))}function et(e){var{scrollCb:t,doc:r,mirror:n,blockClass:i,blockSelector:o,sampling:a}=e;return he("scroll",Ue(ve(Ue((e=>{var a=qe(e);if(a&&!Ae(a,i,o,!0)){var s=n.getId(a);if(a===r&&r.defaultView){var u=ye(r.defaultView);t({id:s,x:u.left,y:u.top})}else t({id:s,x:a.scrollLeft,y:a.scrollTop})}})),a.scroll||100)),r)}var tt=["INPUT","TEXTAREA","SELECT"],rt=new WeakMap;function nt(e){return function(e,t){if(st("CSSGroupingRule")&&e.parentRule instanceof CSSGroupingRule||st("CSSMediaRule")&&e.parentRule instanceof CSSMediaRule||st("CSSSupportsRule")&&e.parentRule instanceof CSSSupportsRule||st("CSSConditionRule")&&e.parentRule instanceof CSSConditionRule){var r=Array.from(e.parentRule.cssRules).indexOf(e);t.unshift(r)}else if(e.parentStyleSheet){var n=Array.from(e.parentStyleSheet.cssRules).indexOf(e);t.unshift(n)}return t}(e,[])}function it(e,t,r){var n,i;return e?(e.ownerNode?n=t.getId(e.ownerNode):i=r.getId(e),{styleId:i,id:n}):{}}function ot(e,t){var r,n,i,{mirror:o,stylesheetManager:a}=e,s=null;s="#document"===t.nodeName?o.getId(t):o.getId(de.host(t));var u="#document"===t.nodeName?null==(r=t.defaultView)?void 0:r.Document:null==(i=null==(n=t.ownerDocument)?void 0:n.defaultView)?void 0:i.ShadowRoot,l=(null==u?void 0:u.prototype)?Object.getOwnPropertyDescriptor(null==u?void 0:u.prototype,"adoptedStyleSheets"):void 0;return null!==s&&-1!==s&&u&&l?(Object.defineProperty(t,"adoptedStyleSheets",{configurable:l.configurable,enumerable:l.enumerable,get(){var e;return null==(e=l.get)?void 0:e.call(this)},set(e){var t,r=null==(t=l.set)?void 0:t.call(this,e);if(null!==s&&-1!==s)try{a.adoptStyleSheets(e,s)}catch(e){}return r}}),Ue((()=>{Object.defineProperty(t,"adoptedStyleSheets",{configurable:l.configurable,enumerable:l.enumerable,get:l.get,set:l.set})}))):()=>{}}function at(e,t){void 0===t&&(t={});var n,i=e.doc.defaultView;if(!i)return()=>{};!function(e,t){var{mutationCb:r,mousemoveCb:n,mouseInteractionCb:i,scrollCb:o,viewportResizeCb:a,inputCb:s,mediaInteractionCb:u,styleSheetRuleCb:l,styleDeclarationCb:c,canvasMutationCb:d,fontCb:h,selectionCb:f,customElementCb:m}=e;e.mutationCb=function(){t.mutation&&t.mutation(...arguments),r(...arguments)},e.mousemoveCb=function(){t.mousemove&&t.mousemove(...arguments),n(...arguments)},e.mouseInteractionCb=function(){t.mouseInteraction&&t.mouseInteraction(...arguments),i(...arguments)},e.scrollCb=function(){t.scroll&&t.scroll(...arguments),o(...arguments)},e.viewportResizeCb=function(){t.viewportResize&&t.viewportResize(...arguments),a(...arguments)},e.inputCb=function(){t.input&&t.input(...arguments),s(...arguments)},e.mediaInteractionCb=function(){t.mediaInteaction&&t.mediaInteaction(...arguments),u(...arguments)},e.styleSheetRuleCb=function(){t.styleSheetRule&&t.styleSheetRule(...arguments),l(...arguments)},e.styleDeclarationCb=function(){t.styleDeclaration&&t.styleDeclaration(...arguments),c(...arguments)},e.canvasMutationCb=function(){t.canvasMutation&&t.canvasMutation(...arguments),d(...arguments)},e.fontCb=function(){t.font&&t.font(...arguments),h(...arguments)},e.selectionCb=function(){t.selection&&t.selection(...arguments),f(...arguments)},e.customElementCb=function(){t.customElement&&t.customElement(...arguments),m(...arguments)}}(e,t),e.recordDOM&&(n=Qe(e,e.doc));var o=function(e){var{mousemoveCb:t,sampling:r,doc:n,mirror:i}=e;if(!1===r.mousemove)return()=>{};var o,a="number"==typeof r.mousemove?r.mousemove:50,s="number"==typeof r.mousemoveCallback?r.mousemoveCallback:500,u=[],l=ve(Ue((e=>{var r=Date.now()-o;t(u.map((e=>(e.timeOffset-=r,e))),e),u=[],o=null})),s),c=Ue(ve(Ue((e=>{var t=qe(e),{clientX:r,clientY:n}=Ne(e)?e.changedTouches[0]:e;o||(o=Ie()),u.push({x:r,y:n,id:i.getId(t),timeOffset:Ie()-o}),l("undefined"!=typeof DragEvent&&e instanceof DragEvent?xe.Drag:e instanceof MouseEvent?xe.MouseMove:xe.TouchMove)})),a,{trailing:!1})),d=[he("mousemove",c,n),he("touchmove",c,n),he("drag",c,n)];return Ue((()=>{d.forEach((e=>e()))}))}(e),a=$e(e),s=et(e),u=function(e,t){var{viewportResizeCb:r}=e,{win:n}=t,i=-1,o=-1;return he("resize",Ue(ve(Ue((()=>{var e=Ce(),t=be();i===e&&o===t||(r({width:Number(t),height:Number(e)}),i=e,o=t)})),200)),n)}(e,{win:i}),l=function(e){var{inputCb:t,doc:n,mirror:i,blockClass:o,blockSelector:a,ignoreClass:s,ignoreSelector:u,maskInputOptions:l,maskInputFn:c,sampling:d,userTriggeredOnInput:h}=e;function f(e){var t=qe(e),r=e.isTrusted,i=t&&t.tagName;if(t&&"OPTION"===i&&(t=de.parentElement(t)),t&&i&&!(tt.indexOf(i)<0)&&!Ae(t,o,a,!0)&&!(t.classList.contains(s)||u&&t.matches(u))){var d=t.value,f=!1,v=F(t)||"";"radio"===v||"checkbox"===v?f=t.checked:(l[i.toLowerCase()]||l[v])&&(d=N({element:t,maskInputOptions:l,tagName:i,type:v,value:d,maskInputFn:c})),m(t,h?{text:d,isChecked:f,userTriggered:r}:{text:d,isChecked:f});var p=t.name;"radio"===v&&p&&f&&n.querySelectorAll('input[type="radio"][name="'+p+'"]').forEach((e=>{if(e!==t){var r=e.value;m(e,h?{text:r,isChecked:!f,userTriggered:!1}:{text:r,isChecked:!f})}}))}}function m(e,n){var o=rt.get(e);if(!o||o.text!==n.text||o.isChecked!==n.isChecked){rt.set(e,n);var a=i.getId(e);Ue(t)(r({},n,{id:a}))}}var v=("last"===d.input?["change"]:["input","change"]).map((e=>he(e,Ue(f),n))),p=n.defaultView;if(!p)return()=>{v.forEach((e=>e()))};var g=p.Object.getOwnPropertyDescriptor(p.HTMLInputElement.prototype,"value"),I=[[p.HTMLInputElement.prototype,"value"],[p.HTMLInputElement.prototype,"checked"],[p.HTMLSelectElement.prototype,"value"],[p.HTMLTextAreaElement.prototype,"value"],[p.HTMLSelectElement.prototype,"selectedIndex"],[p.HTMLOptionElement.prototype,"selected"]];return g&&g.set&&v.push(...I.map((e=>pe(e[0],e[1],{set(){Ue(f)({target:this,isTrusted:!1})}},!1,p)))),Ue((()=>{v.forEach((e=>e()))}))}(e),c=function(e){var{mediaInteractionCb:t,blockClass:r,blockSelector:n,mirror:i,sampling:o,doc:a}=e,s=Ue((e=>ve(Ue((o=>{var a=qe(o);if(a&&!Ae(a,r,n,!0)){var{currentTime:s,volume:u,muted:l,playbackRate:c,loop:d}=a;t({type:e,id:i.getId(a),currentTime:s,volume:u,muted:l,playbackRate:c,loop:d})}})),o.media||500))),u=[he("play",s(Le.Play),a),he("pause",s(Le.Pause),a),he("seeked",s(Le.Seeked),a),he("volumechange",s(Le.VolumeChange),a),he("ratechange",s(Le.RateChange),a)];return Ue((()=>{u.forEach((e=>e()))}))}(e),d=()=>{},h=()=>{},f=()=>{},m=()=>{};e.recordDOM&&(d=function(e,t){var{styleSheetRuleCb:r,mirror:n,stylesheetManager:i}=e,{win:o}=t;if(!o.CSSStyleSheet||!o.CSSStyleSheet.prototype)return()=>{};var a=o.CSSStyleSheet.prototype.insertRule;o.CSSStyleSheet.prototype.insertRule=new Proxy(a,{apply:Ue(((e,t,o)=>{var[a,s]=o,{id:u,styleId:l}=it(t,n,i.styleMirror);return(u&&-1!==u||l&&-1!==l)&&r({id:u,styleId:l,adds:[{rule:a,index:s}]}),e.apply(t,o)}))}),o.CSSStyleSheet.prototype.addRule=function(e,t,r){void 0===r&&(r=this.cssRules.length);var n=e+" { "+t+" }";return o.CSSStyleSheet.prototype.insertRule.apply(this,[n,r])};var s,u,l=o.CSSStyleSheet.prototype.deleteRule;o.CSSStyleSheet.prototype.deleteRule=new Proxy(l,{apply:Ue(((e,t,o)=>{var[a]=o,{id:s,styleId:u}=it(t,n,i.styleMirror);return(s&&-1!==s||u&&-1!==u)&&r({id:s,styleId:u,removes:[{index:a}]}),e.apply(t,o)}))}),o.CSSStyleSheet.prototype.removeRule=function(e){return o.CSSStyleSheet.prototype.deleteRule.apply(this,[e])},o.CSSStyleSheet.prototype.replace&&(s=o.CSSStyleSheet.prototype.replace,o.CSSStyleSheet.prototype.replace=new Proxy(s,{apply:Ue(((e,t,o)=>{var[a]=o,{id:s,styleId:u}=it(t,n,i.styleMirror);return(s&&-1!==s||u&&-1!==u)&&r({id:s,styleId:u,replace:a}),e.apply(t,o)}))})),o.CSSStyleSheet.prototype.replaceSync&&(u=o.CSSStyleSheet.prototype.replaceSync,o.CSSStyleSheet.prototype.replaceSync=new Proxy(u,{apply:Ue(((e,t,o)=>{var[a]=o,{id:s,styleId:u}=it(t,n,i.styleMirror);return(s&&-1!==s||u&&-1!==u)&&r({id:s,styleId:u,replaceSync:a}),e.apply(t,o)}))}));var c={};ut("CSSGroupingRule")?c.CSSGroupingRule=o.CSSGroupingRule:(ut("CSSMediaRule")&&(c.CSSMediaRule=o.CSSMediaRule),ut("CSSConditionRule")&&(c.CSSConditionRule=o.CSSConditionRule),ut("CSSSupportsRule")&&(c.CSSSupportsRule=o.CSSSupportsRule));var d={};return Object.entries(c).forEach((e=>{var[t,o]=e;d[t]={insertRule:o.prototype.insertRule,deleteRule:o.prototype.deleteRule},o.prototype.insertRule=new Proxy(d[t].insertRule,{apply:Ue(((e,t,o)=>{var[a,s]=o,{id:u,styleId:l}=it(t.parentStyleSheet,n,i.styleMirror);return(u&&-1!==u||l&&-1!==l)&&r({id:u,styleId:l,adds:[{rule:a,index:[...nt(t),s||0]}]}),e.apply(t,o)}))}),o.prototype.deleteRule=new Proxy(d[t].deleteRule,{apply:Ue(((e,t,o)=>{var[a]=o,{id:s,styleId:u}=it(t.parentStyleSheet,n,i.styleMirror);return(s&&-1!==s||u&&-1!==u)&&r({id:s,styleId:u,removes:[{index:[...nt(t),a]}]}),e.apply(t,o)}))})})),Ue((()=>{o.CSSStyleSheet.prototype.insertRule=a,o.CSSStyleSheet.prototype.deleteRule=l,s&&(o.CSSStyleSheet.prototype.replace=s),u&&(o.CSSStyleSheet.prototype.replaceSync=u),Object.entries(c).forEach((e=>{var[t,r]=e;r.prototype.insertRule=d[t].insertRule,r.prototype.deleteRule=d[t].deleteRule}))}))}(e,{win:i}),h=ot(e,e.doc),f=function(e,t){var{styleDeclarationCb:r,mirror:n,ignoreCSSAttributes:i,stylesheetManager:o}=e,{win:a}=t,s=a.CSSStyleDeclaration.prototype.setProperty;a.CSSStyleDeclaration.prototype.setProperty=new Proxy(s,{apply:Ue(((e,t,a)=>{var u,[l,c,d]=a;if(i.has(l))return s.apply(t,[l,c,d]);var{id:h,styleId:f}=it(null==(u=t.parentRule)?void 0:u.parentStyleSheet,n,o.styleMirror);return(h&&-1!==h||f&&-1!==f)&&r({id:h,styleId:f,set:{property:l,value:c,priority:d},index:nt(t.parentRule)}),e.apply(t,a)}))});var u=a.CSSStyleDeclaration.prototype.removeProperty;return a.CSSStyleDeclaration.prototype.removeProperty=new Proxy(u,{apply:Ue(((e,t,a)=>{var s,[l]=a;if(i.has(l))return u.apply(t,[l]);var{id:c,styleId:d}=it(null==(s=t.parentRule)?void 0:s.parentStyleSheet,n,o.styleMirror);return(c&&-1!==c||d&&-1!==d)&&r({id:c,styleId:d,remove:{property:l},index:nt(t.parentRule)}),e.apply(t,a)}))}),Ue((()=>{a.CSSStyleDeclaration.prototype.setProperty=s,a.CSSStyleDeclaration.prototype.removeProperty=u}))}(e,{win:i}),e.collectFonts&&(m=function(e){var{fontCb:t,doc:r}=e,n=r.defaultView;if(!n)return()=>{};var i=[],o=new WeakMap,a=n.FontFace;n.FontFace=function(e,t,r){var n=new a(e,t,r);return o.set(n,{family:e,buffer:"string"!=typeof t,descriptors:r,fontSource:"string"==typeof t?t:JSON.stringify(Array.from(new Uint8Array(t)))}),n};var s=ge(r.fonts,"add",(function(e){return function(r){return setTimeout(Ue((()=>{var e=o.get(r);e&&(t(e),o.delete(r))})),0),e.apply(this,[r])}}));return i.push((()=>{n.FontFace=a})),i.push(s),Ue((()=>{i.forEach((e=>e()))}))}(e)));var v=function(e){var{doc:t,mirror:r,blockClass:n,blockSelector:i,selectionCb:o}=e,a=!0,s=Ue((()=>{var e=t.getSelection();if(!(!e||a&&(null==e?void 0:e.isCollapsed))){a=e.isCollapsed||!1;for(var s=[],u=e.rangeCount||0,l=0;l<u;l++){var c=e.getRangeAt(l),{startContainer:d,startOffset:h,endContainer:f,endOffset:m}=c;Ae(d,n,i,!0)||Ae(f,n,i,!0)||s.push({start:r.getId(d),startOffset:h,end:r.getId(f),endOffset:m})}o({ranges:s})}}));return s(),he("selectionchange",s)}(e),p=function(e){var{doc:t,customElementCb:r}=e,n=t.defaultView;return n&&n.customElements?ge(n.customElements,"define",(function(e){return function(t,n,i){try{r({define:{name:t}})}catch(e){console.warn("Custom element callback failed for "+t)}return e.apply(this,[t,n,i])}})):()=>{}}(e),g=[];for(var I of e.plugins)g.push(I.observer(I.callback,i,I.options));return Ue((()=>{Pe.forEach((e=>e.reset())),null==n||n.disconnect(),o(),a(),s(),u(),l(),c(),d(),h(),f(),m(),v(),p(),g.forEach((e=>e()))}))}function st(e){return void 0!==window[e]}function ut(e){return Boolean(void 0!==window[e]&&window[e].prototype&&"insertRule"in window[e].prototype&&"deleteRule"in window[e].prototype)}class lt{constructor(e){a(this,"iframeIdToRemoteIdMap",new WeakMap),a(this,"iframeRemoteIdToIdMap",new WeakMap),this.generateIdFn=e}getId(e,t,r,n){var i=r||this.getIdToRemoteIdMap(e),o=n||this.getRemoteIdToIdMap(e),a=i.get(t);return a||(a=this.generateIdFn(),i.set(t,a),o.set(a,t)),a}getIds(e,t){var r=this.getIdToRemoteIdMap(e),n=this.getRemoteIdToIdMap(e);return t.map((t=>this.getId(e,t,r,n)))}getRemoteId(e,t,r){var n=r||this.getRemoteIdToIdMap(e);if("number"!=typeof t)return t;var i=n.get(t);return i||-1}getRemoteIds(e,t){var r=this.getRemoteIdToIdMap(e);return t.map((t=>this.getRemoteId(e,t,r)))}reset(e){if(!e)return this.iframeIdToRemoteIdMap=new WeakMap,void(this.iframeRemoteIdToIdMap=new WeakMap);this.iframeIdToRemoteIdMap.delete(e),this.iframeRemoteIdToIdMap.delete(e)}getIdToRemoteIdMap(e){var t=this.iframeIdToRemoteIdMap.get(e);return t||(t=new Map,this.iframeIdToRemoteIdMap.set(e,t)),t}getRemoteIdToIdMap(e){var t=this.iframeRemoteIdToIdMap.get(e);return t||(t=new Map,this.iframeRemoteIdToIdMap.set(e,t)),t}}class ct{constructor(e){a(this,"iframes",new WeakMap),a(this,"crossOriginIframeMap",new WeakMap),a(this,"crossOriginIframeMirror",new lt(V)),a(this,"crossOriginIframeStyleMirror"),a(this,"crossOriginIframeRootIdMap",new WeakMap),a(this,"mirror"),a(this,"mutationCb"),a(this,"wrappedEmit"),a(this,"loadListener"),a(this,"stylesheetManager"),a(this,"recordCrossOriginIframes"),this.mutationCb=e.mutationCb,this.wrappedEmit=e.wrappedEmit,this.stylesheetManager=e.stylesheetManager,this.recordCrossOriginIframes=e.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new lt(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=e.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(e){this.iframes.set(e,!0),e.contentWindow&&this.crossOriginIframeMap.set(e.contentWindow,e)}addLoadListener(e){this.loadListener=e}attachIframe(e,t){var r,n;this.mutationCb({adds:[{parentId:this.mirror.getId(e),nextId:null,node:t}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),this.recordCrossOriginIframes&&(null==(r=e.contentWindow)||r.addEventListener("message",this.handleMessage.bind(this))),null==(n=this.loadListener)||n.call(this,e),e.contentDocument&&e.contentDocument.adoptedStyleSheets&&e.contentDocument.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(e.contentDocument.adoptedStyleSheets,this.mirror.getId(e.contentDocument))}handleMessage(e){var t=e;if("rrweb"===t.data.type&&t.origin===t.data.origin&&e.source){var r=this.crossOriginIframeMap.get(e.source);if(r){var n=this.transformCrossOriginEvent(r,t.data.event);n&&this.wrappedEmit(n,t.data.isCheckout)}}}transformCrossOriginEvent(e,t){var r;switch(t.type){case Ye.FullSnapshot:this.crossOriginIframeMirror.reset(e),this.crossOriginIframeStyleMirror.reset(e),this.replaceIdOnNode(t.data.node,e);var n=t.data.node.id;return this.crossOriginIframeRootIdMap.set(e,n),this.patchRootIdOnNode(t.data.node,n),{timestamp:t.timestamp,type:Ye.IncrementalSnapshot,data:{source:xe.Mutation,adds:[{parentId:this.mirror.getId(e),nextId:null,node:t.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}};case Ye.Meta:case Ye.Load:case Ye.DomContentLoaded:return!1;case Ye.Plugin:return t;case Ye.Custom:return this.replaceIds(t.data.payload,e,["id","parentId","previousId","nextId"]),t;case Ye.IncrementalSnapshot:switch(t.data.source){case xe.Mutation:return t.data.adds.forEach((t=>{this.replaceIds(t,e,["parentId","nextId","previousId"]),this.replaceIdOnNode(t.node,e);var r=this.crossOriginIframeRootIdMap.get(e);r&&this.patchRootIdOnNode(t.node,r)})),t.data.removes.forEach((t=>{this.replaceIds(t,e,["parentId","id"])})),t.data.attributes.forEach((t=>{this.replaceIds(t,e,["id"])})),t.data.texts.forEach((t=>{this.replaceIds(t,e,["id"])})),t;case xe.Drag:case xe.TouchMove:case xe.MouseMove:return t.data.positions.forEach((t=>{this.replaceIds(t,e,["id"])})),t;case xe.ViewportResize:return!1;case xe.MediaInteraction:case xe.MouseInteraction:case xe.Scroll:case xe.CanvasMutation:case xe.Input:return this.replaceIds(t.data,e,["id"]),t;case xe.StyleSheetRule:case xe.StyleDeclaration:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleId"]),t;case xe.Font:return t;case xe.Selection:return t.data.ranges.forEach((t=>{this.replaceIds(t,e,["start","end"])})),t;case xe.AdoptedStyleSheet:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleIds"]),null==(r=t.data.styles)||r.forEach((t=>{this.replaceStyleIds(t,e,["styleId"])})),t}}return!1}replace(e,t,r,n){for(var i of n)(Array.isArray(t[i])||"number"==typeof t[i])&&(Array.isArray(t[i])?t[i]=e.getIds(r,t[i]):t[i]=e.getId(r,t[i]));return t}replaceIds(e,t,r){return this.replace(this.crossOriginIframeMirror,e,t,r)}replaceStyleIds(e,t,r){return this.replace(this.crossOriginIframeStyleMirror,e,t,r)}replaceIdOnNode(e,t){this.replaceIds(e,t,["id","rootId"]),"childNodes"in e&&e.childNodes.forEach((e=>{this.replaceIdOnNode(e,t)}))}patchRootIdOnNode(e,t){e.type===l.Document||e.rootId||(e.rootId=t),"childNodes"in e&&e.childNodes.forEach((e=>{this.patchRootIdOnNode(e,t)}))}}class dt{constructor(e){a(this,"shadowDoms",new WeakSet),a(this,"mutationCb"),a(this,"scrollCb"),a(this,"bypassOptions"),a(this,"mirror"),a(this,"restoreHandlers",[]),this.mutationCb=e.mutationCb,this.scrollCb=e.scrollCb,this.bypassOptions=e.bypassOptions,this.mirror=e.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(e,t){if(b(e)&&!this.shadowDoms.has(e)){this.shadowDoms.add(e);var n=Qe(r({},this.bypassOptions,{doc:t,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this}),e);this.restoreHandlers.push((()=>n.disconnect())),this.restoreHandlers.push(et(r({},this.bypassOptions,{scrollCb:this.scrollCb,doc:e,mirror:this.mirror}))),setTimeout((()=>{e.adoptedStyleSheets&&e.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(e.adoptedStyleSheets,this.mirror.getId(de.host(e))),this.restoreHandlers.push(ot({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},e))}),0)}}observeAttachShadow(e){e.contentWindow&&e.contentDocument&&this.patchAttachShadow(e.contentWindow.Element,e.contentDocument)}patchAttachShadow(e,t){var r=this;this.restoreHandlers.push(ge(e.prototype,"attachShadow",(function(e){return function(n){var i=e.call(this,n),o=de.shadowRoot(this);return o&&Ze(this)&&r.addShadowRoot(o,t),i}})))}reset(){this.restoreHandlers.forEach((e=>{try{e()}catch(e){}})),this.restoreHandlers=[],this.shadowDoms=new WeakSet}}for(var ht="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ft="undefined"==typeof Uint8Array?[]:new Uint8Array(256),mt=0;mt<64;mt++)ft[ht.charCodeAt(mt)]=mt;var vt=new Map;var pt=(e,t,r)=>{if(e&&(yt(e,t)||"object"==typeof e)){var n=function(e,t){var r=vt.get(e);return r||(r=new Map,vt.set(e,r)),r.has(t)||r.set(t,[]),r.get(t)}(r,e.constructor.name),i=n.indexOf(e);return-1===i&&(i=n.length,n.push(e)),i}};function gt(e,t,r){if(e instanceof Array)return e.map((e=>gt(e,t,r)));if(null===e)return e;if(e instanceof Float32Array||e instanceof Float64Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Uint8Array||e instanceof Uint16Array||e instanceof Int16Array||e instanceof Int8Array||e instanceof Uint8ClampedArray)return{rr_type:e.constructor.name,args:[Object.values(e)]};if(e instanceof ArrayBuffer)return{rr_type:e.constructor.name,base64:function(e){var t,r=new Uint8Array(e),n=r.length,i="";for(t=0;t<n;t+=3)i+=ht[r[t]>>2],i+=ht[(3&r[t])<<4|r[t+1]>>4],i+=ht[(15&r[t+1])<<2|r[t+2]>>6],i+=ht[63&r[t+2]];return n%3==2?i=i.substring(0,i.length-1)+"=":n%3==1&&(i=i.substring(0,i.length-2)+"=="),i}(e)};if(e instanceof DataView)return{rr_type:e.constructor.name,args:[gt(e.buffer,t,r),e.byteOffset,e.byteLength]};if(e instanceof HTMLImageElement){var n=e.constructor.name,{src:i}=e;return{rr_type:n,src:i}}if(e instanceof HTMLCanvasElement){return{rr_type:"HTMLImageElement",src:e.toDataURL()}}return e instanceof ImageData?{rr_type:e.constructor.name,args:[gt(e.data,t,r),e.width,e.height]}:yt(e,t)||"object"==typeof e?{rr_type:e.constructor.name,index:pt(e,t,r)}:e}var It=(e,t,r)=>e.map((e=>gt(e,t,r))),yt=(e,t)=>{var r=["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject","WebGLVertexArrayObjectOES"].filter((e=>"function"==typeof t[e]));return Boolean(r.find((r=>e instanceof t[r])))};function Ct(e,t,r,n){var i=[];try{var o=ge(e.HTMLCanvasElement.prototype,"getContext",(function(e){return function(i){for(var o=arguments.length,a=new Array(o>1?o-1:0),s=1;s<o;s++)a[s-1]=arguments[s];if(!Ae(this,t,r,!0)){var u=function(e){return"experimental-webgl"===e?"webgl":e}(i);if("__context"in this||(this.__context=u),n&&["webgl","webgl2"].includes(u))if(a[0]&&"object"==typeof a[0]){var l=a[0];l.preserveDrawingBuffer||(l.preserveDrawingBuffer=!0)}else a.splice(0,1,{preserveDrawingBuffer:!0})}return e.apply(this,[i,...a])}}));i.push(o)}catch(e){console.error("failed to patch HTMLCanvasElement.prototype.getContext")}return()=>{i.forEach((e=>e()))}}function bt(e,t,r,n,i,o){var a=[],s=Object.getOwnPropertyNames(e),u=function(s){if(["isContextLost","canvas","drawingBufferWidth","drawingBufferHeight"].includes(s))return 0;try{if("function"!=typeof e[s])return 0;var u=ge(e,s,(function(e){return function(){for(var a=arguments.length,u=new Array(a),l=0;l<a;l++)u[l]=arguments[l];var c=e.apply(this,u);if(pt(c,o,this),"tagName"in this.canvas&&!Ae(this.canvas,n,i,!0)){var d=It(u,o,this),h={type:t,property:s,args:d};r(this.canvas,h)}return c}}));a.push(u)}catch(n){var l=pe(e,s,{set(e){r(this.canvas,{type:t,property:s,args:[e],setter:!0})}});a.push(l)}};for(var l of s)u(l);return a}var wt,At,St,kt,Nt="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",Rt="undefined"!=typeof window&&window.Blob&&new Blob([(wt=Nt,Uint8Array.from(atob(wt),(e=>e.charCodeAt(0))))],{type:"text/javascript;charset=utf-8"});function Tt(e){var t;try{if(!(t=Rt&&(window.URL||window.webkitURL).createObjectURL(Rt)))throw"";var r=new Worker(t,{name:null==e?void 0:e.name});return r.addEventListener("error",(()=>{(window.URL||window.webkitURL).revokeObjectURL(t)})),r}catch(t){return new Worker("data:text/javascript;base64,"+Nt,{name:null==e?void 0:e.name})}finally{t&&(window.URL||window.webkitURL).revokeObjectURL(t)}}class Ft{constructor(e){a(this,"pendingCanvasMutations",new Map),a(this,"rafStamps",{latestId:0,invokeId:null}),a(this,"mirror"),a(this,"mutationCb"),a(this,"resetObservers"),a(this,"frozen",!1),a(this,"locked",!1),a(this,"processMutation",((e,t)=>{!(this.rafStamps.invokeId&&this.rafStamps.latestId!==this.rafStamps.invokeId)&&this.rafStamps.invokeId||(this.rafStamps.invokeId=this.rafStamps.latestId),this.pendingCanvasMutations.has(e)||this.pendingCanvasMutations.set(e,[]),this.pendingCanvasMutations.get(e).push(t)}));var{sampling:t="all",win:r,blockClass:n,blockSelector:i,recordCanvas:o,dataURLOptions:s}=e;this.mutationCb=e.mutationCb,this.mirror=e.mirror,o&&"all"===t&&this.initCanvasMutationObserver(r,n,i),o&&"number"==typeof t&&this.initCanvasFPSObserver(t,r,n,i,{dataURLOptions:s})}reset(){this.pendingCanvasMutations.clear(),this.resetObservers&&this.resetObservers()}freeze(){this.frozen=!0}unfreeze(){this.frozen=!1}lock(){this.locked=!0}unlock(){this.locked=!1}initCanvasFPSObserver(e,r,n,i,o){var a=this,s=Ct(r,n,i,!0),u=new Map,l=new Tt;l.onmessage=e=>{var{id:t}=e.data;if(u.set(t,!1),"base64"in e.data){var{base64:r,type:n,width:i,height:o}=e.data;this.mutationCb({id:t,type:We["2D"],commands:[{property:"clearRect",args:[0,0,i,o]},{property:"drawImage",args:[{rr_type:"ImageBitmap",args:[{rr_type:"Blob",data:[{rr_type:"ArrayBuffer",base64:r}],type:n}]},0,0]}]})}};var c,d=1e3/e,h=0,f=e=>{var s,m;h&&e-h<d?c=requestAnimationFrame(f):(h=e,(s=[],m=e=>{e.querySelectorAll("canvas").forEach((e=>{Ae(e,n,i,!0)||s.push(e)})),e.querySelectorAll("*").forEach((e=>{e.shadowRoot&&m(e.shadowRoot)}))},m(r.document),s).forEach(function(){var e=t((function*(e){var t,r=a.mirror.getId(e);if(!u.get(r)&&0!==e.width&&0!==e.height){if(u.set(r,!0),["webgl","webgl2"].includes(e.__context)){var n=e.getContext(e.__context);!1===(null==(t=null==n?void 0:n.getContextAttributes())?void 0:t.preserveDrawingBuffer)&&n.clear(n.COLOR_BUFFER_BIT)}var i=e.clientWidth||e.width,s=e.clientHeight||e.height,c=yield createImageBitmap(e,{resizeWidth:i,resizeHeight:s});l.postMessage({id:r,bitmap:c,width:i,height:s,dataURLOptions:o.dataURLOptions},[c])}}));return function(t){return e.apply(this,arguments)}}()),c=requestAnimationFrame(f))};c=requestAnimationFrame(f),this.resetObservers=()=>{s(),cancelAnimationFrame(c)}}initCanvasMutationObserver(e,t,r){this.startRAFTimestamping(),this.startPendingCanvasMutationFlusher();var n=Ct(e,t,r,!1),i=function(e,t,r,n){var i=[],o=Object.getOwnPropertyNames(t.CanvasRenderingContext2D.prototype),a=function(o){try{if("function"!=typeof t.CanvasRenderingContext2D.prototype[o])return 1;var a=ge(t.CanvasRenderingContext2D.prototype,o,(function(i){return function(){for(var a=arguments.length,s=new Array(a),u=0;u<a;u++)s[u]=arguments[u];return Ae(this.canvas,r,n,!0)||setTimeout((()=>{var r=It(s,t,this);e(this.canvas,{type:We["2D"],property:o,args:r})}),0),i.apply(this,s)}}));i.push(a)}catch(r){var s=pe(t.CanvasRenderingContext2D.prototype,o,{set(t){e(this.canvas,{type:We["2D"],property:o,args:[t],setter:!0})}});i.push(s)}};for(var s of o)a(s);return()=>{i.forEach((e=>e()))}}(this.processMutation.bind(this),e,t,r),o=function(e,t,r,n){var i=[];return i.push(...bt(t.WebGLRenderingContext.prototype,We.WebGL,e,r,n,t)),void 0!==t.WebGL2RenderingContext&&i.push(...bt(t.WebGL2RenderingContext.prototype,We.WebGL2,e,r,n,t)),()=>{i.forEach((e=>e()))}}(this.processMutation.bind(this),e,t,r);this.resetObservers=()=>{n(),i(),o()}}startPendingCanvasMutationFlusher(){requestAnimationFrame((()=>this.flushPendingCanvasMutations()))}startRAFTimestamping(){var e=t=>{this.rafStamps.latestId=t,requestAnimationFrame(e)};requestAnimationFrame(e)}flushPendingCanvasMutations(){this.pendingCanvasMutations.forEach(((e,t)=>{var r=this.mirror.getId(t);this.flushPendingCanvasMutationFor(t,r)})),requestAnimationFrame((()=>this.flushPendingCanvasMutations()))}flushPendingCanvasMutationFor(e,t){if(!this.frozen&&!this.locked){var r=this.pendingCanvasMutations.get(e);if(r&&-1!==t){var n=r.map((e=>{var t=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,i);return t})),{type:o}=r[0];this.mutationCb({id:t,type:o,commands:n}),this.pendingCanvasMutations.delete(e)}}}}class Mt{constructor(e){a(this,"trackedLinkElements",new WeakSet),a(this,"mutationCb"),a(this,"adoptedStyleSheetCb"),a(this,"styleMirror",new Me),this.mutationCb=e.mutationCb,this.adoptedStyleSheetCb=e.adoptedStyleSheetCb}attachLinkElement(e,t){"_cssText"in t.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:t.id,attributes:t.attributes}]}),this.trackLinkElement(e)}trackLinkElement(e){this.trackedLinkElements.has(e)||(this.trackedLinkElements.add(e),this.trackStylesheetInLinkElement(e))}adoptStyleSheets(e,t){var r=this;if(0!==e.length){var n={id:t,styleIds:[]},i=[],o=function(e){var t;r.styleMirror.has(e)?t=r.styleMirror.getId(e):(t=r.styleMirror.add(e),i.push({styleId:t,rules:Array.from(e.rules||CSSRule,((t,r)=>({rule:A(t,e.href),index:r})))})),n.styleIds.push(t)};for(var a of e)o(a);i.length>0&&(n.styles=i),this.adoptedStyleSheetCb(n)}}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(e){}}class Bt{constructor(){a(this,"nodeMap",new WeakMap),a(this,"active",!1)}inOtherBuffer(e,t){var r=this.nodeMap.get(e);return r&&Array.from(r).some((e=>e!==t))}add(e,t){this.active||(this.active=!0,requestAnimationFrame((()=>{this.nodeMap=new WeakMap,this.active=!1}))),this.nodeMap.set(e,(this.nodeMap.get(e)||new Set).add(t))}destroy(){}}var Ot=!1;try{if(2!==Array.from([1],(e=>2*e))[0]){var Zt=document.createElement("iframe");document.body.appendChild(Zt),Array.from=(null==(n=Zt.contentWindow)?void 0:n.Array.from)||Array.from,document.body.removeChild(Zt)}}catch(e){console.debug("Unable to override Array.from",e)}var Yt,xt,Gt=new k;function Et(e){void 0===e&&(e={});var{emit:t,checkoutEveryNms:n,checkoutEveryNth:i,blockClass:o="rr-block",blockSelector:a=null,ignoreClass:s="rr-ignore",ignoreSelector:u=null,maskTextClass:l="rr-mask",maskTextSelector:c=null,inlineStylesheet:d=!0,maskAllInputs:h,maskInputOptions:f,slimDOMOptions:m,maskInputFn:v,maskTextFn:p,hooks:g,packFn:I,sampling:y={},dataURLOptions:C={},mousemoveWait:b,recordDOM:w=!0,recordCanvas:A=!1,recordCrossOriginIframes:S=!1,recordAfter:N=("DOMContentLoaded"===e.recordAfter?e.recordAfter:"load"),userTriggeredOnInput:R=!1,collectFonts:T=!1,inlineImages:F=!1,plugins:M,keepIframeSrcFn:B=(()=>!1),ignoreCSSAttributes:O=new Set([]),errorHandler:Z}=e;De=Z;var Y=!S||window.parent===window,x=!1;if(!Y)try{window.parent.document&&(x=!1)}catch(e){x=!0}if(Y&&!t)throw new Error("emit function is required");if(!Y&&!x)return()=>{};void 0!==b&&void 0===y.mousemove&&(y.mousemove=b),Gt.reset();var G,E=!0===h?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:void 0!==f?f:{password:!0},W=!0===m||"all"===m?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===m,headMetaDescKeywords:"all"===m,headTitleMutations:"all"===m}:m||{};!function(e){void 0===e&&(e=window),"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach)}();var L=0,X=e=>{for(var t of M||[])t.eventProcessor&&(e=t.eventProcessor(e));return I&&!x&&(e=I(e)),e};At=(e,r)=>{var o,a=e;if(a.timestamp=Ie(),!(null==(o=Pe[0])?void 0:o.isFrozen())||a.type===Ye.FullSnapshot||a.type===Ye.IncrementalSnapshot&&a.data.source===xe.Mutation||Pe.forEach((e=>e.unfreeze())),Y)null==t||t(X(a),r);else if(x){var s={type:"rrweb",event:X(a),origin:window.location.origin,isCheckout:r};window.parent.postMessage(s,"*")}if(a.type===Ye.FullSnapshot)G=a,L=0;else if(a.type===Ye.IncrementalSnapshot){if(a.data.source===xe.Mutation&&a.data.isAttachIframe)return;L++;var u=i&&L>=i,l=n&&a.timestamp-G.timestamp>n;(u||l)&&St(!0)}};var V=e=>{At({type:Ye.IncrementalSnapshot,data:r({source:xe.Mutation},e)})},D=e=>At({type:Ye.IncrementalSnapshot,data:r({source:xe.Scroll},e)}),K=e=>At({type:Ye.IncrementalSnapshot,data:r({source:xe.CanvasMutation},e)}),J=new Mt({mutationCb:V,adoptedStyleSheetCb:e=>At({type:Ye.IncrementalSnapshot,data:r({source:xe.AdoptedStyleSheet},e)})}),z=new ct({mirror:Gt,mutationCb:V,stylesheetManager:J,recordCrossOriginIframes:S,wrappedEmit:At});for(var H of M||[])H.getMirror&&H.getMirror({nodeMirror:Gt,crossOriginIframeMirror:z.crossOriginIframeMirror,crossOriginIframeStyleMirror:z.crossOriginIframeStyleMirror});var j=new Bt;kt=new Ft({recordCanvas:A,mutationCb:K,win:window,blockClass:o,blockSelector:a,mirror:Gt,sampling:y.canvas,dataURLOptions:C});var _=new dt({mutationCb:V,scrollCb:D,bypassOptions:{blockClass:o,blockSelector:a,maskTextClass:l,maskTextSelector:c,inlineStylesheet:d,maskInputOptions:E,dataURLOptions:C,maskTextFn:p,maskInputFn:v,recordCanvas:A,inlineImages:F,sampling:y,slimDOMOptions:W,iframeManager:z,stylesheetManager:J,canvasManager:kt,keepIframeSrcFn:B,processedNodeManager:j},mirror:Gt});St=function(e){if(void 0===e&&(e=!1),w){At({type:Ye.Meta,data:{href:window.location.href,width:be(),height:Ce()}},e),J.reset(),_.init(),Pe.forEach((e=>e.lock()));var t=function(e,t){var{mirror:r=new k,blockClass:n="rr-block",blockSelector:i=null,maskTextClass:o="rr-mask",maskTextSelector:a=null,inlineStylesheet:s=!0,inlineImages:u=!1,recordCanvas:l=!1,maskAllInputs:c=!1,maskTextFn:d,maskInputFn:h,slimDOM:f=!1,dataURLOptions:m,preserveWhiteSpace:v,onSerialize:p,onIframeLoad:g,iframeLoadTimeout:I,onStylesheetLoad:y,stylesheetLoadTimeout:C,keepIframeSrcFn:b=(()=>!1)}=t||{};return ee(e,{doc:e,mirror:r,blockClass:n,blockSelector:i,maskTextClass:o,maskTextSelector:a,skipChild:!1,inlineStylesheet:s,maskInputOptions:!0===c?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:!1===c?{password:!0}:c,maskTextFn:d,maskInputFn:h,slimDOMOptions:!0===f||"all"===f?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===f,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===f?{}:f,dataURLOptions:m,inlineImages:u,recordCanvas:l,preserveWhiteSpace:v,onSerialize:p,onIframeLoad:g,iframeLoadTimeout:I,onStylesheetLoad:y,stylesheetLoadTimeout:C,keepIframeSrcFn:b,newlyAddedElement:!1})}(document,{mirror:Gt,blockClass:o,blockSelector:a,maskTextClass:l,maskTextSelector:c,inlineStylesheet:d,maskAllInputs:E,maskTextFn:p,maskInputFn:v,slimDOM:W,dataURLOptions:C,recordCanvas:A,inlineImages:F,onSerialize:e=>{Re(e,Gt)&&z.addIframe(e),Te(e,Gt)&&J.trackLinkElement(e),Fe(e)&&_.addShadowRoot(de.shadowRoot(e),document)},onIframeLoad:(e,t)=>{z.attachIframe(e,t),_.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{J.attachLinkElement(e,t)},keepIframeSrcFn:B});if(!t)return console.warn("Failed to snapshot the document");At({type:Ye.FullSnapshot,data:{node:t,initialOffset:ye(window)}},e),Pe.forEach((e=>e.unlock())),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&J.adoptStyleSheets(document.adoptedStyleSheets,Gt.getId(document))}};try{var U=[],P=e=>{var t;return Ue(at)({mutationCb:V,mousemoveCb:(e,t)=>At({type:Ye.IncrementalSnapshot,data:{source:t,positions:e}}),mouseInteractionCb:e=>At({type:Ye.IncrementalSnapshot,data:r({source:xe.MouseInteraction},e)}),scrollCb:D,viewportResizeCb:e=>At({type:Ye.IncrementalSnapshot,data:r({source:xe.ViewportResize},e)}),inputCb:e=>At({type:Ye.IncrementalSnapshot,data:r({source:xe.Input},e)}),mediaInteractionCb:e=>At({type:Ye.IncrementalSnapshot,data:r({source:xe.MediaInteraction},e)}),styleSheetRuleCb:e=>At({type:Ye.IncrementalSnapshot,data:r({source:xe.StyleSheetRule},e)}),styleDeclarationCb:e=>At({type:Ye.IncrementalSnapshot,data:r({source:xe.StyleDeclaration},e)}),canvasMutationCb:K,fontCb:e=>At({type:Ye.IncrementalSnapshot,data:r({source:xe.Font},e)}),selectionCb:e=>{At({type:Ye.IncrementalSnapshot,data:r({source:xe.Selection},e)})},customElementCb:e=>{At({type:Ye.IncrementalSnapshot,data:r({source:xe.CustomElement},e)})},blockClass:o,ignoreClass:s,ignoreSelector:u,maskTextClass:l,maskTextSelector:c,maskInputOptions:E,inlineStylesheet:d,sampling:y,recordDOM:w,recordCanvas:A,inlineImages:F,userTriggeredOnInput:R,collectFonts:T,doc:e,maskInputFn:v,maskTextFn:p,keepIframeSrcFn:B,blockSelector:a,slimDOMOptions:W,dataURLOptions:C,mirror:Gt,iframeManager:z,stylesheetManager:J,shadowDomManager:_,processedNodeManager:j,canvasManager:kt,ignoreCSSAttributes:O,plugins:(null==(t=null==M?void 0:M.filter((e=>e.observer)))?void 0:t.map((e=>({observer:e.observer,options:e.options,callback:t=>At({type:Ye.Plugin,data:{plugin:e.name,payload:t}})}))))||[]},g)};z.addLoadListener((e=>{try{U.push(P(e.contentDocument))}catch(e){console.warn(e)}}));var q=()=>{St(),U.push(P(document)),Ot=!0};return"interactive"===document.readyState||"complete"===document.readyState?q():(U.push(he("DOMContentLoaded",(()=>{At({type:Ye.DomContentLoaded,data:{}}),"DOMContentLoaded"===N&&q()}))),U.push(he("load",(()=>{At({type:Ye.Load,data:{}}),"load"===N&&q()}),window))),()=>{U.forEach((e=>e())),j.destroy(),Ot=!1,De=void 0}}catch(e){console.warn(e)}}Et.addCustomEvent=(e,t)=>{if(!Ot)throw new Error("please add custom event after start recording");At({type:Ye.Custom,data:{tag:e,payload:t}})},Et.freezePage=()=>{Pe.forEach((e=>e.freeze()))},Et.takeFullSnapshot=e=>{if(!Ot)throw new Error("please take full snapshot after start recording");St(e)},Et.mirror=Gt,(xt=Yt||(Yt={}))[xt.NotStarted=0]="NotStarted",xt[xt.Running=1]="Running",xt[xt.Stopped=2]="Stopped";var Wt,Lt=Object.defineProperty,Xt=(e,t,r)=>((e,t,r)=>t in e?Lt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),Vt=Object.defineProperty,Dt=(e,t,r)=>((e,t,r)=>t in e?Vt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),Kt=Object.defineProperty,Jt=(e,t,r)=>((e,t,r)=>t in e?Kt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),zt={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]},Ht={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]},jt={};function _t(e){if(jt[e])return jt[e];var t=globalThis[e],r=t.prototype,n=e in zt?zt[e]:void 0,i=Boolean(n&&n.every((e=>{var t,n;return Boolean(null==(n=null==(t=Object.getOwnPropertyDescriptor(r,e))?void 0:t.get)?void 0:n.toString().includes("[native code]"))}))),o=e in Ht?Ht[e]:void 0,a=Boolean(o&&o.every((e=>{var t;return"function"==typeof r[e]&&(null==(t=r[e])?void 0:t.toString().includes("[native code]"))})));if(i&&a)return jt[e]=t.prototype,t.prototype;try{var s=document.createElement("iframe");document.body.appendChild(s);var u=s.contentWindow;if(!u)return t.prototype;var l=u[e].prototype;return document.body.removeChild(s),l?jt[e]=l:r}catch(e){return r}}var Ut={};function Pt(e,t,r){var n,i=e+"."+String(r);if(Ut[i])return Ut[i].call(t);var o=_t(e),a=null==(n=Object.getOwnPropertyDescriptor(o,r))?void 0:n.get;return a?(Ut[i]=a,a.call(t)):t[r]}var qt={};function Qt(e,t,r){var n=e+"."+String(r);if(qt[n])return qt[n].bind(t);var i=_t(e)[r];return"function"!=typeof i?t[r]:(qt[n]=i,i.bind(t))}var $t={childNodes:function(e){return Pt("Node",e,"childNodes")},parentNode:function(e){return Pt("Node",e,"parentNode")},parentElement:function(e){return Pt("Node",e,"parentElement")},textContent:function(e){return Pt("Node",e,"textContent")},contains:function(e,t){return Qt("Node",e,"contains")(t)},getRootNode:function(e){return Qt("Node",e,"getRootNode")()},host:function(e){return e&&"host"in e?Pt("ShadowRoot",e,"host"):null},styleSheets:function(e){return e.styleSheets},shadowRoot:function(e){return e&&"shadowRoot"in e?Pt("Element",e,"shadowRoot"):null},querySelector:function(e,t){return Pt("Element",e,"querySelector")(t)},querySelectorAll:function(e,t){return Pt("Element",e,"querySelectorAll")(t)},mutationObserver:function(){return _t("MutationObserver").constructor}};class er{constructor(){Jt(this,"idNodeMap",new Map),Jt(this,"nodeMetaMap",new WeakMap)}getId(e){var t;if(!e)return-1;var r=null==(t=this.getMeta(e))?void 0:t.id;return null!=r?r:-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){var t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach((e=>this.removeNodeFromMap(e)))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){var r=t.id;this.idNodeMap.set(r,e),this.nodeMetaMap.set(e,t)}replace(e,t){var r=this.getNode(e);if(r){var n=this.nodeMetaMap.get(r);n&&this.nodeMetaMap.set(t,n)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function tr(e,t,r){if(!e)return!1;if(e.nodeType!==e.ELEMENT_NODE)return!!r&&tr($t.parentNode(e),t,r);for(var n=e.classList.length;n--;){var i=e.classList[n];if(t.test(i))return!0}return!!r&&tr($t.parentNode(e),t,r)}class rr{constructor(){__publicField22(this,"parentElement",null),__publicField22(this,"parentNode",null),__publicField22(this,"ownerDocument"),__publicField22(this,"firstChild",null),__publicField22(this,"lastChild",null),__publicField22(this,"previousSibling",null),__publicField22(this,"nextSibling",null),__publicField22(this,"ELEMENT_NODE",1),__publicField22(this,"TEXT_NODE",3),__publicField22(this,"nodeType"),__publicField22(this,"nodeName"),__publicField22(this,"RRNodeType")}get childNodes(){for(var e=[],t=this.firstChild;t;)e.push(t),t=t.nextSibling;return e}contains(e){if(!(e instanceof rr))return!1;if(e.ownerDocument!==this.ownerDocument)return!1;if(e===this)return!0;for(;e.parentNode;){if(e.parentNode===this)return!0;e=e.parentNode}return!1}appendChild(e){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")}insertBefore(e,t){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")}removeChild(e){throw new Error("RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.")}toString(){return"RRNode"}}var nr={Node:["childNodes","parentNode","parentElement","textContent"],ShadowRoot:["host","styleSheets"],Element:["shadowRoot","querySelector","querySelectorAll"],MutationObserver:[]},ir={Node:["contains","getRootNode"],ShadowRoot:["getSelection"],Element:[],MutationObserver:["constructor"]},or={};function ar(e){if(or[e])return or[e];var t=globalThis[e],r=t.prototype,n=e in nr?nr[e]:void 0,i=Boolean(n&&n.every((e=>{var t,n;return Boolean(null==(n=null==(t=Object.getOwnPropertyDescriptor(r,e))?void 0:t.get)?void 0:n.toString().includes("[native code]"))}))),o=e in ir?ir[e]:void 0,a=Boolean(o&&o.every((e=>{var t;return"function"==typeof r[e]&&(null==(t=r[e])?void 0:t.toString().includes("[native code]"))})));if(i&&a)return or[e]=t.prototype,t.prototype;try{var s=document.createElement("iframe");document.body.appendChild(s);var u=s.contentWindow;if(!u)return t.prototype;var l=u[e].prototype;return document.body.removeChild(s),l?or[e]=l:r}catch(e){return r}}var sr={};function ur(e,t,r){var n,i=e+"."+String(r);if(sr[i])return sr[i].call(t);var o=ar(e),a=null==(n=Object.getOwnPropertyDescriptor(o,r))?void 0:n.get;return a?(sr[i]=a,a.call(t)):t[r]}var lr={};function cr(e,t,r){var n=e+"."+String(r);if(lr[n])return lr[n].bind(t);var i=ar(e)[r];return"function"!=typeof i?t[r]:(lr[n]=i,i.bind(t))}var dr={childNodes:function(e){return ur("Node",e,"childNodes")},parentNode:function(e){return ur("Node",e,"parentNode")},parentElement:function(e){return ur("Node",e,"parentElement")},textContent:function(e){return ur("Node",e,"textContent")},contains:function(e,t){return cr("Node",e,"contains")(t)},getRootNode:function(e){return cr("Node",e,"getRootNode")()},host:function(e){return e&&"host"in e?ur("ShadowRoot",e,"host"):null},styleSheets:function(e){return e.styleSheets},shadowRoot:function(e){return e&&"shadowRoot"in e?ur("Element",e,"shadowRoot"):null},querySelector:function(e,t){return ur("Element",e,"querySelector")(t)},querySelectorAll:function(e,t){return ur("Element",e,"querySelectorAll")(t)},mutationObserver:function(){return ar("MutationObserver").constructor}};var hr="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",fr={map:{},getId:()=>(console.error(hr),-1),getNode:()=>(console.error(hr),null),removeNodeFromMap(){console.error(hr)},has:()=>(console.error(hr),!1),reset(){console.error(hr)}};"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(fr=new Proxy(fr,{get:(e,t,r)=>("map"===t&&console.error(hr),Reflect.get(e,t,r))}));var mr=Date.now;function vr(e){return e?e.nodeType===e.ELEMENT_NODE?e:dr.parentElement(e):null}/[1-9][0-9]{12}/.test(Date.now().toString())||(mr=()=>(new Date).getTime());function pr(e){var t,r=null;return"getRootNode"in e&&(null==(t=dr.getRootNode(e))?void 0:t.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&dr.host(dr.getRootNode(e))&&(r=dr.host(dr.getRootNode(e))),r}function gr(e){for(var t,r=e;t=pr(r);)r=t;return r}function Ir(e){var t=e.ownerDocument;if(!t)return!1;var r=gr(e);return dr.contains(t,r)}for(var yr=Object.freeze(Object.defineProperty({__proto__:null,StyleSheetMirror:class{constructor(){Dt(this,"id",1),Dt(this,"styleIDMap",new WeakMap),Dt(this,"idStyleMap",new Map)}getId(e){var t;return null!==(t=this.styleIDMap.get(e))&&void 0!==t?t:-1}has(e){return this.styleIDMap.has(e)}add(e,t){return this.has(e)?this.getId(e):(r=void 0===t?this.id++:t,this.styleIDMap.set(e,r),this.idStyleMap.set(r,e),r);var r}getStyle(e){return this.idStyleMap.get(e)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}},get _mirror(){return fr},closestElementOfNode:vr,getBaseDimension:function e(t,r){var n,i,o=null==(i=null==(n=t.ownerDocument)?void 0:n.defaultView)?void 0:i.frameElement;if(!o||o===r)return{x:0,y:0,relativeScale:1,absoluteScale:1};var a=o.getBoundingClientRect(),s=e(o,r),u=a.height/o.clientHeight;return{x:a.x*s.relativeScale+s.x,y:a.y*s.relativeScale+s.y,relativeScale:u,absoluteScale:s.absoluteScale*u}},getNestedRule:function e(t,r){var n=t[r[0]];return 1===r.length?n:e(n.cssRules[r[1]].cssRules,r.slice(2))},getPositionsAndIndex:function(e){var t=[...e],r=t.pop();return{positions:t,index:r}},getRootShadowHost:gr,getShadowHost:pr,getWindowHeight:function(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight},getWindowScroll:function(e){var t,r,n,i,o=e.document;return{left:o.scrollingElement?o.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:o.documentElement.scrollLeft||(null==o?void 0:o.body)&&(null==(t=dr.parentElement(o.body))?void 0:t.scrollLeft)||(null==(r=null==o?void 0:o.body)?void 0:r.scrollLeft)||0,top:o.scrollingElement?o.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:(null==o?void 0:o.documentElement.scrollTop)||(null==o?void 0:o.body)&&(null==(n=dr.parentElement(o.body))?void 0:n.scrollTop)||(null==(i=null==o?void 0:o.body)?void 0:i.scrollTop)||0}},getWindowWidth:function(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth},hasShadowRoot:function(e){return!!e&&(e instanceof rr&&"shadowRoot"in e?Boolean(e.shadowRoot):Boolean(dr.shadowRoot(e)))},hookSetter:function e(t,r,n,i,o){void 0===o&&(o=window);var a=o.Object.getOwnPropertyDescriptor(t,r);return o.Object.defineProperty(t,r,i?n:{set(e){setTimeout((()=>{n.set.call(this,e)}),0),a&&a.set&&a.set.call(this,e)}}),()=>e(t,r,a||{},!0)},inDom:function(e){var t=e.ownerDocument;return!!t&&(dr.contains(t,e)||Ir(e))},isAncestorRemoved:function e(t,r){if(i=(n=t)&&"host"in n&&"mode"in n&&$t.host(n)||null,Boolean(i&&"shadowRoot"in i&&$t.shadowRoot(i)===n))return!1;var n,i,o=r.getId(t);if(!r.has(o))return!0;var a=dr.parentNode(t);return(!a||a.nodeType!==t.DOCUMENT_NODE)&&(!a||e(a,r))},isBlocked:function(e,t,r,n){if(!e)return!1;var i=vr(e);if(!i)return!1;try{if("string"==typeof t){if(i.classList.contains(t))return!0;if(n&&null!==i.closest("."+t))return!0}else if(tr(i,t,n))return!0}catch(e){}if(r){if(i.matches(r))return!0;if(n&&null!==i.closest(r))return!0}return!1},isIgnored:function(e,t,r){return!("TITLE"!==e.tagName||!r.headTitleMutations)||-2===t.getId(e)},isSerialized:function(e,t){return-1!==t.getId(e)},isSerializedIframe:function(e,t){return Boolean("IFRAME"===e.nodeName&&t.getMeta(e))},isSerializedStylesheet:function(e,t){return Boolean("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))},iterateResolveTree:function e(t,r){r(t.value);for(var n=t.children.length-1;n>=0;n--)e(t.children[n],r)},legacy_isTouchEvent:function(e){return Boolean(e.changedTouches)},get nowTimestamp(){return mr},on:function(e,t,r){void 0===r&&(r=document);var n={capture:!0,passive:!0};return r.addEventListener(e,t,n),()=>r.removeEventListener(e,t,n)},patch:function(e,t,r){try{if(!(t in e))return()=>{};var n=e[t],i=r(n);return"function"==typeof i&&(i.prototype=i.prototype||{},Object.defineProperties(i,{__rrweb_original__:{enumerable:!1,value:n}})),e[t]=i,()=>{e[t]=n}}catch(e){return()=>{}}},polyfill:function(e){void 0===e&&(e=window),"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach)},queueToResolveTrees:function(e){var t={},r=(e,r)=>{var n={value:e,parent:r,children:[]};return t[e.node.id]=n,n},n=[];for(var i of e){var{nextId:o,parentId:a}=i;if(o&&o in t){var s=t[o];if(s.parent){var u=s.parent.children.indexOf(s);s.parent.children.splice(u,0,r(i,s.parent))}else{var l=n.indexOf(s);n.splice(l,0,r(i,null))}}else if(a in t){var c=t[a];c.children.push(r(i,c))}else n.push(r(i,null))}return n},shadowHostInDom:Ir,throttle:function(e,t,r){void 0===r&&(r={});var n=null,i=0;return function(){for(var o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];var u=Date.now();i||!1!==r.leading||(i=u);var l=t-(u-i),c=this;l<=0||l>t?(n&&(clearTimeout(n),n=null),i=u,e.apply(c,a)):n||!1===r.trailing||(n=setTimeout((()=>{i=!1===r.leading?0:Date.now(),n=null,e.apply(c,a)}),l))}},uniqueTextMutations:function(e){for(var t=new Set,r=[],n=e.length;n--;){var i=e[n];t.has(i.id)||(r.push(i),t.add(i.id))}return r}},Symbol.toStringTag,{value:"Module"})),Cr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",br="undefined"==typeof Uint8Array?[]:new Uint8Array(256),wr=0;wr<64;wr++)br[Cr.charCodeAt(wr)]=wr;var Ar;"undefined"!=typeof window&&window.Blob&&new Blob([(e=>Uint8Array.from(atob(e),(e=>e.charCodeAt(0))))("KGZ1bmN0aW9uKCkgewogICJ1c2Ugc3RyaWN0IjsKICB2YXIgY2hhcnMgPSAiQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVphYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejAxMjM0NTY3ODkrLyI7CiAgdmFyIGxvb2t1cCA9IHR5cGVvZiBVaW50OEFycmF5ID09PSAidW5kZWZpbmVkIiA/IFtdIDogbmV3IFVpbnQ4QXJyYXkoMjU2KTsKICBmb3IgKHZhciBpID0gMDsgaSA8IGNoYXJzLmxlbmd0aDsgaSsrKSB7CiAgICBsb29rdXBbY2hhcnMuY2hhckNvZGVBdChpKV0gPSBpOwogIH0KICB2YXIgZW5jb2RlID0gZnVuY3Rpb24oYXJyYXlidWZmZXIpIHsKICAgIHZhciBieXRlcyA9IG5ldyBVaW50OEFycmF5KGFycmF5YnVmZmVyKSwgaTIsIGxlbiA9IGJ5dGVzLmxlbmd0aCwgYmFzZTY0ID0gIiI7CiAgICBmb3IgKGkyID0gMDsgaTIgPCBsZW47IGkyICs9IDMpIHsKICAgICAgYmFzZTY0ICs9IGNoYXJzW2J5dGVzW2kyXSA+PiAyXTsKICAgICAgYmFzZTY0ICs9IGNoYXJzWyhieXRlc1tpMl0gJiAzKSA8PCA0IHwgYnl0ZXNbaTIgKyAxXSA+PiA0XTsKICAgICAgYmFzZTY0ICs9IGNoYXJzWyhieXRlc1tpMiArIDFdICYgMTUpIDw8IDIgfCBieXRlc1tpMiArIDJdID4+IDZdOwogICAgICBiYXNlNjQgKz0gY2hhcnNbYnl0ZXNbaTIgKyAyXSAmIDYzXTsKICAgIH0KICAgIGlmIChsZW4gJSAzID09PSAyKSB7CiAgICAgIGJhc2U2NCA9IGJhc2U2NC5zdWJzdHJpbmcoMCwgYmFzZTY0Lmxlbmd0aCAtIDEpICsgIj0iOwogICAgfSBlbHNlIGlmIChsZW4gJSAzID09PSAxKSB7CiAgICAgIGJhc2U2NCA9IGJhc2U2NC5zdWJzdHJpbmcoMCwgYmFzZTY0Lmxlbmd0aCAtIDIpICsgIj09IjsKICAgIH0KICAgIHJldHVybiBiYXNlNjQ7CiAgfTsKICBjb25zdCBsYXN0QmxvYk1hcCA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKCk7CiAgY29uc3QgdHJhbnNwYXJlbnRCbG9iTWFwID0gLyogQF9fUFVSRV9fICovIG5ldyBNYXAoKTsKICBhc3luYyBmdW5jdGlvbiBnZXRUcmFuc3BhcmVudEJsb2JGb3Iod2lkdGgsIGhlaWdodCwgZGF0YVVSTE9wdGlvbnMpIHsKICAgIGNvbnN0IGlkID0gYCR7d2lkdGh9LSR7aGVpZ2h0fWA7CiAgICBpZiAoIk9mZnNjcmVlbkNhbnZhcyIgaW4gZ2xvYmFsVGhpcykgewogICAgICBpZiAodHJhbnNwYXJlbnRCbG9iTWFwLmhhcyhpZCkpIHJldHVybiB0cmFuc3BhcmVudEJsb2JNYXAuZ2V0KGlkKTsKICAgICAgY29uc3Qgb2Zmc2NyZWVuID0gbmV3IE9mZnNjcmVlbkNhbnZhcyh3aWR0aCwgaGVpZ2h0KTsKICAgICAgb2Zmc2NyZWVuLmdldENvbnRleHQoIjJkIik7CiAgICAgIGNvbnN0IGJsb2IgPSBhd2FpdCBvZmZzY3JlZW4uY29udmVydFRvQmxvYihkYXRhVVJMT3B0aW9ucyk7CiAgICAgIGNvbnN0IGFycmF5QnVmZmVyID0gYXdhaXQgYmxvYi5hcnJheUJ1ZmZlcigpOwogICAgICBjb25zdCBiYXNlNjQgPSBlbmNvZGUoYXJyYXlCdWZmZXIpOwogICAgICB0cmFuc3BhcmVudEJsb2JNYXAuc2V0KGlkLCBiYXNlNjQpOwogICAgICByZXR1cm4gYmFzZTY0OwogICAgfSBlbHNlIHsKICAgICAgcmV0dXJuICIiOwogICAgfQogIH0KICBjb25zdCB3b3JrZXIgPSBzZWxmOwogIHdvcmtlci5vbm1lc3NhZ2UgPSBhc3luYyBmdW5jdGlvbihlKSB7CiAgICBpZiAoIk9mZnNjcmVlbkNhbnZhcyIgaW4gZ2xvYmFsVGhpcykgewogICAgICBjb25zdCB7IGlkLCBiaXRtYXAsIHdpZHRoLCBoZWlnaHQsIGRhdGFVUkxPcHRpb25zIH0gPSBlLmRhdGE7CiAgICAgIGNvbnN0IHRyYW5zcGFyZW50QmFzZTY0ID0gZ2V0VHJhbnNwYXJlbnRCbG9iRm9yKAogICAgICAgIHdpZHRoLAogICAgICAgIGhlaWdodCwKICAgICAgICBkYXRhVVJMT3B0aW9ucwogICAgICApOwogICAgICBjb25zdCBvZmZzY3JlZW4gPSBuZXcgT2Zmc2NyZWVuQ2FudmFzKHdpZHRoLCBoZWlnaHQpOwogICAgICBjb25zdCBjdHggPSBvZmZzY3JlZW4uZ2V0Q29udGV4dCgiMmQiKTsKICAgICAgY3R4LmRyYXdJbWFnZShiaXRtYXAsIDAsIDApOwogICAgICBiaXRtYXAuY2xvc2UoKTsKICAgICAgY29uc3QgYmxvYiA9IGF3YWl0IG9mZnNjcmVlbi5jb252ZXJ0VG9CbG9iKGRhdGFVUkxPcHRpb25zKTsKICAgICAgY29uc3QgdHlwZSA9IGJsb2IudHlwZTsKICAgICAgY29uc3QgYXJyYXlCdWZmZXIgPSBhd2FpdCBibG9iLmFycmF5QnVmZmVyKCk7CiAgICAgIGNvbnN0IGJhc2U2NCA9IGVuY29kZShhcnJheUJ1ZmZlcik7CiAgICAgIGlmICghbGFzdEJsb2JNYXAuaGFzKGlkKSAmJiBhd2FpdCB0cmFuc3BhcmVudEJhc2U2NCA9PT0gYmFzZTY0KSB7CiAgICAgICAgbGFzdEJsb2JNYXAuc2V0KGlkLCBiYXNlNjQpOwogICAgICAgIHJldHVybiB3b3JrZXIucG9zdE1lc3NhZ2UoeyBpZCB9KTsKICAgICAgfQogICAgICBpZiAobGFzdEJsb2JNYXAuZ2V0KGlkKSA9PT0gYmFzZTY0KSByZXR1cm4gd29ya2VyLnBvc3RNZXNzYWdlKHsgaWQgfSk7CiAgICAgIHdvcmtlci5wb3N0TWVzc2FnZSh7CiAgICAgICAgaWQsCiAgICAgICAgdHlwZSwKICAgICAgICBiYXNlNjQsCiAgICAgICAgd2lkdGgsCiAgICAgICAgaGVpZ2h0CiAgICAgIH0pOwogICAgICBsYXN0QmxvYk1hcC5zZXQoaWQsIGJhc2U2NCk7CiAgICB9IGVsc2UgewogICAgICByZXR1cm4gd29ya2VyLnBvc3RNZXNzYWdlKHsgaWQ6IGUuZGF0YS5pZCB9KTsKICAgIH0KICB9Owp9KSgpOwovLyMgc291cmNlTWFwcGluZ1VSTD1pbWFnZS1iaXRtYXAtZGF0YS11cmwtd29ya2VyLUlKcEM3Z19iLmpzLm1hcAo=")],{type:"text/javascript;charset=utf-8"});try{if(2!==Array.from([1],(e=>2*e))[0]){var Sr=document.createElement("iframe");document.body.appendChild(Sr),Array.from=(null==(Wt=Sr.contentWindow)?void 0:Wt.Array.from)||Array.from,document.body.removeChild(Sr)}}catch(e){console.debug("Unable to override Array.from",e)}new er,function(e){e[e.NotStarted=0]="NotStarted",e[e.Running=1]="Running",e[e.Stopped=2]="Stopped"}(Ar||(Ar={}));class kr{constructor(e){Xt(this,"fileName"),Xt(this,"functionName"),Xt(this,"lineNumber"),Xt(this,"columnNumber"),this.fileName=e.fileName||"",this.functionName=e.functionName||"",this.lineNumber=e.lineNumber,this.columnNumber=e.columnNumber}toString(){var e=this.lineNumber||"",t=this.columnNumber||"";return this.functionName?this.functionName+" ("+this.fileName+":"+e+":"+t+")":this.fileName+":"+e+":"+t}}var Nr=/(^|@)\S+:\d+/,Rr=/^\s*at .*(\S+:\d+|\(native\))/m,Tr=/^(eval@)?(\[native code])?$/,Fr={parse:function(e){return e?void 0!==e.stacktrace||void 0!==e["opera#sourceloc"]?this.parseOpera(e):e.stack&&e.stack.match(Rr)?this.parseV8OrIE(e):e.stack?this.parseFFOrSafari(e):(console.warn("[console-record-plugin]: Failed to parse error object:",e),[]):[]},extractLocation:function(e){if(-1===e.indexOf(":"))return[e];var t=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(e.replace(/[()]/g,""));if(!t)throw new Error("Cannot parse given url: "+e);return[t[1],t[2]||void 0,t[3]||void 0]},parseV8OrIE:function(e){return e.stack.split("\n").filter((function(e){return!!e.match(Rr)}),this).map((function(e){e.indexOf("(eval ")>-1&&(e=e.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(\),.*$)/g,""));var t=e.replace(/^\s+/,"").replace(/\(eval code/g,"("),r=t.match(/ (\((.+):(\d+):(\d+)\)$)/),n=(t=r?t.replace(r[0],""):t).split(/\s+/).slice(1),i=this.extractLocation(r?r[1]:n.pop()),o=n.join(" ")||void 0,a=["eval","<anonymous>"].indexOf(i[0])>-1?void 0:i[0];return new kr({functionName:o,fileName:a,lineNumber:i[1],columnNumber:i[2]})}),this)},parseFFOrSafari:function(e){return e.stack.split("\n").filter((function(e){return!e.match(Tr)}),this).map((function(e){if(e.indexOf(" > eval")>-1&&(e=e.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),-1===e.indexOf("@")&&-1===e.indexOf(":"))return new kr({functionName:e});var t=/((.*".+"[^@]*)?[^@]*)(?:@)/,r=e.match(t),n=r&&r[1]?r[1]:void 0,i=this.extractLocation(e.replace(t,""));return new kr({functionName:n,fileName:i[0],lineNumber:i[1],columnNumber:i[2]})}),this)},parseOpera:function(e){return!e.stacktrace||e.message.indexOf("\n")>-1&&e.message.split("\n").length>e.stacktrace.split("\n").length?this.parseOpera9(e):e.stack?this.parseOpera11(e):this.parseOpera10(e)},parseOpera9:function(e){for(var t=/Line (\d+).*script (?:in )?(\S+)/i,r=e.message.split("\n"),n=[],i=2,o=r.length;i<o;i+=2){var a=t.exec(r[i]);a&&n.push(new kr({fileName:a[2],lineNumber:parseFloat(a[1])}))}return n},parseOpera10:function(e){for(var t=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,r=e.stacktrace.split("\n"),n=[],i=0,o=r.length;i<o;i+=2){var a=t.exec(r[i]);a&&n.push(new kr({functionName:a[3]||void 0,fileName:a[2],lineNumber:parseFloat(a[1])}))}return n},parseOpera11:function(e){return e.stack.split("\n").filter((function(e){return!!e.match(Nr)&&!e.match(/^Error created at/)}),this).map((function(e){var t=e.split("@"),r=this.extractLocation(t.pop()),n=(t.shift()||"").replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;return new kr({functionName:n,fileName:r[0],lineNumber:r[1],columnNumber:r[2]})}),this)}};function Mr(e){if(!e||!e.outerHTML)return"";for(var t="";e.parentElement;){var r=e.localName;if(!r)break;r=r.toLowerCase();var n=e.parentElement,i=[];if(n.children&&n.children.length>0)for(var o=0;o<n.children.length;o++){var a=n.children[o];a.localName&&a.localName.toLowerCase&&a.localName.toLowerCase()===r&&i.push(a)}i.length>1&&(r+=":eq("+i.indexOf(e)+")"),t=r+(t?">"+t:""),e=n}return t}function Br(e){return"[object Object]"===Object.prototype.toString.call(e)}function Or(e,t){if(0===t)return!0;var r=Object.keys(e);for(var n of r)if(Br(e[n])&&Or(e[n],t-1))return!0;return!1}function Zr(e,t){var r={numOfKeysLimit:50,depthOfLimit:4};Object.assign(r,t);var n=[],i=[];return JSON.stringify(e,(function(e,t){if(n.length>0){var o=n.indexOf(this);~o?n.splice(o+1):n.push(this),~o?i.splice(o,1/0,e):i.push(e),~n.indexOf(t)&&(t=n[0]===t?"[Circular ~]":"[Circular ~."+i.slice(0,n.indexOf(t)).join(".")+"]")}else n.push(t);if(null===t)return t;if(void 0===t)return"undefined";if(function(e){if(Br(e)&&Object.keys(e).length>r.numOfKeysLimit)return!0;if("function"==typeof e)return!0;if(Br(e)&&Or(e,r.depthOfLimit))return!0;return!1}(t))return function(e){var t=e.toString();r.stringLengthLimit&&t.length>r.stringLengthLimit&&(t=t.slice(0,r.stringLengthLimit)+"...");return t}(t);if("bigint"==typeof t)return t.toString()+"n";if(t instanceof Event){var a={};for(var s in t){var u=t[s];Array.isArray(u)?a[s]=Mr(u.length?u[0]:null):a[s]=u}return a}return t instanceof Node?t instanceof HTMLElement?t?t.outerHTML:"":t.nodeName:t instanceof Error?t.stack?t.stack+"\nEnd of stack for Error object":t.name+": "+t.message:t}))}var Yr={level:["assert","clear","count","countReset","debug","dir","dirxml","error","group","groupCollapsed","groupEnd","info","log","table","time","timeEnd","timeLog","trace","warn"],lengthThreshold:1e3,logger:"console"};function xr(e,t,r){var n,i=r?Object.assign({},Yr,r):Yr,o=i.logger;if(!o)return()=>{};n="string"==typeof o?t[o]:o;var a=0,s=!1,u=[];if(i.level.includes("error")){var l=t=>{var r=t.message,n=t.error,o=Fr.parse(n).map((e=>e.toString())),a=[Zr(r,i.stringifyOptions)];e({level:"error",trace:o,payload:a})};t.addEventListener("error",l),u.push((()=>{t.removeEventListener("error",l)}));var c=t=>{var r,n;t.reason instanceof Error?n=[Zr("Uncaught (in promise) "+(r=t.reason).name+": "+r.message,i.stringifyOptions)]:(r=new Error,n=[Zr("Uncaught (in promise)",i.stringifyOptions),Zr(t.reason,i.stringifyOptions)]);var o=Fr.parse(r).map((e=>e.toString()));e({level:"error",trace:o,payload:n})};t.addEventListener("unhandledrejection",c),u.push((()=>{t.removeEventListener("unhandledrejection",c)}))}for(var d of i.level)u.push(h(n,d));return()=>{u.forEach((e=>e()))};function h(t,r){var n=this;return t[r]?yr.patch(t,r,(t=>function(){for(var o=arguments.length,u=new Array(o),l=0;l<o;l++)u[l]=arguments[l];if(t.apply(n,u),!("assert"===r&&u[0]||s)){s=!0;try{var c=Fr.parse(new Error).map((e=>e.toString())).splice(1),d=("assert"===r?u.slice(1):u).map((e=>Zr(e,i.stringifyOptions)));++a<i.lengthThreshold?e({level:r,trace:c,payload:d}):a===i.lengthThreshold&&e({level:"warn",trace:[],payload:[Zr("The number of log records reached the threshold.")]})}catch(e){t("rrweb logger error:",e,...u)}finally{s=!1}}})):()=>{}}}var Gr=e=>({name:"rrweb/console@1",observer:xr,options:e}),Er="undefined"!=typeof window?window:void 0,Wr="undefined"!=typeof globalThis?globalThis:Er,Lr=Array.prototype.forEach,Xr=null==Wr?void 0:Wr.navigator;null==Wr||Wr.document,null==Wr||Wr.location,null==Wr||Wr.fetch,null!=Wr&&Wr.XMLHttpRequest&&"withCredentials"in new Wr.XMLHttpRequest&&Wr.XMLHttpRequest,null==Wr||Wr.AbortController,null==Xr||Xr.userAgent;var Vr=null!=Er?Er:{},Dr=Array.isArray,Kr=Object.prototype,Jr=Kr.hasOwnProperty,zr=Kr.toString,Hr=Dr||function(e){return"[object Array]"===zr.call(e)},jr=e=>"function"==typeof e,_r=e=>e===Object(e)&&!Hr(e),Ur=e=>void 0===e,Pr=e=>"[object String]"==zr.call(e),qr=e=>null===e,Qr=e=>Ur(e)||qr(e),$r=e=>"[object Boolean]"===zr.call(e),en=e=>e instanceof Document,tn=e=>e instanceof FormData,rn=e=>{var t={t:function(t){if(Er&&Vr.POSTHOG_DEBUG&&!Ur(Er.console)&&Er.console){for(var r=("__rrweb_original__"in Er.console[t]?Er.console[t].__rrweb_original__:Er.console[t]),n=arguments.length,i=new Array(n>1?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];r(e,...i)}},info:function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];t.t("log",...r)},warn:function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];t.t("warn",...r)},error:function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];t.t("error",...r)},critical:function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];console.error(e,...r)},uninitializedWarning:e=>{t.error("You must initialize PostHog before calling "+e)},createLogger:t=>rn(e+" "+t)};return t},nn=rn("[PostHog.js]").createLogger,on={};function an(e,t,r){if(!Qr(e)){if(Hr(e))return function(e,t,r){if(Hr(e))if(Lr&&e.forEach===Lr)e.forEach(t,r);else if("length"in e&&e.length===+e.length)for(var n=0,i=e.length;n<i;n++)if(n in e&&t.call(r,e[n],n)===on)return}(e,t,r);if(tn(e)){for(var n of e.entries())if(t.call(r,n[1],n[0])===on)return}else for(var i in e)if(Jr.call(e,i)&&t.call(r,e[i],i)===on)return}}var sn=function(e,t){var r,n;void 0===t&&(t="&");var i=[];return an(e,(function(e,t){Ur(e)||Ur(t)||"undefined"===t||(r=encodeURIComponent(e instanceof File?e.name:e.toString()),n=encodeURIComponent(t),i[i.length]=n+"="+r)})),i.join(t)};function un(e,t,r){try{if(!(t in e))return()=>{};var n=e[t],i=r(n);return jr(i)&&(i.prototype=i.prototype||{},Object.defineProperties(i,{__posthog_wrapped__:{enumerable:!1,value:!0}})),e[t]=i,()=>{e[t]=n}}catch(e){return()=>{}}}function ln(e,t){var r,n=function(e){try{return"string"==typeof e?new URL(e).hostname:"url"in e?new URL(e.url).hostname:e.hostname}catch(e){return null}}(e),i={hostname:n,isHostDenied:!1};if(null==(r=t.payloadHostDenyList)||!r.length||null==n||!n.trim().length)return i;for(var o of t.payloadHostDenyList)if(n.endsWith(o))return{hostname:n,isHostDenied:!0};return i}var cn={initiatorTypes:["audio","beacon","body","css","early-hint","embed","fetch","frame","iframe","icon","image","img","input","link","navigation","object","ping","script","track","video","xmlhttprequest"],maskRequestFn:e=>e,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:["first-input","navigation","paint","resource"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[".lr-ingest.io",".ingest.sentry.io",".clarity.ms","analytics.google.com","bam.nr-data.net"]},dn=nn("[Recorder]"),hn=e=>"navigation"===e.entryType,fn=e=>"resource"===e.entryType;function mn(e,t,r){if(r.recordInitialRequests){var n=t.performance.getEntries().filter((e=>hn(e)||fn(e)&&r.initiatorTypes.includes(e.initiatorType)));e({requests:n.flatMap((e=>bn({entry:e,method:void 0,status:void 0,networkRequest:{},isInitial:!0}))),isInitial:!0})}var i=new t.PerformanceObserver((t=>{var n=t.getEntries().filter((e=>hn(e)||fn(e)&&r.initiatorTypes.includes(e.initiatorType)&&(e=>!r.recordBody&&!r.recordHeaders||"xmlhttprequest"!==e.initiatorType&&"fetch"!==e.initiatorType)(e)));e({requests:n.flatMap((e=>bn({entry:e,method:void 0,status:void 0,networkRequest:{}})))})})),o=PerformanceObserver.supportedEntryTypes.filter((e=>r.performanceEntryTypeToObserve.includes(e)));return i.observe({entryTypes:o}),()=>{i.disconnect()}}function vn(e,t){return!!t&&($r(t)||t[e])}function pn(e){var{type:t,recordBody:r,headers:n,url:i}=e;function o(e){var t=Object.keys(n).find((e=>"content-type"===e.toLowerCase())),r=t&&n[t];return e.some((e=>null==r?void 0:r.includes(e)))}if(!r)return!1;if(function e(t){try{return"string"==typeof t?t.startsWith("blob:"):t instanceof URL?"blob:"===t.protocol:t instanceof Request&&e(t.url)}catch(e){return!1}}(i))return!1;if($r(r))return!0;if(Hr(r))return o(r);var a=r[t];return $r(a)?a:o(a)}function gn(e,t,r,n,i,o){return In.apply(this,arguments)}function In(){return In=t((function*(e,t,r,n,i,o){if(void 0===o&&(o=0),o>10)return dn.warn("Failed to get performance entry for request",{url:r,initiatorType:t}),null;var a=function(e,t){for(var r=e.length-1;r>=0;r-=1)if(t(e[r]))return e[r]}(e.performance.getEntriesByName(r),(e=>fn(e)&&e.initiatorType===t&&(Ur(n)||e.startTime>=n)&&(Ur(i)||e.startTime<=i)));return a||(yield new Promise((e=>setTimeout(e,50*o))),gn(e,t,r,n,i,o+1))})),In.apply(this,arguments)}function yn(e){var{body:t,options:r,url:n}=e;if(Qr(t))return null;var{hostname:i,isHostDenied:o}=ln(n,r);if(o)return i+" is in deny list";if(Pr(t))return t;if(en(t))return t.textContent;if(tn(t))return sn(t);if(_r(t))try{return JSON.stringify(t)}catch(e){return"[SessionReplay] Failed to stringify response object"}return"[SessionReplay] Cannot read body of type "+toString.call(t)}var Cn=e=>!qr(e)&&("navigation"===e.entryType||"resource"===e.entryType);function bn(e){var{entry:t,method:n,status:i,networkRequest:o,isInitial:a,start:s,end:u,url:l,initiatorType:c}=e;s=t?t.startTime:s,u=t?t.responseEnd:u;var d=Math.floor(Date.now()-performance.now()),h=Math.floor(d+(s||0)),f=[r({},t?t.toJSON():{name:l},{startTime:Ur(s)?void 0:Math.round(s),endTime:Ur(u)?void 0:Math.round(u),timeOrigin:d,timestamp:h,method:n,initiatorType:c||(t?t.initiatorType:void 0),status:i,requestHeaders:o.requestHeaders,requestBody:o.requestBody,responseHeaders:o.responseHeaders,responseBody:o.responseBody,isInitial:a})];if(Cn(t))for(var m of t.serverTiming||[])f.push({timeOrigin:d,timestamp:h,startTime:Math.round(t.startTime),name:m.name,duration:m.duration,entryType:"serverTiming"});return f}var wn=["video/","audio/"];function An(e){return new Promise(((t,r)=>{var n=setTimeout((()=>t("[SessionReplay] Timeout while trying to read body")),500);try{e.clone().text().then((e=>t(e)),(e=>r(e))).finally((()=>clearTimeout(n)))}catch(e){clearTimeout(n),t("[SessionReplay] Failed to read body")}}))}function Sn(){return(Sn=t((function*(e){var{r:t,options:r,url:n}=e,{hostname:i,isHostDenied:o}=ln(n,r);return o?Promise.resolve(i+" is in deny list"):An(t)}))).apply(this,arguments)}function kn(){return(kn=t((function*(e){var{r:t,options:r,url:n}=e,i=function(e){var t,{r:r,options:n,url:i}=e;if("chunked"===r.headers.get("Transfer-Encoding"))return"Chunked Transfer-Encoding is not supported";var o=null==(t=r.headers.get("Content-Type"))?void 0:t.toLowerCase(),a=wn.some((e=>null==o?void 0:o.startsWith(e)));if(o&&a)return"Content-Type "+o+" is not supported";var{hostname:s,isHostDenied:u}=ln(i,n);return u?s+" is in deny list":null}({r:t,options:r,url:n});return qr(i)?An(t):Promise.resolve(i)}))).apply(this,arguments)}function Nn(e,r,n){if(!n.initiatorTypes.includes("fetch"))return()=>{};var i=vn("request",n.recordHeaders),o=vn("response",n.recordHeaders),a=un(r,"fetch",(a=>function(){var s=t((function*(t,s){var u,l,c,d=new Request(t,s),h={};try{var f={};d.headers.forEach(((e,t)=>{f[t]=e})),i&&(h.requestHeaders=f),pn({type:"request",headers:f,url:t,recordBody:n.recordBody})&&(h.requestBody=yield function(e){return Sn.apply(this,arguments)}({r:d,options:n,url:t})),l=r.performance.now(),u=yield a(d),c=r.performance.now();var m={};return u.headers.forEach(((e,t)=>{m[t]=e})),o&&(h.responseHeaders=m),pn({type:"response",headers:m,url:t,recordBody:n.recordBody})&&(h.responseBody=yield function(e){return kn.apply(this,arguments)}({r:u,options:n,url:t})),u}finally{gn(r,"fetch",d.url,l,c).then((t=>{var r,n=bn({entry:t,method:d.method,status:null==(r=u)?void 0:r.status,networkRequest:h,start:l,end:c,url:d.url,initiatorType:"fetch"});e({requests:n})})).catch((()=>{}))}}));return function(e,t){return s.apply(this,arguments)}}()));return()=>{a()}}var Rn=null;function Tn(e,t,n){if(!("performance"in t))return()=>{};if(Rn)return dn.warn("Network observer already initialised, doing nothing"),()=>{};var i=n?Object.assign({},cn,n):cn,o=t=>{var n=[];t.requests.forEach((e=>{var t=i.maskRequestFn(e);t&&n.push(t)})),n.length>0&&e(r({},t,{requests:n}))},a=mn(o,t,i),s=()=>{},u=()=>{};return(i.recordHeaders||i.recordBody)&&(s=function(e,t,r){if(!r.initiatorTypes.includes("xmlhttprequest"))return()=>{};var n=vn("request",r.recordHeaders),i=vn("response",r.recordHeaders),o=un(t.XMLHttpRequest.prototype,"open",(o=>function(a,s,u,l,c){void 0===u&&(u=!0);var d,h,f=this,m=new Request(s),v={},p={},g=f.setRequestHeader.bind(f);f.setRequestHeader=(e,t)=>(p[e]=t,g(e,t)),n&&(v.requestHeaders=p);var I=f.send.bind(f);f.send=e=>(pn({type:"request",headers:p,url:s,recordBody:r.recordBody})&&(v.requestBody=yn({body:e,options:r,url:s})),d=t.performance.now(),I(e)),f.addEventListener("readystatechange",(()=>{if(f.readyState===f.DONE){h=t.performance.now();var n={};f.getAllResponseHeaders().trim().split(/[\r\n]+/).forEach((e=>{var t=e.split(": "),r=t.shift(),i=t.join(": ");r&&(n[r]=i)})),i&&(v.responseHeaders=n),pn({type:"response",headers:n,url:s,recordBody:r.recordBody})&&(v.responseBody=yn({body:f.response,options:r,url:s})),gn(t,"xmlhttprequest",m.url,d,h).then((t=>{var r=bn({entry:t,method:a,status:null==f?void 0:f.status,networkRequest:v,start:d,end:h,url:s.toString(),initiatorType:"xmlhttprequest"});e({requests:r})})).catch((()=>{}))}})),o.call(f,a,s,u,l,c)}));return()=>{o()}}(o,t,i),u=Nn(o,t,i)),Rn=()=>{a(),s(),u()}}var Fn=e=>({name:"rrweb/network@1",observer:Tn,options:e});Vr.__PosthogExtensions__=Vr.__PosthogExtensions__||{},Vr.__PosthogExtensions__.rrwebPlugins={getRecordConsolePlugin:Gr,getRecordNetworkPlugin:Fn},Vr.__PosthogExtensions__.rrweb={record:Et,version:"v2"},Vr.rrweb={record:Et,version:"v2"},Vr.rrwebConsoleRecord={getRecordConsolePlugin:Gr},Vr.getRecordNetworkPlugin=Fn}();
//# sourceMappingURL=recorder-v2.js.map
