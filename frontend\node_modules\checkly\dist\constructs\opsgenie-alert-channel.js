"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpsgenieAlertChannel = void 0;
const alert_channel_1 = require("./alert-channel");
const project_1 = require("./project");
/**
 * Creates an Opsgenie Alert Channel
 *
 * @remarks
 *
 * This class make use of the Alert Channel endpoints.
 */
class OpsgenieAlertChannel extends alert_channel_1.AlertChannel {
    name;
    apiKey;
    region;
    priority;
    /**
     * Constructs the Opsgenie Alert Channel instance
     *
     * @param logicalId unique project-scoped resource name identification
     * @param props Opsgenie alert channel configuration properties
     *
     * {@link https://checklyhq.com/docs/cli/constructs-reference/#opsgeniealertchannel Read more in the docs}
     */
    constructor(logicalId, props) {
        super(logicalId, props);
        this.name = props.name;
        this.apiKey = props.apiKey;
        this.region = props.region;
        this.priority = props.priority;
        project_1.Session.registerConstruct(this);
    }
    synthesize() {
        return {
            ...super.synthesize(),
            type: 'OPSGENIE',
            config: {
                name: this.name,
                apiKey: this.apiKey,
                region: this.region,
                priority: this.priority,
            },
        };
    }
}
exports.OpsgenieAlertChannel = OpsgenieAlertChannel;
//# sourceMappingURL=opsgenie-alert-channel.js.map