"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/es-UY.ts
var es_UY_exports = {};
__export(es_UY_exports, {
  esUY: () => esUY
});
module.exports = __toCommonJS(es_UY_exports);
var esUY = {
  locale: "es-UY",
  backButton: "Atr\xE1s",
  badge__default: "Predeterminado",
  badge__otherImpersonatorDevice: "Otro dispositivo de suplantaci\xF3n",
  badge__primary: "Principal",
  badge__requiresAction: "Requiere acci\xF3n",
  badge__thisDevice: "Este dispositivo",
  badge__unverified: "No verificado",
  badge__userDevice: "Dispositivo del usuario",
  badge__you: "Vos",
  createOrganization: {
    formButtonSubmit: "Crear organizaci\xF3n",
    invitePage: {
      formButtonReset: "Omitir"
    },
    title: "Crear organizaci\xF3n"
  },
  dates: {
    lastDay: "Ayer a las {{ date | timeString('es-UY') }}",
    next6Days: "{{ date | weekday('es-UY','long') }} a las {{ date | timeString('es-UY') }}",
    nextDay: "Ma\xF1ana a las {{ date | timeString('es-UY') }}",
    numeric: "{{ date | numeric('es-UY') }}",
    previous6Days: "El pasado {{ date | weekday('es-UY','long') }} a las {{ date | timeString('es-UY') }}",
    sameDay: "Hoy a las {{ date | timeString('es-UY') }}"
  },
  dividerText: "o",
  footerActionLink__useAnotherMethod: "Usar otro m\xE9todo",
  footerPageLink__help: "Ayuda",
  footerPageLink__privacy: "Privacidad",
  footerPageLink__terms: "T\xE9rminos",
  formButtonPrimary: "Continuar",
  formButtonPrimary__verify: "Verificar",
  formFieldAction__forgotPassword: "\xBFOlvidaste tu contrase\xF1a?",
  formFieldError__matchingPasswords: "Las contrase\xF1as coinciden.",
  formFieldError__notMatchingPasswords: "Las contrase\xF1as no coinciden.",
  formFieldError__verificationLinkExpired: "El enlace de verificaci\xF3n ha expirado. Por favor, solicit\xE1 un nuevo enlace.",
  formFieldHintText__optional: "Opcional",
  formFieldHintText__slug: "Un slug es una identificaci\xF3n legible por humanos que debe ser \xFAnica. A menudo se utiliza en URLs.",
  formFieldInputPlaceholder__backupCode: "Ingres\xE1 el c\xF3digo de respaldo",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Eliminar cuenta",
  formFieldInputPlaceholder__emailAddress: "Ingres\xE1 tu direcci\xF3n de correo electr\xF3nico",
  formFieldInputPlaceholder__emailAddress_username: "Ingres\xE1 correo electr\xF3nico o nombre de usuario",
  formFieldInputPlaceholder__emailAddresses: "<EMAIL>, <EMAIL>",
  formFieldInputPlaceholder__firstName: "Nombre",
  formFieldInputPlaceholder__lastName: "Apellido",
  formFieldInputPlaceholder__organizationDomain: "example.com",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "<EMAIL>",
  formFieldInputPlaceholder__organizationName: "Nombre de la organizaci\xF3n",
  formFieldInputPlaceholder__organizationSlug: "mi-org",
  formFieldInputPlaceholder__password: "Ingres\xE1 tu contrase\xF1a",
  formFieldInputPlaceholder__phoneNumber: "Ingres\xE1 tu n\xFAmero de tel\xE9fono",
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__automaticInvitations: "Habilitar invitaciones autom\xE1ticas para este dominio",
  formFieldLabel__backupCode: "C\xF3digo de respaldo",
  formFieldLabel__confirmDeletion: "Confirmaci\xF3n",
  formFieldLabel__confirmPassword: "Confirmar contrase\xF1a",
  formFieldLabel__currentPassword: "Contrase\xF1a actual",
  formFieldLabel__emailAddress: "Correo electr\xF3nico",
  formFieldLabel__emailAddress_username: "Correo electr\xF3nico o nombre de usuario",
  formFieldLabel__emailAddresses: "Correos electr\xF3nicos",
  formFieldLabel__firstName: "Nombre",
  formFieldLabel__lastName: "Apellido",
  formFieldLabel__newPassword: "Nueva contrase\xF1a",
  formFieldLabel__organizationDomain: "Dominio",
  formFieldLabel__organizationDomainDeletePending: "Eliminar invitaciones y sugerencias pendientes",
  formFieldLabel__organizationDomainEmailAddress: "Correo electr\xF3nico de verificaci\xF3n",
  formFieldLabel__organizationDomainEmailAddressDescription: "Ingres\xE1 un correo electr\xF3nico bajo este dominio para recibir un c\xF3digo y verificar el dominio.",
  formFieldLabel__organizationName: "Nombre",
  formFieldLabel__organizationSlug: "Slug",
  formFieldLabel__passkeyName: "Nombre de la clave de acceso",
  formFieldLabel__password: "Contrase\xF1a",
  formFieldLabel__phoneNumber: "N\xFAmero de tel\xE9fono",
  formFieldLabel__role: "Rol",
  formFieldLabel__signOutOfOtherSessions: "Cerrar sesi\xF3n en todos los dem\xE1s dispositivos",
  formFieldLabel__username: "Nombre de usuario",
  impersonationFab: {
    action__signOut: "Cerrar sesi\xF3n",
    title: "Conectado como {{identifier}}"
  },
  maintenanceMode: "Actualmente estamos en mantenimiento, pero no te preocupes, no tomar\xE1 m\xE1s que unos minutos.",
  membershipRole__admin: "Administrador",
  membershipRole__basicMember: "Miembro",
  membershipRole__guestMember: "Invitado",
  organizationList: {
    action__createOrganization: "Crear organizaci\xF3n",
    action__invitationAccept: "Unirse",
    action__suggestionsAccept: "Solicitar unirse",
    createOrganization: "Crear organizaci\xF3n",
    invitationAcceptedLabel: "Unido",
    subtitle: "para continuar a {{applicationName}}",
    suggestionsAcceptedLabel: "Aprobaci\xF3n pendiente",
    title: "Eleg\xED una cuenta",
    titleWithoutPersonal: "Eleg\xED una organizaci\xF3n"
  },
  organizationProfile: {
    badge__automaticInvitation: "Invitaciones autom\xE1ticas",
    badge__automaticSuggestion: "Sugerencias autom\xE1ticas",
    badge__manualInvitation: "Sin inscripci\xF3n autom\xE1tica",
    badge__unverified: "No verificado",
    createDomainPage: {
      subtitle: "Agreg\xE1 el dominio para verificar. Los usuarios con correos electr\xF3nicos de este dominio pueden unirse autom\xE1ticamente a la organizaci\xF3n o solicitar unirse.",
      title: "Agregar dominio"
    },
    invitePage: {
      detailsTitle__inviteFailed: "No se pudieron enviar las invitaciones. Ya existen invitaciones pendientes para los siguientes correos electr\xF3nicos: {{email_addresses}}.",
      formButtonPrimary__continue: "Enviar invitaciones",
      selectDropdown__role: "Seleccionar rol",
      subtitle: "Ingres\xE1 o peg\xE1 uno o m\xE1s correos electr\xF3nicos, separados por espacios o comas.",
      successMessage: "Invitaciones enviadas con \xE9xito",
      title: "Invitar nuevos miembros"
    },
    membersPage: {
      action__invite: "Invitar",
      action__search: "Buscar",
      activeMembersTab: {
        menuAction__remove: "Eliminar miembro",
        tableHeader__actions: "Acciones",
        tableHeader__joined: "Se uni\xF3",
        tableHeader__role: "Rol",
        tableHeader__user: "Usuario"
      },
      detailsTitle__emptyRow: "No hay miembros para mostrar",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Invit\xE1 a usuarios conectando un dominio de correo electr\xF3nico con tu organizaci\xF3n. Cualquiera que se registre con un correo que coincida podr\xE1 unirse a la organizaci\xF3n en cualquier momento.",
          headerTitle: "Invitaciones autom\xE1ticas",
          primaryButton: "Gestionar dominios verificados"
        },
        table__emptyRow: "No hay invitaciones para mostrar"
      },
      invitedMembersTab: {
        menuAction__revoke: "Revocar invitaci\xF3n",
        tableHeader__invited: "Invitado"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Los usuarios que se registren con un correo que coincida podr\xE1n ver una sugerencia para solicitar unirse a tu organizaci\xF3n.",
          headerTitle: "Sugerencias autom\xE1ticas",
          primaryButton: "Gestionar dominios verificados"
        },
        menuAction__approve: "Aprobar",
        menuAction__reject: "Rechazar",
        tableHeader__requested: "Acceso solicitado",
        table__emptyRow: "No hay solicitudes para mostrar"
      },
      start: {
        headerTitle__invitations: "Invitaciones",
        headerTitle__members: "Miembros",
        headerTitle__requests: "Solicitudes"
      }
    },
    navbar: {
      description: "Gestion\xE1 tu organizaci\xF3n.",
      general: "General",
      members: "Miembros",
      title: "Organizaci\xF3n"
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Escrib\xED "{{organizationName}}" abajo para continuar.',
          messageLine1: "\xBFEst\xE1s seguro de que quer\xE9s eliminar esta organizaci\xF3n?",
          messageLine2: "Esta acci\xF3n es permanente e irreversible.",
          successMessage: "Has eliminado la organizaci\xF3n.",
          title: "Eliminar organizaci\xF3n"
        },
        leaveOrganization: {
          actionDescription: 'Escrib\xED "{{organizationName}}" abajo para continuar.',
          messageLine1: "\xBFEst\xE1s seguro de que quer\xE9s abandonar esta organizaci\xF3n? Perder\xE1s el acceso a la organizaci\xF3n y a sus aplicaciones.",
          messageLine2: "Esta acci\xF3n es permanente e irreversible.",
          successMessage: "Has abandonado la organizaci\xF3n.",
          title: "Abandonar organizaci\xF3n"
        },
        title: "Peligro"
      },
      domainSection: {
        menuAction__manage: "Gestionar",
        menuAction__remove: "Eliminar",
        menuAction__verify: "Verificar",
        primaryButton: "Agregar dominio",
        subtitle: "Permit\xED que los usuarios se unan a la organizaci\xF3n autom\xE1ticamente o soliciten unirse en base a un dominio de correo verificado.",
        title: "Dominios verificados"
      },
      successMessage: "La organizaci\xF3n ha sido actualizada.",
      title: "Actualizar perfil"
    },
    removeDomainPage: {
      messageLine1: "El dominio de correo {{domain}} ser\xE1 eliminado.",
      messageLine2: "Los usuarios no podr\xE1n unirse autom\xE1ticamente a la organizaci\xF3n despu\xE9s de esto.",
      successMessage: "{{domain}} ha sido eliminado.",
      title: "Eliminar dominio"
    },
    start: {
      headerTitle__general: "General",
      headerTitle__members: "Miembros",
      profileSection: {
        primaryButton: "Actualizar perfil",
        title: "Perfil de la organizaci\xF3n",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Eliminar este dominio afectar\xE1 a los usuarios invitados.",
        removeDomainActionLabel__remove: "Eliminar dominio",
        removeDomainSubtitle: "Eliminar este dominio de tus dominios verificados",
        removeDomainTitle: "Eliminar dominio"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Los usuarios son invitados autom\xE1ticamente a unirse a la organizaci\xF3n al registrarse y pueden unirse en cualquier momento.",
        automaticInvitationOption__label: "Invitaciones autom\xE1ticas",
        automaticSuggestionOption__description: "Los usuarios reciben una sugerencia para solicitar unirse, pero deben ser aprobados por un administrador antes de poder unirse a la organizaci\xF3n.",
        automaticSuggestionOption__label: "Sugerencias autom\xE1ticas",
        calloutInfoLabel: "Cambiar el modo de inscripci\xF3n solo afectar\xE1 a los nuevos usuarios.",
        calloutInvitationCountLabel: "Invitaciones pendientes enviadas a usuarios: {{count}}",
        calloutSuggestionCountLabel: "Sugerencias pendientes enviadas a usuarios: {{count}}",
        manualInvitationOption__description: "Los usuarios solo pueden ser invitados manualmente a la organizaci\xF3n.",
        manualInvitationOption__label: "Sin inscripci\xF3n autom\xE1tica",
        subtitle: "Eleg\xED c\xF3mo los usuarios de este dominio pueden unirse a la organizaci\xF3n."
      },
      start: {
        headerTitle__danger: "Peligro",
        headerTitle__enrollment: "Opciones de inscripci\xF3n"
      },
      subtitle: "El dominio {{domain}} est\xE1 verificado. Continu\xE1 seleccionando el modo de inscripci\xF3n.",
      title: "Actualizar {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Ingres\xE1 el c\xF3digo de verificaci\xF3n enviado a tu correo electr\xF3nico",
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "\xBFNo recibiste un c\xF3digo? Reenviar",
      subtitle: "El dominio {{domainName}} necesita ser verificado por correo electr\xF3nico.",
      subtitleVerificationCodeScreen: "Se envi\xF3 un c\xF3digo de verificaci\xF3n a {{emailAddress}}. Ingres\xE1 el c\xF3digo para continuar.",
      title: "Verificar dominio"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Crear organizaci\xF3n",
    action__invitationAccept: "Unirse",
    action__manageOrganization: "Gestionar",
    action__suggestionsAccept: "Solicitar unirse",
    notSelected: "Ninguna organizaci\xF3n seleccionada",
    personalWorkspace: "Cuenta personal",
    suggestionsAcceptedLabel: "Aprobaci\xF3n pendiente"
  },
  paginationButton__next: "Siguiente",
  paginationButton__previous: "Anterior",
  paginationRowText__displaying: "Mostrando",
  paginationRowText__of: "de",
  reverification: {
    alternativeMethods: {
      actionLink: "Obtener ayuda",
      actionText: "\xBFNo ten\xE9s ninguno de estos?",
      blockButton__backupCode: "Usar un c\xF3digo de respaldo",
      blockButton__emailCode: "Enviar c\xF3digo por correo a {{identifier}}",
      blockButton__password: "Continuar con tu contrase\xF1a",
      blockButton__phoneCode: "Enviar c\xF3digo SMS a {{identifier}}",
      blockButton__totp: "Usar tu aplicaci\xF3n autenticadora",
      getHelp: {
        blockButton__emailSupport: "Soporte por correo",
        content: "Si ten\xE9s problemas para verificar tu cuenta, envi\xE1nos un correo y trabajaremos para restaurar el acceso lo antes posible.",
        title: "Obtener ayuda"
      },
      subtitle: "\xBFTen\xE9s problemas? Pod\xE9s usar cualquiera de estos m\xE9todos para la verificaci\xF3n.",
      title: "Usar otro m\xE9todo"
    },
    backupCodeMfa: {
      subtitle: "Ingres\xE1 el c\xF3digo de respaldo que recibiste al configurar la autenticaci\xF3n de dos pasos",
      title: "Ingres\xE1 un c\xF3digo de respaldo"
    },
    emailCode: {
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "\xBFNo recibiste un c\xF3digo? Reenviar",
      subtitle: "Ingres\xE1 el c\xF3digo enviado a tu correo para continuar",
      title: "Verificaci\xF3n requerida"
    },
    noAvailableMethods: {
      message: "No se puede proceder con la verificaci\xF3n. No hay un factor de autenticaci\xF3n adecuado configurado",
      subtitle: "Ocurri\xF3 un error",
      title: "No se puede verificar tu cuenta"
    },
    password: {
      actionLink: "Usar otro m\xE9todo",
      subtitle: "Ingres\xE1 tu contrase\xF1a para continuar",
      title: "Verificaci\xF3n requerida"
    },
    phoneCode: {
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "\xBFNo recibiste un c\xF3digo? Reenviar",
      subtitle: "Ingres\xE1 el c\xF3digo enviado a tu tel\xE9fono para continuar",
      title: "Verificaci\xF3n requerida"
    },
    phoneCodeMfa: {
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "\xBFNo recibiste un c\xF3digo? Reenviar",
      subtitle: "Ingres\xE1 el c\xF3digo enviado a tu tel\xE9fono para continuar",
      title: "Verificaci\xF3n requerida"
    },
    totpMfa: {
      formTitle: "C\xF3digo de verificaci\xF3n",
      subtitle: "Ingres\xE1 el c\xF3digo generado por tu aplicaci\xF3n autenticadora para continuar",
      title: "Verificaci\xF3n requerida"
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Agregar cuenta",
      action__signOutAll: "Cerrar sesi\xF3n en todas las cuentas",
      subtitle: "Seleccion\xE1 la cuenta con la que deseas continuar.",
      title: "Eleg\xED una cuenta"
    },
    alternativeMethods: {
      actionLink: "Obtener ayuda",
      actionText: "\xBFNo ten\xE9s ninguno de estos?",
      blockButton__backupCode: "Usar un c\xF3digo de respaldo",
      blockButton__emailCode: "Enviar c\xF3digo por correo a {{identifier}}",
      blockButton__emailLink: "Enviar enlace por correo a {{identifier}}",
      blockButton__passkey: "Ingres\xE1 con tu clave de acceso",
      blockButton__password: "Ingres\xE1 con tu contrase\xF1a",
      blockButton__phoneCode: "Enviar c\xF3digo SMS a {{identifier}}",
      blockButton__totp: "Usar tu aplicaci\xF3n autenticadora",
      getHelp: {
        blockButton__emailSupport: "Soporte por correo",
        content: "Si ten\xE9s problemas para ingresar a tu cuenta, envi\xE1nos un correo y trabajaremos para restaurar el acceso lo antes posible.",
        title: "Obtener ayuda"
      },
      subtitle: "\xBFTen\xE9s problemas? Pod\xE9s usar cualquiera de estos m\xE9todos para ingresar.",
      title: "Usar otro m\xE9todo"
    },
    backupCodeMfa: {
      subtitle: "Tu c\xF3digo de respaldo es el que recibiste al configurar la autenticaci\xF3n de dos pasos.",
      title: "Ingres\xE1 un c\xF3digo de respaldo"
    },
    emailCode: {
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "\xBFNo recibiste un c\xF3digo? Reenviar",
      subtitle: "para continuar a {{applicationName}}",
      title: "Revis\xE1 tu correo"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Para continuar, abr\xED el enlace de verificaci\xF3n en el dispositivo y navegador desde el cual iniciaste sesi\xF3n",
        title: "El enlace de verificaci\xF3n no es v\xE1lido para este dispositivo"
      },
      expired: {
        subtitle: "Volv\xE9 a la pesta\xF1a original para continuar.",
        title: "Este enlace de verificaci\xF3n ha expirado"
      },
      failed: {
        subtitle: "Volv\xE9 a la pesta\xF1a original para continuar.",
        title: "Este enlace de verificaci\xF3n no es v\xE1lido"
      },
      formSubtitle: "Us\xE1 el enlace de verificaci\xF3n enviado a tu correo",
      formTitle: "Enlace de verificaci\xF3n",
      loading: {
        subtitle: "Ser\xE1s redirigido pronto",
        title: "Iniciando sesi\xF3n..."
      },
      resendButton: "\xBFNo recibiste un enlace? Reenviar",
      subtitle: "para continuar a {{applicationName}}",
      title: "Revis\xE1 tu correo",
      unusedTab: {
        title: "Pod\xE9s cerrar esta pesta\xF1a"
      },
      verified: {
        subtitle: "Ser\xE1s redirigido pronto",
        title: "Sesi\xF3n iniciada con \xE9xito"
      },
      verifiedSwitchTab: {
        subtitle: "Volv\xE9 a la pesta\xF1a original para continuar",
        subtitleNewTab: "Volv\xE9 a la nueva pesta\xF1a para continuar",
        titleNewTab: "Sesi\xF3n iniciada en otra pesta\xF1a"
      }
    },
    forgotPassword: {
      formTitle: "C\xF3digo para restablecer la contrase\xF1a",
      resendButton: "\xBFNo recibiste un c\xF3digo? Reenviar",
      subtitle: "para restablecer tu contrase\xF1a",
      subtitle_email: "Primero, ingres\xE1 el c\xF3digo enviado a tu correo electr\xF3nico",
      subtitle_phone: "Primero, ingres\xE1 el c\xF3digo enviado a tu tel\xE9fono",
      title: "Restablecer contrase\xF1a"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Restablecer tu contrase\xF1a",
      label__alternativeMethods: "O, ingres\xE1 con otro m\xE9todo",
      title: "\xBFOlvidaste tu contrase\xF1a?"
    },
    noAvailableMethods: {
      message: "No se puede continuar con el inicio de sesi\xF3n. No hay un factor de autenticaci\xF3n disponible.",
      subtitle: "Ocurri\xF3 un error",
      title: "No se puede iniciar sesi\xF3n"
    },
    passkey: {
      subtitle: "Usar tu clave de acceso confirma que sos vos. Tu dispositivo puede solicitar tu huella, rostro o bloqueo de pantalla.",
      title: "Usar tu clave de acceso"
    },
    password: {
      actionLink: "Usar otro m\xE9todo",
      subtitle: "Ingres\xE1 la contrase\xF1a asociada a tu cuenta",
      title: "Ingres\xE1 tu contrase\xF1a"
    },
    passwordPwned: {
      title: "Contrase\xF1a comprometida"
    },
    phoneCode: {
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "\xBFNo recibiste un c\xF3digo? Reenviar",
      subtitle: "para continuar a {{applicationName}}",
      title: "Revis\xE1 tu tel\xE9fono"
    },
    phoneCodeMfa: {
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "\xBFNo recibiste un c\xF3digo? Reenviar",
      subtitle: "Para continuar, ingres\xE1 el c\xF3digo enviado a tu tel\xE9fono",
      title: "Revis\xE1 tu tel\xE9fono"
    },
    resetPassword: {
      formButtonPrimary: "Restablecer contrase\xF1a",
      requiredMessage: "Por razones de seguridad, es necesario restablecer tu contrase\xF1a.",
      successMessage: "Tu contrase\xF1a se cambi\xF3 con \xE9xito. Iniciando sesi\xF3n, por favor, esper\xE1 un momento.",
      title: "Establecer nueva contrase\xF1a"
    },
    resetPasswordMfa: {
      detailsLabel: "Necesitamos verificar tu identidad antes de restablecer tu contrase\xF1a."
    },
    start: {
      actionLink: "Registrate",
      actionLink__join_waitlist: "Unirse a la lista de espera",
      actionLink__use_email: "Usar correo",
      actionLink__use_email_username: "Usar correo o nombre de usuario",
      actionLink__use_passkey: "Usar clave de acceso en su lugar",
      actionLink__use_phone: "Usar tel\xE9fono",
      actionLink__use_username: "Usar nombre de usuario",
      actionText: "\xBFNo ten\xE9s una cuenta?",
      actionText__join_waitlist: "\xBFQuer\xE9s acceso anticipado?",
      subtitle: "\xA1Bienvenido de nuevo! Por favor, ingres\xE1 para continuar",
      subtitleCombined: void 0,
      title: "Inici\xE1 sesi\xF3n en {{applicationName}}",
      titleCombined: "Continuar a {{applicationName}}"
    },
    totpMfa: {
      formTitle: "C\xF3digo de verificaci\xF3n",
      subtitle: "Para continuar, ingres\xE1 el c\xF3digo generado por tu aplicaci\xF3n autenticadora",
      title: "Verificaci\xF3n en dos pasos"
    }
  },
  signInEnterPasswordTitle: "Ingres\xE1 tu contrase\xF1a",
  signUp: {
    continue: {
      actionLink: "Iniciar sesi\xF3n",
      actionText: "\xBFYa ten\xE9s una cuenta?",
      subtitle: "Complet\xE1 los datos restantes para continuar.",
      title: "Complet\xE1 los campos faltantes"
    },
    emailCode: {
      formSubtitle: "Ingres\xE1 el c\xF3digo de verificaci\xF3n enviado a tu correo electr\xF3nico",
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "\xBFNo recibiste un c\xF3digo? Reenviar",
      subtitle: "Ingres\xE1 el c\xF3digo de verificaci\xF3n enviado a tu correo",
      title: "Verific\xE1 tu correo"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Para continuar, abr\xED el enlace de verificaci\xF3n en el dispositivo y navegador desde el cual te registraste",
        title: "El enlace de verificaci\xF3n no es v\xE1lido para este dispositivo"
      },
      formSubtitle: "Us\xE1 el enlace de verificaci\xF3n enviado a tu correo electr\xF3nico",
      formTitle: "Enlace de verificaci\xF3n",
      loading: {
        title: "Registr\xE1ndose..."
      },
      resendButton: "\xBFNo recibiste un enlace? Reenviar",
      subtitle: "para continuar a {{applicationName}}",
      title: "Verific\xE1 tu correo",
      verified: {
        title: "Registro exitoso"
      },
      verifiedSwitchTab: {
        subtitle: "Volv\xE9 a la nueva pesta\xF1a para continuar",
        subtitleNewTab: "Volv\xE9 a la pesta\xF1a anterior para continuar",
        title: "Correo verificado con \xE9xito"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: 'Acepto la {{ privacyPolicyLink || link("Pol\xEDtica de Privacidad") }}',
        label__onlyTermsOfService: 'Acepto los {{ termsOfServiceLink || link("T\xE9rminos de Servicio") }}',
        label__termsOfServiceAndPrivacyPolicy: 'Acepto los {{ termsOfServiceLink || link("T\xE9rminos de Servicio") }} y la {{ privacyPolicyLink || link("Pol\xEDtica de Privacidad") }}'
      },
      continue: {
        subtitle: "Por favor, le\xE9 y acept\xE1 los t\xE9rminos para continuar",
        title: "Consentimiento legal"
      }
    },
    phoneCode: {
      formSubtitle: "Ingres\xE1 el c\xF3digo de verificaci\xF3n enviado a tu n\xFAmero de tel\xE9fono",
      formTitle: "C\xF3digo de verificaci\xF3n",
      resendButton: "\xBFNo recibiste un c\xF3digo? Reenviar",
      subtitle: "Ingres\xE1 el c\xF3digo enviado a tu tel\xE9fono",
      title: "Verific\xE1 tu tel\xE9fono"
    },
    restrictedAccess: {
      actionLink: "Iniciar sesi\xF3n",
      actionText: "\xBFYa ten\xE9s una cuenta?",
      blockButton__emailSupport: "Soporte por correo",
      blockButton__joinWaitlist: "Unirse a la lista de espera",
      subtitle: "Actualmente, los registros est\xE1n deshabilitados. Si cre\xE9s que deber\xEDas tener acceso, contact\xE1 al soporte.",
      subtitleWaitlist: "Actualmente, los registros est\xE1n deshabilitados. Para ser el primero en saber cuando lancemos, unite a la lista de espera.",
      title: "Acceso restringido"
    },
    start: {
      actionLink: "Iniciar sesi\xF3n",
      actionLink__use_email: "Usar correo en su lugar",
      actionLink__use_phone: "Usar tel\xE9fono en su lugar",
      actionText: "\xBFYa ten\xE9s una cuenta?",
      subtitle: "\xA1Bienvenido! Complet\xE1 los datos para comenzar.",
      subtitleCombined: "\xA1Bienvenido! Complet\xE1 los datos para comenzar.",
      title: "Cre\xE1 tu cuenta",
      titleCombined: "Cre\xE1 tu cuenta"
    }
  },
  socialButtonsBlockButton: "Continuar con {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: "{{provider|titleize}}",
  unstable__errors: {
    already_a_member_in_organization: "{{email}} ya es miembro de la organizaci\xF3n.",
    captcha_invalid: "El registro no se pudo completar debido a validaciones de seguridad fallidas. Por favor, actualiz\xE1 la p\xE1gina para intentarlo de nuevo o contact\xE1 al soporte para m\xE1s asistencia.",
    captcha_unavailable: "El registro no se pudo completar debido a la validaci\xF3n fallida contra bots. Por favor, actualiz\xE1 la p\xE1gina para intentarlo de nuevo o contact\xE1 al soporte para m\xE1s asistencia.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: "Este correo electr\xF3nico ya est\xE1 en uso. Por favor, prob\xE1 con otro.",
    form_identifier_exists__phone_number: "Este n\xFAmero de tel\xE9fono ya est\xE1 en uso. Por favor, prob\xE1 con otro.",
    form_identifier_exists__username: "Este nombre de usuario ya est\xE1 en uso. Por favor, prob\xE1 con otro.",
    form_identifier_not_found: "No se encontr\xF3 una cuenta con esos detalles.",
    form_param_format_invalid: "El valor ingresado tiene un formato inv\xE1lido. Por favor, verific\xE1 y correg\xED.",
    form_param_format_invalid__email_address: "El correo electr\xF3nico debe ser v\xE1lido.",
    form_param_format_invalid__phone_number: "El n\xFAmero de tel\xE9fono debe estar en un formato internacional v\xE1lido.",
    form_param_max_length_exceeded__first_name: "El nombre no debe exceder los 256 caracteres.",
    form_param_max_length_exceeded__last_name: "El apellido no debe exceder los 256 caracteres.",
    form_param_max_length_exceeded__name: "El nombre no debe exceder los 256 caracteres.",
    form_param_nil: "Este campo es obligatorio y no puede estar vac\xEDo.",
    form_param_value_invalid: "El valor ingresado es inv\xE1lido. Por favor, corregilo.",
    form_password_incorrect: "La contrase\xF1a ingresada es incorrecta. Por favor, intent\xE1 de nuevo.",
    form_password_length_too_short: "Tu contrase\xF1a es demasiado corta. Debe tener al menos 8 caracteres.",
    form_password_not_strong_enough: "Tu contrase\xF1a no es lo suficientemente fuerte.",
    form_password_pwned: "Esta contrase\xF1a se encontr\xF3 en una filtraci\xF3n y no se puede usar. Por favor, prob\xE1 con otra contrase\xF1a.",
    form_password_pwned__sign_in: "Esta contrase\xF1a se encontr\xF3 en una filtraci\xF3n y no se puede usar. Por favor, restablec\xE9 tu contrase\xF1a.",
    form_password_size_in_bytes_exceeded: "Tu contrase\xF1a ha excedido el n\xFAmero m\xE1ximo de bytes permitidos. Por favor, acortala o elimin\xE1 algunos caracteres especiales.",
    form_password_validation_failed: "Contrase\xF1a incorrecta",
    form_username_invalid_character: "Tu nombre de usuario contiene caracteres inv\xE1lidos. Por favor, us\xE1 solo letras, n\xFAmeros y guiones bajos.",
    form_username_invalid_length: "Tu nombre de usuario debe tener entre {{min_length}} y {{max_length}} caracteres.",
    identification_deletion_failed: "No pod\xE9s eliminar tu \xFAltima identificaci\xF3n.",
    not_allowed_access: "La direcci\xF3n de correo electr\xF3nico o el n\xFAmero de tel\xE9fono no est\xE1 permitido para registrarse. Esto puede deberse al uso de '+', '=', '#' o '.' en tu direcci\xF3n de correo electr\xF3nico, el uso de un dominio conectado a un servicio de correo electr\xF3nico temporal o la exclusi\xF3n expl\xEDcita. Si cree que se trata de un error, p\xF3ngase en contacto con el soporte.",
    organization_domain_blocked: "Este es un dominio de proveedor de correo bloqueado. Por favor, us\xE1 otro.",
    organization_domain_common: "Este es un dominio de proveedor de correo com\xFAn. Por favor, us\xE1 otro.",
    organization_domain_exists_for_enterprise_connection: "Este dominio ya se utiliza para el SSO de tu organizaci\xF3n.",
    organization_membership_quota_exceeded: "Has alcanzado el l\xEDmite de membres\xEDas en organizaciones, incluyendo invitaciones pendientes.",
    organization_minimum_permissions_needed: "Debe haber al menos un miembro de la organizaci\xF3n con los permisos m\xEDnimos requeridos.",
    passkey_already_exists: "Ya hay una clave de acceso registrada en este dispositivo.",
    passkey_not_supported: "Las claves de acceso no son compatibles con este dispositivo.",
    passkey_pa_not_supported: "El registro requiere un autenticador de plataforma, pero el dispositivo no lo soporta.",
    passkey_registration_cancelled: "El registro de la clave de acceso fue cancelado o agot\xF3 el tiempo de espera.",
    passkey_retrieval_cancelled: "La verificaci\xF3n de la clave de acceso fue cancelada o agot\xF3 el tiempo de espera.",
    passwordComplexity: {
      maximumLength: "menos de {{length}} caracteres",
      minimumLength: "{{length}} o m\xE1s caracteres",
      requireLowercase: "una letra min\xFAscula",
      requireNumbers: "un n\xFAmero",
      requireSpecialCharacter: "un car\xE1cter especial",
      requireUppercase: "una letra may\xFAscula",
      sentencePrefix: "Tu contrase\xF1a debe contener"
    },
    phone_number_exists: "Este n\xFAmero de tel\xE9fono ya est\xE1 en uso. Por favor, prob\xE1 con otro.",
    session_exists: "Ya has iniciado sesi\xF3n",
    web3_missing_identifier: "No se encontr\xF3 una extensi\xF3n de cartera Web3. Por favor, instal\xE1 una para continuar.",
    zxcvbn: {
      couldBeStronger: "Tu contrase\xF1a funciona, pero podr\xEDa ser m\xE1s fuerte. Intent\xE1 agregar m\xE1s caracteres.",
      goodPassword: "Tu contrase\xF1a cumple con todos los requisitos necesarios.",
      notEnough: "Tu contrase\xF1a no es lo suficientemente fuerte.",
      suggestions: {
        allUppercase: "Us\xE1 may\xFAsculas en algunas letras, pero no en todas.",
        anotherWord: "Agreg\xE1 m\xE1s palabras que sean menos comunes.",
        associatedYears: "Evit\xE1 a\xF1os que est\xE9n asociados contigo.",
        capitalization: "Us\xE1 may\xFAsculas en m\xE1s de la primera letra.",
        dates: "Evit\xE1 fechas y a\xF1os que est\xE9n asociados contigo.",
        l33t: 'Evit\xE1 sustituciones de letras predecibles como "@" en lugar de "a".',
        longerKeyboardPattern: "Us\xE1 patrones de teclado m\xE1s largos y cambi\xE1 la direcci\xF3n de tecleo varias veces.",
        noNeed: "Pod\xE9s crear contrase\xF1as fuertes sin usar s\xEDmbolos, n\xFAmeros o letras may\xFAsculas.",
        pwned: "Si us\xE1s esta contrase\xF1a en otros sitios, deber\xEDas cambiarla.",
        recentYears: "Evit\xE1 a\xF1os recientes.",
        repeated: "Evit\xE1 palabras y caracteres repetidos.",
        reverseWords: "Evit\xE1 las versiones invertidas de palabras comunes.",
        sequences: "Evit\xE1 secuencias comunes de caracteres.",
        useWords: "Us\xE1 varias palabras, pero evit\xE1 frases comunes."
      },
      warnings: {
        common: "Esta es una contrase\xF1a com\xFAn.",
        commonNames: "Los nombres y apellidos comunes son f\xE1ciles de adivinar.",
        dates: "Las fechas son f\xE1ciles de adivinar.",
        extendedRepeat: 'Los patrones de caracteres repetidos como "abcabcabc" son f\xE1ciles de adivinar.',
        keyPattern: "Los patrones cortos de teclado son f\xE1ciles de adivinar.",
        namesByThemselves: "Los nombres o apellidos por s\xED solos son f\xE1ciles de adivinar.",
        pwned: "Tu contrase\xF1a se vio comprometida en una filtraci\xF3n de datos en Internet.",
        recentYears: "Los a\xF1os recientes son f\xE1ciles de adivinar.",
        sequences: 'Secuencias comunes de caracteres como "abc" son f\xE1ciles de adivinar.',
        similarToCommon: "Esto es similar a una contrase\xF1a com\xFAn.",
        simpleRepeat: 'Caracteres repetidos como "aaa" son f\xE1ciles de adivinar.',
        straightRow: "Filas rectas de teclas en tu teclado son f\xE1ciles de adivinar.",
        topHundred: "Esta es una contrase\xF1a frecuentemente usada.",
        topTen: "Esta es una contrase\xF1a muy usada.",
        userInputs: "No debe haber ning\xFAn dato personal o relacionado con la p\xE1gina.",
        wordByItself: "Palabras solas son f\xE1ciles de adivinar."
      }
    }
  },
  userButton: {
    action__addAccount: "Agregar cuenta",
    action__manageAccount: "Gestionar cuenta",
    action__signOut: "Cerrar sesi\xF3n",
    action__signOutAll: "Cerrar sesi\xF3n en todas las cuentas"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "\xA1Copiado!",
      actionLabel__copy: "Copiar todo",
      actionLabel__download: "Descargar .txt",
      actionLabel__print: "Imprimir",
      infoText1: "Se habilitar\xE1n c\xF3digos de respaldo para esta cuenta.",
      infoText2: "Manten\xE9 los c\xF3digos de respaldo en secreto y guardalos de forma segura. Pod\xE9s regenerarlos si sospech\xE1s que han sido comprometidos.",
      subtitle__codelist: "Guardalos de forma segura y mantenelos en secreto.",
      successMessage: "Los c\xF3digos de respaldo est\xE1n habilitados. Pod\xE9s usar uno de ellos para ingresar a tu cuenta si perd\xE9s el acceso a tu dispositivo de autenticaci\xF3n. Cada c\xF3digo solo puede usarse una vez.",
      successSubtitle: "Pod\xE9s usar uno de ellos para ingresar a tu cuenta si perd\xE9s el acceso a tu dispositivo de autenticaci\xF3n.",
      title: "Agregar verificaci\xF3n con c\xF3digo de respaldo",
      title__codelist: "C\xF3digos de respaldo"
    },
    connectedAccountPage: {
      formHint: "Seleccion\xE1 un proveedor para conectar tu cuenta.",
      formHint__noAccounts: "No hay proveedores externos de cuenta disponibles.",
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 eliminado de esta cuenta.",
        messageLine2: "Ya no podr\xE1s usar esta cuenta conectada y las funciones dependientes dejar\xE1n de funcionar.",
        successMessage: "{{connectedAccount}} ha sido eliminado de tu cuenta.",
        title: "Eliminar cuenta conectada"
      },
      socialButtonsBlockButton: "{{provider|titleize}}",
      successMessage: "El proveedor ha sido agregado a tu cuenta",
      title: "Agregar cuenta conectada"
    },
    deletePage: {
      actionDescription: 'Escrib\xED "Eliminar cuenta" abajo para continuar.',
      confirm: "Eliminar cuenta",
      messageLine1: "\xBFEst\xE1s seguro de que quer\xE9s eliminar tu cuenta?",
      messageLine2: "Esta acci\xF3n es permanente e irreversible.",
      title: "Eliminar cuenta"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Se enviar\xE1 un correo con un c\xF3digo de verificaci\xF3n a este correo electr\xF3nico.",
        formSubtitle: "Ingres\xE1 el c\xF3digo de verificaci\xF3n enviado a {{identifier}}",
        formTitle: "C\xF3digo de verificaci\xF3n",
        resendButton: "\xBFNo recibiste un c\xF3digo? Reenviar",
        successMessage: "El correo {{identifier}} ha sido agregado a tu cuenta."
      },
      emailLink: {
        formHint: "Se enviar\xE1 un correo con un enlace de verificaci\xF3n a este correo electr\xF3nico.",
        formSubtitle: "Hac\xE9 clic en el enlace de verificaci\xF3n enviado a {{identifier}}",
        formTitle: "Enlace de verificaci\xF3n",
        resendButton: "\xBFNo recibiste un enlace? Reenviar",
        successMessage: "El correo {{identifier}} ha sido agregado a tu cuenta."
      },
      enterpriseSSOLink: {
        formButton: "Hac\xE9 clic para iniciar sesi\xF3n",
        formSubtitle: "Complet\xE1 el inicio de sesi\xF3n con {{identifier}}"
      },
      formHint: "Necesit\xE1s verificar este correo electr\xF3nico antes de poder agregarlo a tu cuenta.",
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 eliminado de esta cuenta.",
        messageLine2: "Ya no podr\xE1s iniciar sesi\xF3n usando este correo electr\xF3nico.",
        successMessage: "{{emailAddress}} ha sido eliminado de tu cuenta.",
        title: "Eliminar correo electr\xF3nico"
      },
      title: "Agregar correo electr\xF3nico",
      verifyTitle: "Verificar correo electr\xF3nico"
    },
    formButtonPrimary__add: "Agregar",
    formButtonPrimary__continue: "Continuar",
    formButtonPrimary__finish: "Finalizar",
    formButtonPrimary__remove: "Eliminar",
    formButtonPrimary__save: "Guardar",
    formButtonReset: "Cancelar",
    mfaPage: {
      formHint: "Seleccion\xE1 un m\xE9todo para agregar.",
      title: "Agregar verificaci\xF3n de dos pasos"
    },
    mfaPhoneCodePage: {
      backButton: "Usar n\xFAmero existente",
      primaryButton__addPhoneNumber: "Agregar n\xFAmero de tel\xE9fono",
      removeResource: {
        messageLine1: "{{identifier}} dejar\xE1 de recibir c\xF3digos de verificaci\xF3n al iniciar sesi\xF3n.",
        messageLine2: "Tu cuenta podr\xEDa no estar tan segura. \xBFEst\xE1s seguro de querer continuar?",
        successMessage: "La verificaci\xF3n de dos pasos mediante c\xF3digo SMS ha sido eliminada para {{mfaPhoneCode}}",
        title: "Eliminar verificaci\xF3n de dos pasos"
      },
      subtitle__availablePhoneNumbers: "Seleccion\xE1 un n\xFAmero de tel\xE9fono existente para registrar la verificaci\xF3n de dos pasos por c\xF3digo SMS o agreg\xE1 uno nuevo.",
      subtitle__unavailablePhoneNumbers: "No hay n\xFAmeros de tel\xE9fono disponibles para registrar la verificaci\xF3n de dos pasos por c\xF3digo SMS, por favor, agreg\xE1 uno nuevo.",
      successMessage1: "Al iniciar sesi\xF3n, deber\xE1s ingresar un c\xF3digo de verificaci\xF3n enviado a este n\xFAmero como un paso adicional.",
      successMessage2: "Guard\xE1 estos c\xF3digos de respaldo y almacenalos en un lugar seguro. Si perd\xE9s el acceso a tu dispositivo de autenticaci\xF3n, pod\xE9s usar los c\xF3digos de respaldo para ingresar.",
      successTitle: "Verificaci\xF3n por c\xF3digo SMS habilitada",
      title: "Agregar verificaci\xF3n por c\xF3digo SMS"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Escane\xE1 el c\xF3digo QR en su lugar",
        buttonUnableToScan__nonPrimary: "\xBFNo pod\xE9s escanear el c\xF3digo QR?",
        infoText__ableToScan: "Configur\xE1 un nuevo m\xE9todo de ingreso en tu aplicaci\xF3n autenticadora y escane\xE1 el siguiente c\xF3digo QR para vincularlo a tu cuenta.",
        infoText__unableToScan: "Configur\xE1 un nuevo m\xE9todo de ingreso en tu autenticadora e ingres\xE1 la clave proporcionada abajo.",
        inputLabel__unableToScan1: "Asegurate de que las contrase\xF1as de un solo uso basadas en tiempo est\xE9n habilitadas, luego complet\xE1 la vinculaci\xF3n de tu cuenta.",
        inputLabel__unableToScan2: "Alternativamente, si tu autenticadora soporta URIs TOTP, tambi\xE9n pod\xE9s copiar la URI completa."
      },
      removeResource: {
        messageLine1: "Los c\xF3digos de verificaci\xF3n de esta autenticadora ya no ser\xE1n requeridos al iniciar sesi\xF3n.",
        messageLine2: "Tu cuenta podr\xEDa no estar tan segura. \xBFEst\xE1s seguro de querer continuar?",
        successMessage: "La verificaci\xF3n de dos pasos mediante aplicaci\xF3n autenticadora ha sido eliminada.",
        title: "Eliminar verificaci\xF3n de dos pasos"
      },
      successMessage: "La verificaci\xF3n de dos pasos est\xE1 habilitada. Al iniciar sesi\xF3n, deber\xE1s ingresar un c\xF3digo de verificaci\xF3n de esta autenticadora como paso adicional.",
      title: "Agregar aplicaci\xF3n autenticadora",
      verifySubtitle: "Ingres\xE1 el c\xF3digo de verificaci\xF3n generado por tu autenticadora",
      verifyTitle: "C\xF3digo de verificaci\xF3n"
    },
    mobileButton__menu: "Men\xFA",
    navbar: {
      account: "Perfil",
      description: "Gestion\xE1 la informaci\xF3n de tu cuenta.",
      security: "Seguridad",
      title: "Cuenta"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} ser\xE1 eliminado de esta cuenta.",
        title: "Eliminar clave de acceso"
      },
      subtitle__rename: "Pod\xE9s cambiar el nombre de la clave de acceso para facilitar su identificaci\xF3n.",
      title__rename: "Renombrar clave de acceso"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Se recomienda cerrar sesi\xF3n en todos los dem\xE1s dispositivos que hayan usado tu antigua contrase\xF1a.",
      readonly: "Actualmente, tu contrase\xF1a no puede ser editada porque solo pod\xE9s ingresar mediante la conexi\xF3n empresarial.",
      successMessage__set: "Tu contrase\xF1a ha sido establecida.",
      successMessage__signOutOfOtherSessions: "Todos los dem\xE1s dispositivos han sido cerrados sesi\xF3n.",
      successMessage__update: "Tu contrase\xF1a ha sido actualizada.",
      title__set: "Establecer contrase\xF1a",
      title__update: "Actualizar contrase\xF1a"
    },
    phoneNumberPage: {
      infoText: "Se enviar\xE1 un mensaje de texto con un c\xF3digo de verificaci\xF3n a este n\xFAmero. Pueden aplicarse tarifas de mensaje y datos.",
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 eliminado de esta cuenta.",
        messageLine2: "Ya no podr\xE1s iniciar sesi\xF3n usando este n\xFAmero de tel\xE9fono.",
        successMessage: "{{phoneNumber}} ha sido eliminado de tu cuenta.",
        title: "Eliminar n\xFAmero de tel\xE9fono"
      },
      successMessage: "{{identifier}} ha sido agregado a tu cuenta.",
      title: "Agregar n\xFAmero de tel\xE9fono",
      verifySubtitle: "Ingres\xE1 el c\xF3digo de verificaci\xF3n enviado a {{identifier}}",
      verifyTitle: "Verificar n\xFAmero de tel\xE9fono"
    },
    profilePage: {
      fileDropAreaHint: "Tama\xF1o recomendado 1:1, hasta 10MB.",
      imageFormDestructiveActionSubtitle: "Eliminar",
      imageFormSubtitle: "Subir",
      imageFormTitle: "Imagen de perfil",
      readonly: "La informaci\xF3n de tu perfil fue proporcionada por la conexi\xF3n empresarial y no puede ser editada.",
      successMessage: "Tu perfil ha sido actualizado.",
      title: "Actualizar perfil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Cerrar sesi\xF3n en dispositivo",
        title: "Dispositivos activos"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Reconectar",
        actionLabel__reauthorize: "Autorizar ahora",
        destructiveActionTitle: "Eliminar",
        primaryButton: "Conectar cuenta",
        subtitle__disconnected: "Esta cuenta ha sido desconectada.",
        subtitle__reauthorize: "Los permisos requeridos han sido actualizados y podr\xEDas experimentar funcionalidad limitada. Por favor, reautoriza esta aplicaci\xF3n para evitar inconvenientes",
        title: "Cuentas conectadas"
      },
      dangerSection: {
        deleteAccountButton: "Eliminar cuenta",
        title: "Eliminar cuenta"
      },
      emailAddressesSection: {
        destructiveAction: "Eliminar correo",
        detailsAction__nonPrimary: "Establecer como principal",
        detailsAction__primary: "Completar verificaci\xF3n",
        detailsAction__unverified: "Verificar",
        primaryButton: "Agregar correo electr\xF3nico",
        title: "Correos electr\xF3nicos"
      },
      enterpriseAccountsSection: {
        title: "Cuentas empresariales"
      },
      headerTitle__account: "Detalles del perfil",
      headerTitle__security: "Seguridad",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Regenerar",
          headerTitle: "C\xF3digos de respaldo",
          subtitle__regenerate: "Obten\xE9 un nuevo conjunto de c\xF3digos de respaldo seguros. Los c\xF3digos anteriores ser\xE1n eliminados y no podr\xE1n usarse.",
          title__regenerate: "Regenerar c\xF3digos de respaldo"
        },
        phoneCode: {
          actionLabel__setDefault: "Establecer como predeterminado",
          destructiveActionLabel: "Eliminar"
        },
        primaryButton: "Agregar verificaci\xF3n de dos pasos",
        title: "Verificaci\xF3n de dos pasos",
        totp: {
          destructiveActionTitle: "Eliminar",
          headerTitle: "Aplicaci\xF3n autenticadora"
        }
      },
      passkeysSection: {
        primaryButton: "Agregar una clave de acceso",
        menuAction__destructive: "Eliminar",
        menuAction__rename: "Renombrar",
        title: "Claves de acceso"
      },
      passwordSection: {
        primaryButton__setPassword: "Establecer contrase\xF1a",
        primaryButton__updatePassword: "Actualizar contrase\xF1a",
        title: "Contrase\xF1a"
      },
      phoneNumbersSection: {
        destructiveAction: "Eliminar n\xFAmero de tel\xE9fono",
        detailsAction__nonPrimary: "Establecer como principal",
        detailsAction__primary: "Completar verificaci\xF3n",
        detailsAction__unverified: "Verificar n\xFAmero de tel\xE9fono",
        primaryButton: "Agregar n\xFAmero de tel\xE9fono",
        title: "N\xFAmeros de tel\xE9fono"
      },
      profileSection: {
        primaryButton: "Actualizar perfil",
        title: "Perfil"
      },
      usernameSection: {
        primaryButton__setUsername: "Establecer nombre de usuario",
        primaryButton__updateUsername: "Actualizar nombre de usuario",
        title: "Nombre de usuario"
      },
      web3WalletsSection: {
        destructiveAction: "Eliminar cartera",
        primaryButton: "Conectar cartera",
        title: "Carteras Web3"
      }
    },
    usernamePage: {
      successMessage: "Tu nombre de usuario ha sido actualizado.",
      title__set: "Establecer nombre de usuario",
      title__update: "Actualizar nombre de usuario"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} ser\xE1 eliminado de esta cuenta.",
        messageLine2: "Ya no podr\xE1s iniciar sesi\xF3n usando esta cartera Web3.",
        successMessage: "{{web3Wallet}} ha sido eliminado de tu cuenta.",
        title: "Eliminar cartera Web3"
      },
      subtitle__availableWallets: "Seleccion\xE1 una cartera Web3 para conectar a tu cuenta.",
      subtitle__unavailableWallets: "No hay carteras Web3 disponibles.",
      successMessage: "La cartera ha sido agregada a tu cuenta.",
      title: "Agregar cartera Web3",
      web3WalletButtonsBlockButton: "{{provider|titleize}}"
    }
  },
  waitlist: {
    start: {
      actionLink: "Iniciar sesi\xF3n",
      actionText: "\xBFYa ten\xE9s acceso?",
      formButton: "Unirse a la lista de espera",
      subtitle: "Ingres\xE1 tu correo electr\xF3nico y te avisaremos cuando tu lugar est\xE9 listo",
      title: "Unirse a la lista de espera"
    },
    success: {
      message: "Ser\xE1s redirigido pronto...",
      subtitle: "Nos pondremos en contacto cuando tu lugar est\xE9 listo",
      title: "\xA1Gracias por unirte a la lista de espera!"
    }
  }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  esUY
});
//# sourceMappingURL=es-UY.js.map