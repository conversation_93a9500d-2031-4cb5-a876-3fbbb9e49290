{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/app/%5Blocale%5D/%28auth%29/%28center%29/layout.tsx"], "sourcesContent": ["import { setRequestLocale } from 'next-intl/server';\r\n\r\nexport default async function CenteredLayout(props: {\r\n  children: React.ReactNode;\r\n  params: Promise<{ locale: string }>;\r\n}) {\r\n  const { locale } = await props.params;\r\n  setRequestLocale(locale);\r\n\r\n  return (\r\n    <div className=\"flex min-h-screen items-center justify-center\">\r\n      {props.children}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,eAAe,eAAe,KAG5C;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,CAAA,GAAA,2QAAA,CAAA,mBAAgB,AAAD,EAAE;IAEjB,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,QAAQ;;;;;;AAGrB", "debugId": null}}]}