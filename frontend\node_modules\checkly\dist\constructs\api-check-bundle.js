"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiCheckBundle = void 0;
class ApiCheckBundle {
    apiCheck;
    localSetupScript;
    setupScriptPath;
    setupScriptDependencies;
    localTearDownScript;
    tearDownScriptPath;
    tearDownScriptDependencies;
    constructor(apiCheck, props) {
        this.apiCheck = apiCheck;
        this.localSetupScript = props.localSetupScript;
        this.setupScriptPath = props.setupScriptPath;
        this.setupScriptDependencies = props.setupScriptDependencies;
        this.localTearDownScript = props.localTearDownScript;
        this.tearDownScriptPath = props.tearDownScriptPath;
        this.tearDownScriptDependencies = props.tearDownScriptDependencies;
    }
    synthesize() {
        return {
            ...this.apiCheck.synthesize(),
            localSetupScript: this.localSetupScript,
            setupScriptPath: this.setupScriptPath,
            setupScriptDependencies: this.setupScriptDependencies,
            localTearDownScript: this.localTearDownScript,
            tearDownScriptPath: this.tearDownScriptPath,
            tearDownScriptDependencies: this.tearDownScriptDependencies,
        };
    }
}
exports.ApiCheckBundle = ApiCheckBundle;
//# sourceMappingURL=api-check-bundle.js.map