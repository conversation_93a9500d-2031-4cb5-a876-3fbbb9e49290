{"node": {"7f4a02af96e1bb6aa73a8043a67d03e71bf5a0b1fc": {"workers": {"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page": "action-browser"}}, "7f77a91b277141486faa446cc5de3827f9d835b4e4": {"workers": {"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page": "action-browser"}}, "7ff46c643d4772dc8ee990c6d15e55cd365348ec79": {"workers": {"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page": "action-browser"}}, "7f004e23488fbd4b4c13c8ce70e3fc1b5cfb4136dd": {"workers": {"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page": "action-browser"}}}, "edge": {}}