'use strict';

!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="4910faaf-e841-5015-9ae4-b59cb0cd352f")}catch(e){}}();

var chunkTKGT252T_js = require('./chunk-TKGT252T.js');
var fs = require('fs');

var D=chunkTKGT252T_js.c((Xt,nt)=>{nt.exports={name:"@discoveryjs/json-ext",version:"0.5.7",description:"A set of utilities that extend the use of JSON",keywords:["json","utils","stream","async","promise","stringify","info"],author:"<PERSON> <rd<PERSON><PERSON>@gmail.com> (https://github.com/lahmatiy)",license:"MIT",repository:"discoveryjs/json-ext",main:"./src/index",browser:{"./src/stringify-stream.js":"./src/stringify-stream-browser.js","./src/text-decoder.js":"./src/text-decoder-browser.js","./src/version.js":"./dist/version.js"},types:"./index.d.ts",scripts:{test:"mocha --reporter progress",lint:"eslint src test","lint-and-test":"npm run lint && npm test",build:"rollup --config","test:all":"npm run test:src && npm run test:dist","test:src":"npm test","test:dist":"cross-env MODE=dist npm test && cross-env MODE=dist-min npm test","build-and-test":"npm run build && npm run test:dist",coverage:"c8 --reporter=lcovonly npm test",prepublishOnly:"npm run lint && npm test && npm run build-and-test"},devDependencies:{"@rollup/plugin-commonjs":"^15.1.0","@rollup/plugin-json":"^4.1.0","@rollup/plugin-node-resolve":"^9.0.0",c8:"^7.10.0",chalk:"^4.1.0","cross-env":"^7.0.3",eslint:"^8.10.0",mocha:"^8.4.0",rollup:"^2.28.2","rollup-plugin-terser":"^7.0.2"},engines:{node:">=10.0.0"},files:["dist","src","index.d.ts"]};});var P=chunkTKGT252T_js.c((Zt,C)=>{C.exports=D().version;});var g=chunkTKGT252T_js.c((vt,N)=>{var ht={8:"\\b",9:"\\t",10:"\\n",12:"\\f",13:"\\r",34:'\\"',92:"\\\\"};function at(e){return e>=55296&&e<=56319}function ot(e){return e>=56320&&e<=57343}function E(e){return typeof e.pipe=="function"&&typeof e._read=="function"&&typeof e._readableState=="object"&&e._readableState!==null}function ct(e,t,s,i){switch(s&&typeof s.toJSON=="function"&&(s=s.toJSON()),i!==null&&(s=i.call(e,String(t),s)),typeof s){case"function":case"symbol":s=void 0;break;case"object":if(s!==null){let r=s.constructor;(r===String||r===Number||r===Boolean)&&(s=s.valueOf());}break}return s}function lt(e){return e===null||typeof e!="object"?1:Array.isArray(e)?3:2}function pt(e){return e===null||typeof e!="object"?1:typeof e.then=="function"?4:E(e)?e._readableState.objectMode?6:5:Array.isArray(e)?3:2}function ft(e){return typeof e=="function"?e:Array.isArray(e)?[...new Set(e.map(s=>{let i=s&&s.constructor;return i===String||i===Number?String(s):null}).filter(s=>typeof s=="string"))]:null}function ut(e){return typeof e=="number"?!Number.isFinite(e)||e<1?!1:" ".repeat(Math.min(e,10)):typeof e=="string"&&e.slice(0,10)||!1}N.exports={escapableCharCodeSubstitution:ht,isLeadingSurrogate:at,isTrailingSurrogate:ot,type:{PRIMITIVE:1,PROMISE:4,ARRAY:3,OBJECT:2,STRING_STREAM:5,OBJECT_STREAM:6},isReadableStream:E,replaceValue:ct,getTypeNative:lt,getTypeAsync:pt,normalizeReplacer:ft,normalizeSpace:ut};});var J=chunkTKGT252T_js.c((te,q)=>{var{normalizeReplacer:dt,normalizeSpace:gt,replaceValue:yt,getTypeNative:St,getTypeAsync:bt,isLeadingSurrogate:mt,isTrailingSurrogate:_t,escapableCharCodeSubstitution:kt,type:{PRIMITIVE:F,OBJECT:xt,ARRAY:At,PROMISE:Ot,STRING_STREAM:Tt,OBJECT_STREAM:wt}}=g(),jt=Array.from({length:2048}).map((e,t)=>kt.hasOwnProperty(t)?2:t<32?6:t<128?1:2);function I(e){let t=0,s=!1;for(let i=0;i<e.length;i++){let r=e.charCodeAt(i);if(r<2048)t+=jt[r];else if(mt(r)){t+=6,s=!0;continue}else _t(r)?t=s?t-2:t+6:t+=3;s=!1;}return t+2}function Rt(e){switch(typeof e){case"string":return I(e);case"number":return Number.isFinite(e)?String(e).length:4;case"boolean":return e?4:5;case"undefined":case"object":return 4;default:return 0}}function Dt(e){return e=gt(e),typeof e=="string"?e.length:0}q.exports=function(t,s,i,r){function n(b,st,h){if(w)return;h=yt(b,st,h,s);let j=et(h);if(j!==F&&c.has(h)){O.add(h),a+=4,r.continueOnCircular||(w=!0);return}switch(j){case F:h!==void 0||Array.isArray(b)?a+=Rt(h):b===T&&(a+=9);break;case xt:{if(l.has(h)){y.add(h),a+=l.get(h);break}let m=a,p=0;a+=2,c.add(h);for(let u in h)if(hasOwnProperty.call(h,u)&&(o===null||o.has(u))){let it=a;n(h,u,h[u]),it!==a&&(a+=I(u)+1,p++);}p>1&&(a+=p-1),c.delete(h),i>0&&p>0&&(a+=(1+(c.size+1)*i+1)*p,a+=1+c.size*i),l.set(h,a-m);break}case At:{if(l.has(h)){y.add(h),a+=l.get(h);break}let m=a;a+=2,c.add(h);for(let p=0;p<h.length;p++)n(h,p,h[p]);h.length>1&&(a+=h.length-1),c.delete(h),i>0&&h.length>0&&(a+=(1+(c.size+1)*i)*h.length,a+=1+c.size*i),l.set(h,a-m);break}case Ot:case Tt:S.add(h);break;case wt:a+=2,S.add(h);break}}let o=null;s=dt(s),Array.isArray(s)&&(o=new Set(s),s=null),i=Dt(i),r=r||{};let l=new Map,c=new Set,y=new Set,O=new Set,S=new Set,et=r.async?bt:St,T={"":t},w=!1,a=0;return n(T,"",t),{minLength:isNaN(a)?1/0:a,circular:[...O],duplicate:[...y],async:[...S]}};});var G=chunkTKGT252T_js.c((ee,Y)=>{var{Readable:Ct}=chunkTKGT252T_js.a("stream"),{normalizeReplacer:Pt,normalizeSpace:Et,replaceValue:Nt,getTypeAsync:Ft,type:{PRIMITIVE:It,OBJECT:qt,ARRAY:Jt,PROMISE:Mt,STRING_STREAM:Bt,OBJECT_STREAM:_}}=g(),M=()=>{},zt=Object.prototype.hasOwnProperty,Lt=JSON.stringify("\uD800")==='"\\ud800"'?JSON.stringify:e=>JSON.stringify(e).replace(/\p{Surrogate}/gu,t=>`\\u${t.charCodeAt(0).toString(16)}`);function Vt(){this.push(this._stack.value),this.popStack();}function $t(e){switch(typeof e){case"string":this.push(this.encodeString(e));break;case"number":this.push(Number.isFinite(e)?this.encodeNumber(e):"null");break;case"boolean":this.push(e?"true":"false");break;case"undefined":case"object":this.push("null");break;default:this.destroy(new TypeError(`Do not know how to serialize a ${e.constructor&&e.constructor.name||typeof e}`));}}function V(e){let t=this._stack;t.first?this.push(","):t.first=!0,this.space?this.push(`
${this.space.repeat(this._depth)}${this.encodeString(e)}: `):this.push(this.encodeString(e)+":");}function B(){let e=this._stack;if(e.index===e.keys.length){this.space&&e.first?this.push(`
${this.space.repeat(this._depth-1)}}`):this.push("}"),this.popStack();return}let t=e.keys[e.index];this.processValue(e.value,t,e.value[t],V),e.index++;}function $(e){e!==0&&this.push(","),this.space&&this.push(`
${this.space.repeat(this._depth)}`);}function z(){let e=this._stack;if(e.index===e.value.length){this.space&&e.index>0?this.push(`
${this.space.repeat(this._depth-1)}]`):this.push("]"),this.popStack();return}this.processValue(e.value,e.index,e.value[e.index],$),e.index++;}function K(e){return function(){let t=this._stack,s=t.value.read(this._readSize);s!==null?(t.first=!1,e.call(this,s,t)):t.first&&!t.value._readableState.reading||t.ended?this.popStack():(t.first=!0,t.awaiting=!0);}}var L=K(function(e,t){this.processValue(t.value,t.index,e,$),t.index++;}),Kt=K(function(e){this.push(e);}),k=class extends Ct{constructor(t,s,i){if(super({autoDestroy:!0}),this.getKeys=Object.keys,this.replacer=Pt(s),Array.isArray(this.replacer)){let r=this.replacer;this.getKeys=n=>r.filter(o=>zt.call(n,o)),this.replacer=null;}this.space=Et(i),this._depth=0,this.error=null,this._processing=!1,this._ended=!1,this._readSize=0,this._buffer="",this._stack=null,this._visited=new WeakSet,this.pushStack({handler:()=>{this.popStack(),this.processValue({"":t},"",t,M);}});}encodeString(t){return /[^\x20-\uD799]|[\x22\x5c]/.test(t)?Lt(t):'"'+t+'"'}encodeNumber(t){return t}processValue(t,s,i,r){i=Nt(t,s,i,this.replacer);let n=Ft(i);switch(n){case It:(r!==V||i!==void 0)&&(r.call(this,s),$t.call(this,i));break;case qt:if(r.call(this,s),this._visited.has(i))return this.destroy(new TypeError("Converting circular structure to JSON"));this._visited.add(i),this._depth++,this.push("{"),this.pushStack({handler:B,value:i,index:0,first:!1,keys:this.getKeys(i)});break;case Jt:if(r.call(this,s),this._visited.has(i))return this.destroy(new TypeError("Converting circular structure to JSON"));this._visited.add(i),this.push("["),this.pushStack({handler:z,value:i,index:0}),this._depth++;break;case Mt:this.pushStack({handler:M,awaiting:!0}),Promise.resolve(i).then(c=>{this.popStack(),this.processValue(t,s,c,r),this.processStack();}).catch(c=>{this.destroy(c);});break;case Bt:case _:if(r.call(this,s),i.readableEnded||i._readableState.endEmitted)return this.destroy(new Error("Readable Stream has ended before it was serialized. All stream data have been lost"));if(i.readableFlowing)return this.destroy(new Error("Readable Stream is in flowing mode, data may have been lost. Trying to pause stream."));n===_&&(this.push("["),this.pushStack({handler:Vt,value:this.space?`
`+this.space.repeat(this._depth)+"]":"]"}),this._depth++);let o=this.pushStack({handler:n===_?L:Kt,value:i,index:0,first:!1,ended:!1,awaiting:!i.readable||i.readableLength===0}),l=()=>{o.awaiting&&(o.awaiting=!1,this.processStack());};i.once("error",c=>this.destroy(c)),i.once("end",()=>{o.ended=!0,l();}),i.on("readable",l);break}}pushStack(t){return t.prev=this._stack,this._stack=t}popStack(){let{handler:t,value:s}=this._stack;(t===B||t===z||t===L)&&(this._visited.delete(s),this._depth--),this._stack=this._stack.prev;}processStack(){if(!(this._processing||this._ended)){try{for(this._processing=!0;this._stack!==null&&!this._stack.awaiting;)if(this._stack.handler.call(this),!this._processing)return;this._processing=!1;}catch(t){this.destroy(t);return}this._stack===null&&!this._ended&&(this._finish(),this.push(null));}}push(t){if(t!==null){if(this._buffer+=t,this._buffer.length<this._readSize)return;t=this._buffer,this._buffer="",this._processing=!1;}super.push(t);}_read(t){this._readSize=t||this.readableHighWaterMark,this.processStack();}_finish(){this._ended=!0,this._processing=!1,this._stack=null,this._visited=null,this._buffer&&this._buffer.length&&super.push(this._buffer),this._buffer="";}_destroy(t,s){this.error=this.error||t,this._finish(),s(t);}};Y.exports=function(t,s,i){return new k(t,s,i)};});var H=chunkTKGT252T_js.c((se,W)=>{W.exports=chunkTKGT252T_js.a("util").TextDecoder;});var X=chunkTKGT252T_js.c((ie,Q)=>{var{isReadableStream:Yt}=g(),Gt=H(),d=1,Wt=2,Ht=new Gt;function U(e){return e!==null&&typeof e=="object"}function x(e,t){return e.name==="SyntaxError"&&t.jsonParseOffset&&(e.message=e.message.replace(/at position (\d+)/,(s,i)=>"at position "+(Number(i)+t.jsonParseOffset))),e}function Ut(e,t){let s=e.length;e.length+=t.length;for(let i=0;i<t.length;i++)e[s+i]=t[i];}Q.exports=function(e){let t=new A;if(U(e)&&Yt(e))return new Promise((s,i)=>{e.on("data",r=>{try{t.push(r);}catch(n){i(x(n,t)),t=null;}}).on("error",r=>{t=null,i(r);}).on("end",()=>{try{s(t.finish());}catch(r){i(x(r,t));}finally{t=null;}});});if(typeof e=="function"){let s=e();if(U(s)&&(Symbol.iterator in s||Symbol.asyncIterator in s))return new Promise(async(i,r)=>{try{for await(let n of s)t.push(n);i(t.finish());}catch(n){r(x(n,t));}finally{t=null;}})}throw new Error("Chunk emitter should be readable stream, generator, async generator or function returning an iterable object")};var A=class{constructor(){this.value=void 0,this.valueStack=null,this.stack=new Array(100),this.lastFlushDepth=0,this.flushDepth=0,this.stateString=!1,this.stateStringEscape=!1,this.pendingByteSeq=null,this.pendingChunk=null,this.chunkOffset=0,this.jsonParseOffset=0;}parseAndAppend(t,s){this.stack[this.lastFlushDepth-1]===d?(s&&(this.jsonParseOffset--,t="{"+t+"}"),Object.assign(this.valueStack.value,JSON.parse(t))):(s&&(this.jsonParseOffset--,t="["+t+"]"),Ut(this.valueStack.value,JSON.parse(t)));}prepareAddition(t){let{value:s}=this.valueStack;if(Array.isArray(s)?s.length!==0:Object.keys(s).length!==0){if(t[0]===",")return this.jsonParseOffset++,t.slice(1);if(t[0]!=="}"&&t[0]!=="]")return this.jsonParseOffset-=3,"[[]"+t}return t}flush(t,s,i){let r=t.slice(s,i);if(this.jsonParseOffset=this.chunkOffset+s,this.pendingChunk!==null&&(r=this.pendingChunk+r,this.jsonParseOffset-=this.pendingChunk.length,this.pendingChunk=null),this.flushDepth===this.lastFlushDepth)this.flushDepth>0?this.parseAndAppend(this.prepareAddition(r),!0):(this.value=JSON.parse(r),this.valueStack={value:this.value,prev:null});else if(this.flushDepth>this.lastFlushDepth){for(let n=this.flushDepth-1;n>=this.lastFlushDepth;n--)r+=this.stack[n]===d?"}":"]";this.lastFlushDepth===0?(this.value=JSON.parse(r),this.valueStack={value:this.value,prev:null}):this.parseAndAppend(this.prepareAddition(r),!0);for(let n=this.lastFlushDepth||1;n<this.flushDepth;n++){let o=this.valueStack.value;if(this.stack[n-1]===d){let l;for(l in o);o=o[l];}else o=o[o.length-1];this.valueStack={value:o,prev:this.valueStack};}}else {r=this.prepareAddition(r);for(let n=this.lastFlushDepth-1;n>=this.flushDepth;n--)this.jsonParseOffset--,r=(this.stack[n]===d?"{":"[")+r;this.parseAndAppend(r,!1);for(let n=this.lastFlushDepth-1;n>=this.flushDepth;n--)this.valueStack=this.valueStack.prev;}this.lastFlushDepth=this.flushDepth;}push(t){if(typeof t!="string"){if(this.pendingByteSeq!==null){let n=t;t=new Uint8Array(this.pendingByteSeq.length+n.length),t.set(this.pendingByteSeq),t.set(n,this.pendingByteSeq.length),this.pendingByteSeq=null;}if(t[t.length-1]>127)for(let n=0;n<t.length;n++){let o=t[t.length-1-n];if(o>>6===3){n++,(n!==4&&o>>3===30||n!==3&&o>>4===14||n!==2&&o>>5===6)&&(this.pendingByteSeq=t.slice(t.length-n),t=t.slice(0,-n));break}}t=Ht.decode(t);}let s=t.length,i=0,r=0;t:for(let n=0;n<s;n++){if(this.stateString){for(;n<s;n++)if(this.stateStringEscape)this.stateStringEscape=!1;else switch(t.charCodeAt(n)){case 34:this.stateString=!1;continue t;case 92:this.stateStringEscape=!0;}break}switch(t.charCodeAt(n)){case 34:this.stateString=!0,this.stateStringEscape=!1;break;case 44:r=n;break;case 123:r=n+1,this.stack[this.flushDepth++]=d;break;case 91:r=n+1,this.stack[this.flushDepth++]=Wt;break;case 93:case 125:r=n+1,this.flushDepth--,this.flushDepth<this.lastFlushDepth&&(this.flush(t,i,r),i=r);break;case 9:case 10:case 13:case 32:i===n&&i++,r===n&&r++;break}}r>i&&this.flush(t,i,r),r<s&&(this.pendingChunk!==null?this.pendingChunk+=t:this.pendingChunk=t.slice(r,s)),this.chunkOffset+=s;}finish(){return this.pendingChunk!==null&&(this.flush("",0,0),this.pendingChunk=null),this.value}};});var v=chunkTKGT252T_js.c((re,Z)=>{Z.exports={version:P(),stringifyInfo:J(),stringifyStream:G(),parseChunked:X()};});var tt=chunkTKGT252T_js.e(v());var he=async e=>(0, tt.parseChunked)(fs.createReadStream(e));

exports.a = he;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=chunk-LZXDNZPW.js.map
//# debugId=4910faaf-e841-5015-9ae4-b59cb0cd352f
