"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const promises_1 = require("node:timers/promises");
const fs = __importStar(require("fs/promises"));
const api = __importStar(require("../rest/api"));
const config_1 = __importDefault(require("../services/config"));
const prompts_1 = __importDefault(require("prompts"));
const core_1 = require("@oclif/core");
const authCommand_1 = require("./authCommand");
const project_parser_1 = require("../services/project-parser");
const checkly_config_loader_1 = require("../services/checkly-config-loader");
const constructs_1 = require("../constructs");
const chalk_1 = __importDefault(require("chalk"));
const util_1 = require("../services/util");
const common_messages_1 = __importDefault(require("../messages/common-messages"));
const snapshot_service_1 = require("../services/snapshot-service");
const browser_check_bundle_1 = require("../constructs/browser-check-bundle");
// eslint-disable-next-line no-restricted-syntax
var ResourceDeployStatus;
(function (ResourceDeployStatus) {
    ResourceDeployStatus["UPDATE"] = "UPDATE";
    ResourceDeployStatus["CREATE"] = "CREATE";
    ResourceDeployStatus["DELETE"] = "DELETE";
})(ResourceDeployStatus || (ResourceDeployStatus = {}));
class Deploy extends authCommand_1.AuthCommand {
    static coreCommand = true;
    static hidden = false;
    static description = 'Deploy your project to your Checkly account.';
    static flags = {
        preview: core_1.Flags.boolean({
            char: 'p',
            description: 'Show a preview of the changes made by the deploy command.',
            default: false,
        }),
        output: core_1.Flags.boolean({
            char: 'o',
            description: 'Shows the changes made after the deploy command.',
            default: false,
        }),
        'schedule-on-deploy': core_1.Flags.boolean({
            description: 'Enables automatic check scheduling after a deploy.',
            default: true,
            allowNo: true,
        }),
        force: core_1.Flags.boolean({
            char: 'f',
            description: common_messages_1.default.forceMode,
            default: false,
        }),
        config: core_1.Flags.string({
            char: 'c',
            description: common_messages_1.default.configFile,
        }),
        'verify-runtime-dependencies': core_1.Flags.boolean({
            description: '[default: true] Return an error if checks import dependencies that are not supported by the selected runtime.',
            default: true,
            allowNo: true,
            env: 'CHECKLY_VERIFY_RUNTIME_DEPENDENCIES',
        }),
        'debug-bundle': core_1.Flags.boolean({
            description: 'Output the project bundle to a file without deploying any resources.',
            default: false,
            hidden: true,
        }),
        'debug-bundle-output-file': core_1.Flags.string({
            description: 'The file to output the debug debug bundle to.',
            default: './debug-bundle.json',
            hidden: true,
        }),
    };
    async run() {
        this.style.actionStart('Parsing your project');
        const { flags } = await this.parse(Deploy);
        const { force, preview, 'schedule-on-deploy': scheduleOnDeploy, output, config: configFilename, 'verify-runtime-dependencies': verifyRuntimeDependencies, 'debug-bundle': debugBundle, 'debug-bundle-output-file': debugBundleOutputFile, } = flags;
        const { configDirectory, configFilenames } = (0, util_1.splitConfigFilePath)(configFilename);
        const { config: checklyConfig, constructs: checklyConfigConstructs, } = await (0, checkly_config_loader_1.loadChecklyConfig)(configDirectory, configFilenames);
        const { data: account } = await api.accounts.get(config_1.default.getAccountId());
        const { data: avilableRuntimes } = await api.runtimes.getAll();
        const project = await (0, project_parser_1.parseProject)({
            directory: configDirectory,
            projectLogicalId: checklyConfig.logicalId,
            projectName: checklyConfig.projectName,
            repoUrl: checklyConfig.repoUrl,
            checkMatch: checklyConfig.checks?.checkMatch,
            browserCheckMatch: checklyConfig.checks?.browserChecks?.testMatch,
            multiStepCheckMatch: checklyConfig.checks?.multiStepChecks?.testMatch,
            ignoreDirectoriesMatch: checklyConfig.checks?.ignoreDirectoriesMatch,
            checkDefaults: checklyConfig.checks,
            browserCheckDefaults: checklyConfig.checks?.browserChecks,
            availableRuntimes: avilableRuntimes.reduce((acc, runtime) => {
                acc[runtime.name] = runtime;
                return acc;
            }, {}),
            defaultRuntimeId: account.runtimeId,
            verifyRuntimeDependencies,
            checklyConfigConstructs,
            playwrightConfigPath: checklyConfig.checks?.playwrightConfigPath,
            include: checklyConfig.checks?.include,
            playwrightChecks: checklyConfig.checks?.playwrightChecks,
        });
        const repoInfo = (0, util_1.getGitInformation)(project.repoUrl);
        this.style.actionSuccess();
        this.style.actionStart('Validating project resources');
        const diagnostics = new constructs_1.Diagnostics();
        await project.validate(diagnostics);
        for (const diag of diagnostics.observations) {
            if (diag.isFatal()) {
                this.style.longError(diag.title, diag.message);
            }
            else if (!diag.isBenign()) {
                this.style.longWarning(diag.title, diag.message);
            }
            else {
                this.style.longInfo(diag.title, diag.message);
            }
        }
        if (diagnostics.isFatal()) {
            this.style.actionFailure();
            this.style.shortError(`Unable to continue due to unresolved validation errors.`);
            this.exit(1);
        }
        this.style.actionSuccess();
        this.style.actionStart('Bundling project resources');
        const projectBundle = await (async () => {
            try {
                const bundle = await project.bundle();
                this.style.actionSuccess();
                return bundle;
            }
            catch (err) {
                this.style.actionFailure();
                throw err;
            }
        })();
        if (!preview) {
            for (const { bundle: check } of Object.values(projectBundle.data.check)) {
                if (!(check instanceof browser_check_bundle_1.BrowserCheckBundle)) {
                    continue;
                }
                check.snapshots = await (0, snapshot_service_1.uploadSnapshots)(check.rawSnapshots);
            }
        }
        const projectPayload = projectBundle.synthesize();
        if (!projectPayload.resources.length) {
            if (preview) {
                this.log('\nNo checks were detected. More information on how to set up a Checkly CLI project is available at https://checklyhq.com/docs/cli/.\n');
                return;
            }
            else {
                throw new Error('Failed to deploy your project. Unable to find constructs to deploy.\nMore information on how to set up a Checkly CLI project is available at https://checklyhq.com/docs/cli/.\n');
            }
        }
        if (debugBundle) {
            const output = JSON.stringify(projectPayload, null, 2);
            await fs.writeFile(debugBundleOutputFile, output, 'utf8');
            this.log(`Successfully wrote debug bundle to "${debugBundleOutputFile}".`);
            return;
        }
        if (!force && !preview) {
            const { confirm } = await (0, prompts_1.default)({
                name: 'confirm',
                type: 'confirm',
                message: `You are about to deploy your project "${project.name}" to account "${account.name}". Do you want to continue?`,
            });
            if (!confirm) {
                return;
            }
        }
        try {
            const { data } = await api.projects.deploy({ ...projectPayload, repoInfo }, { dryRun: preview, scheduleOnDeploy });
            if (preview || output) {
                this.log(this.formatPreview(data, project));
            }
            if (!preview) {
                await (0, promises_1.setTimeout)(500);
                this.log(`Successfully deployed project "${project.name}" to account "${account.name}".`);
                // Print the ping URL for heartbeat checks.
                const heartbeatLogicalIds = project.getHeartbeatLogicalIds();
                const heartbeatCheckIds = data.diff.filter((check) => heartbeatLogicalIds.includes(check.logicalId))
                    .map(check => check?.physicalId);
                heartbeatCheckIds.forEach(async (id) => {
                    const { data: { pingUrl, name } } = await api.heartbeatCheck.get(id);
                    this.log(`Ping URL of heartbeat check ${chalk_1.default.green(name)} is ${chalk_1.default.italic.underline.blue(pingUrl)}.`);
                });
            }
        }
        catch (err) {
            if (err?.response?.status === 400) {
                throw new Error(`Failed to deploy your project due to wrong configuration. ${err.response.data?.message}`);
            }
            else {
                throw new Error(`Failed to deploy your project. ${err.message}`);
            }
        }
    }
    formatPreview(previewData, project) {
        // Current format of the data is: { checks: { logical-id-1: 'UPDATE' }, groups: { another-logical-id: 'CREATE' } }
        // We convert it into update: [{ logicalId, resourceType, construct }, ...], create: [], delete: []
        // This makes it easier to display.
        const updating = [];
        const creating = [];
        const deleting = [];
        for (const change of previewData?.diff ?? []) {
            const { type, logicalId, action } = change;
            if ([
                constructs_1.AlertChannelSubscription.__checklyType,
                constructs_1.PrivateLocationCheckAssignment.__checklyType,
                constructs_1.PrivateLocationGroupAssignment.__checklyType,
            ].some(t => t === type)) {
                // Don't report changes to alert channel subscriptions or private location assignments.
                // Users don't create these directly, so it's more intuitive to consider it as part of the check.
                continue;
            }
            const construct = project.data[type][logicalId];
            if (action === ResourceDeployStatus.UPDATE) {
                updating.push({ resourceType: type, logicalId, construct });
            }
            else if (action === ResourceDeployStatus.CREATE) {
                creating.push({ resourceType: type, logicalId, construct });
            }
            else if (action === ResourceDeployStatus.DELETE) {
                // Since the resource is being deleted, the construct isn't in the project.
                deleting.push({ resourceType: type, logicalId });
            }
        }
        // testOnly checks weren't sent to the BE and won't be in previewData.
        // We load them from the `project` instead.
        const skipping = project
            .getTestOnlyConstructs().map(construct => ({
            logicalId: construct.logicalId,
            resourceType: construct.type,
            construct,
        }))
            // There is an edge case when the check already exists in Checkly, but `testOnly: true` was just added.
            // In this case, the check will be included in both `deleting` and `skipping`.
            // To avoid displaying the check twice, we detect this case and only show the check in `deleting`.
            // This implementation is O(n^2), but could be sped up with a map or set.
            .filter((skip) => !deleting.find(deletion => deletion.logicalId === skip.logicalId && deletion.resourceType === skip.resourceType));
        // Having some order will make the output easier to read.
        const compareEntries = (a, b) => a.resourceType.localeCompare(b.resourceType) ||
            a.logicalId.localeCompare(b.logicalId);
        // filter resources without contructs that are created dynamically
        // on the flight (i.e. a non project member private-location)
        const sortedUpdating = updating
            .filter(({ construct }) => Boolean(construct))
            .sort(compareEntries);
        // filter resources without contructs that are created dynamically
        // on the flight (i.e. a non project member private-location)
        const sortedCreating = creating
            .filter(({ construct }) => Boolean(construct))
            .sort(compareEntries);
        const sortedDeleting = deleting
            .sort(compareEntries);
        if (!sortedCreating.length && !sortedDeleting.length && !sortedUpdating.length && !skipping.length) {
            return '\nNo checks were detected. More information on how to set up a Checkly CLI project is available at https://checklyhq.com/docs/cli/.\n';
        }
        const output = [];
        if (sortedCreating.filter(({ construct }) => Boolean(construct)).length) {
            output.push(chalk_1.default.bold.green('Create:'));
            for (const { logicalId, construct } of sortedCreating) {
                output.push(`    ${construct.constructor.name}: ${logicalId}`);
            }
            output.push('');
        }
        if (sortedDeleting.length) {
            output.push(chalk_1.default.bold.red('Delete:'));
            const prettyResourceTypes = {
                [constructs_1.Check.__checklyType]: 'Check',
                [constructs_1.AlertChannel.__checklyType]: 'AlertChannel',
                [constructs_1.CheckGroup.__checklyType]: 'CheckGroup',
                [constructs_1.MaintenanceWindow.__checklyType]: 'MaintenanceWindow',
                [constructs_1.PrivateLocation.__checklyType]: 'PrivateLocation',
                [constructs_1.Dashboard.__checklyType]: 'Dashboard',
            };
            for (const { resourceType, logicalId } of sortedDeleting) {
                output.push(`    ${prettyResourceTypes[resourceType] ?? resourceType}: ${logicalId}`);
            }
            output.push('');
        }
        if (sortedUpdating.length) {
            output.push(chalk_1.default.bold.magenta('Update and Unchanged:'));
            for (const { logicalId, construct } of sortedUpdating) {
                output.push(`    ${construct.constructor.name}: ${logicalId}`);
            }
            output.push('');
        }
        if (skipping.length) {
            output.push(chalk_1.default.bold.grey('Skip (testOnly):'));
            for (const { logicalId, construct } of skipping) {
                output.push(`    ${construct.constructor.name}: ${logicalId}`);
            }
            output.push('');
        }
        return output.join('\n');
    }
}
exports.default = Deploy;
//# sourceMappingURL=deploy.js.map