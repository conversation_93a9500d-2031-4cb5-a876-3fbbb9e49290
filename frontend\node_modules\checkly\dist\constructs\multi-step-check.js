"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MultiStepCheck = void 0;
const promises_1 = __importDefault(require("node:fs/promises"));
const check_1 = require("./check");
const project_1 = require("./project");
const construct_1 = require("./construct");
const constants_1 = __importDefault(require("../constants"));
const construct_diagnostics_1 = require("./construct-diagnostics");
const multi_step_check_bundle_1 = require("./multi-step-check-bundle");
/**
 * Creates a multi-step Check
 *
 * @remarks
 *
 * This class make use of the multi-step checks endpoints.
 */
class MultiStepCheck extends check_1.Check {
    code;
    playwrightConfig;
    /**
     * Constructs the multi-step instance
     *
     * @param logicalId unique project-scoped resource name identification
     * @param props check configuration properties
     * {@link https://checklyhq.com/docs/cli/constructs-reference/#multistepcheck Read more in the docs}
     */
    constructor(logicalId, props) {
        if (props.group) {
            MultiStepCheck.applyDefaultMultiStepCheckGroupConfig(props, props.group.getMultiStepCheckDefaults());
        }
        MultiStepCheck.applyDefaultMultiStepCheckConfig(props);
        super(logicalId, props);
        this.code = props.code;
        this.playwrightConfig = props.playwrightConfig;
        project_1.Session.registerConstruct(this);
        this.addSubscriptions();
        this.addPrivateLocationCheckAssignments();
    }
    async validate(diagnostics) {
        if (!(0, construct_1.isEntrypoint)(this.code) && !(0, construct_1.isContent)(this.code)) {
            diagnostics.add(new construct_diagnostics_1.InvalidPropertyValueDiagnostic('code', new Error(`Either "entrypoint" or "content" is required.`)));
        }
        else if ((0, construct_1.isEntrypoint)(this.code) && (0, construct_1.isContent)(this.code)) {
            diagnostics.add(new construct_diagnostics_1.InvalidPropertyValueDiagnostic('code', new Error(`Provide exactly one of "entrypoint" or "content", but not both.`)));
        }
        else if ((0, construct_1.isEntrypoint)(this.code)) {
            const entrypoint = this.resolveContentFilePath(this.code.entrypoint);
            try {
                const stats = await promises_1.default.stat(entrypoint);
                if (stats.size === 0) {
                    diagnostics.add(new construct_diagnostics_1.InvalidPropertyValueDiagnostic('code', new Error(`The entrypoint file "${entrypoint}" must not be empty.`)));
                }
            }
            catch (err) {
                diagnostics.add(new construct_diagnostics_1.InvalidPropertyValueDiagnostic('code', new Error(`Unable to access entrypoint file "${entrypoint}": ${err.message}`, { cause: err })));
            }
        }
        const runtime = project_1.Session.getRuntime(this.runtimeId);
        if (runtime) {
            if (!runtime.multiStepSupport) {
                diagnostics.add(new construct_diagnostics_1.UnsupportedRuntimeFeatureDiagnostic(runtime.name, new Error(`Multi-Step Checks are not supported.`)));
            }
        }
    }
    static applyDefaultMultiStepCheckGroupConfig(props, groupProps) {
        let configKey;
        for (configKey in groupProps) {
            const newVal = props[configKey] ?? groupProps[configKey];
            props[configKey] = newVal;
        }
    }
    static applyDefaultMultiStepCheckConfig(props) {
        if (!project_1.Session.multiStepCheckDefaults) {
            return;
        }
        let configKey;
        for (configKey in project_1.Session.multiStepCheckDefaults) {
            const newVal = props[configKey] ?? project_1.Session.multiStepCheckDefaults[configKey];
            props[configKey] = newVal;
        }
    }
    static async bundle(entry, runtimeId) {
        const runtime = project_1.Session.getRuntime(runtimeId);
        if (!runtime) {
            throw new Error(`${runtimeId} is not supported`);
        }
        const parser = project_1.Session.getParser(runtime);
        const parsed = await parser.parse(entry);
        // Maybe we can get the parsed deps with the content immediately
        const deps = [];
        for (const { filePath, content } of parsed.dependencies) {
            deps.push(project_1.Session.registerSharedFile({
                path: project_1.Session.relativePosixPath(filePath),
                content,
            }));
        }
        return {
            script: parsed.entrypoint.content,
            scriptPath: project_1.Session.relativePosixPath(parsed.entrypoint.filePath),
            dependencies: deps,
        };
    }
    getSourceFile() {
        return this.__checkFilePath;
    }
    async bundle() {
        return new multi_step_check_bundle_1.MultiStepCheckBundle(this, await (async () => {
            if ((0, construct_1.isEntrypoint)(this.code)) {
                const bundle = await MultiStepCheck.bundle(this.resolveContentFilePath(this.code.entrypoint), this.runtimeId);
                if (!bundle.script) {
                    throw new Error(`The "code" property must not point to an empty file.`);
                }
                return bundle;
            }
            const script = this.code.content;
            return {
                script,
            };
        })());
    }
    synthesize() {
        return {
            ...super.synthesize(),
            checkType: constants_1.default.MULTI_STEP,
            playwrightConfig: this.playwrightConfig,
        };
    }
}
exports.MultiStepCheck = MultiStepCheck;
//# sourceMappingURL=multi-step-check.js.map