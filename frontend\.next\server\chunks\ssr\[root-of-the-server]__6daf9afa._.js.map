{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/app/%5Blocale%5D/%28auth%29/layout.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';\r\nimport { setRequestLocale } from 'next-intl/server';\r\nimport { routing } from '@/libs/I18nRouting';\r\nimport { ClerkLocalizations } from '@/utils/AppConfig';\r\n\r\nexport default async function AuthLayout(props: {\r\n  children: React.ReactNode;\r\n  params: Promise<{ locale: string }>;\r\n}) {\r\n  const { locale } = await props.params;\r\n  setRequestLocale(locale);\r\n\r\n  const clerkLocale = ClerkLocalizations.supportedLocales[locale] ?? ClerkLocalizations.defaultLocale;\r\n  let signInUrl = '/sign-in';\r\n  let signUpUrl = '/sign-up';\r\n  let dashboardUrl = '/dashboard';\r\n  let afterSignOutUrl = '/';\r\n\r\n  if (locale !== routing.defaultLocale) {\r\n    signInUrl = `/${locale}${signInUrl}`;\r\n    signUpUrl = `/${locale}${signUpUrl}`;\r\n    dashboardUrl = `/${locale}${dashboardUrl}`;\r\n    afterSignOutUrl = `/${locale}${afterSignOutUrl}`;\r\n  }\r\n\r\n  return (\r\n    <ClerkProvider\r\n      localization={clerkLocale}\r\n      signInUrl={signInUrl}\r\n      signUpUrl={signUpUrl}\r\n      signInFallbackRedirectUrl={dashboardUrl}\r\n      signUpFallbackRedirectUrl={dashboardUrl}\r\n      afterSignOutUrl={afterSignOutUrl}\r\n    >\r\n      {props.children}\r\n    </ClerkProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,eAAe,WAAW,KAGxC;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,CAAA,GAAA,2QAAA,CAAA,mBAAgB,AAAD,EAAE;IAEjB,MAAM,cAAc,yHAAA,CAAA,qBAAkB,CAAC,gBAAgB,CAAC,OAAO,IAAI,yHAAA,CAAA,qBAAkB,CAAC,aAAa;IACnG,IAAI,YAAY;IAChB,IAAI,YAAY;IAChB,IAAI,eAAe;IACnB,IAAI,kBAAkB;IAEtB,IAAI,WAAW,0HAAA,CAAA,UAAO,CAAC,aAAa,EAAE;QACpC,YAAY,CAAC,CAAC,EAAE,SAAS,WAAW;QACpC,YAAY,CAAC,CAAC,EAAE,SAAS,WAAW;QACpC,eAAe,CAAC,CAAC,EAAE,SAAS,cAAc;QAC1C,kBAAkB,CAAC,CAAC,EAAE,SAAS,iBAAiB;IAClD;IAEA,qBACE,8OAAC,yKAAA,CAAA,gBAAa;QACZ,cAAc;QACd,WAAW;QACX,WAAW;QACX,2BAA2B;QAC3B,2BAA2B;QAC3B,iBAAiB;kBAEhB,MAAM,QAAQ;;;;;;AAGrB", "debugId": null}}]}