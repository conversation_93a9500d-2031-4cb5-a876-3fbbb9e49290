import { getTranslations, setRequestLocale } from 'next-intl/server';
import { Sponsors } from '@/components/Sponsors';

type IIndexProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IIndexProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Index',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

export default async function Index(props: IIndexProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);
  const t = await getTranslations({
    locale,
    namespace: 'Index',
  });

  return (
    <>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-secondary-50 to-white section-padding overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="container-wide relative">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-hero text-gradient mb-6 animate-fade-in-up">
              {t('hero_title')}
            </h1>
            <p className="text-xl text-secondary-600 mb-12 text-balance animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
              {t('hero_description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <button className="btn-gradient text-lg px-8 py-4 shadow-colored">
                {t('get_started_free')}
              </button>
              <button className="btn-outline text-lg px-8 py-4">
                {t('watch_demo')}
              </button>
            </div>

            {/* Hero Image/Video Placeholder */}
            <div className="relative max-w-5xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
              <div className="aspect-video rounded-2xl overflow-hidden shadow-large border border-secondary-200">
                <div className="w-full h-full bg-gradient-primary flex items-center justify-center relative">
                  <div className="absolute inset-0 bg-black/10"></div>
                  <div className="text-center text-white relative z-10">
                    <div className="w-20 h-20 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-lg font-medium">{t('demo_video_placeholder')}</p>
                  </div>
                </div>
              </div>
              {/* Floating elements for visual interest */}
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-accent-500 rounded-full opacity-60 animate-pulse"></div>
              <div className="absolute -bottom-4 -right-4 w-6 h-6 bg-primary-500 rounded-full opacity-60 animate-pulse" style={{ animationDelay: '1s' }}></div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Overview */}
      <section className="section-padding bg-white">
        <div className="container-wide">
          <div className="text-center mb-16">
            <h2 className="text-section text-secondary-900 mb-6">
              {t('features_title')}
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto text-balance">
              {t('features_description')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="card-hover text-center p-8 group">
              <div className="w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-primary-500 group-hover:text-white transition-all duration-300">
                <svg className="w-8 h-8 text-primary-600 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                {t('feature_1_title')}
              </h3>
              <p className="text-secondary-600">
                {t('feature_1_description')}
              </p>
            </div>

            <div className="card-hover text-center p-8 group">
              <div className="w-16 h-16 bg-accent-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-accent-500 group-hover:text-white transition-all duration-300">
                <svg className="w-8 h-8 text-accent-600 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                {t('feature_2_title')}
              </h3>
              <p className="text-secondary-600">
                {t('feature_2_description')}
              </p>
            </div>

            <div className="card-hover text-center p-8 group">
              <div className="w-16 h-16 bg-success-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-success-500 group-hover:text-white transition-all duration-300">
                <svg className="w-8 h-8 text-success-600 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                {t('feature_3_title')}
              </h3>
              <p className="text-secondary-600">
                {t('feature_3_description')}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-section text-gray-900 mb-6">
              {t('trusted_by_title')}
            </h2>
            <p className="text-xl text-gray-600">
              {t('trusted_by_description')}
            </p>
          </div>
          <Sponsors />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-primary-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-section text-white mb-6">
            {t('cta_title')}
          </h2>
          <p className="text-xl text-primary-100 mb-8">
            {t('cta_description')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-primary-600 hover:bg-gray-50 font-medium px-8 py-4 rounded-lg transition-colors duration-200">
              {t('start_free_trial')}
            </button>
            <button className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium px-8 py-4 rounded-lg transition-all duration-200">
              {t('contact_sales')}
            </button>
          </div>
        </div>
      </section>
    </div >
  );
};
