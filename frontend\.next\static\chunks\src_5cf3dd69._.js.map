{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/utils/AppConfig.ts"], "sourcesContent": ["import type { LocalizationResource } from '@clerk/types';\r\nimport type { LocalePrefixMode } from 'next-intl/routing';\r\nimport { enUS, frFR } from '@clerk/localizations';\r\n\r\nconst localePrefix: LocalePrefixMode = 'as-needed';\r\n\r\n// AppExtera configuration\r\nexport const AppConfig = {\r\n  name: 'AppExtera',\r\n  locales: ['en', 'fr'],\r\n  defaultLocale: 'en',\r\n  localePrefix,\r\n};\r\n\r\nconst supportedLocales: Record<string, LocalizationResource> = {\r\n  en: enUS,\r\n  fr: frFR,\r\n};\r\n\r\nexport const ClerkLocalizations = {\r\n  defaultLocale: enUS,\r\n  supportedLocales,\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;;AAEA,MAAM,eAAiC;AAGhC,MAAM,YAAY;IACvB,MAAM;IACN,SAAS;QAAC;QAAM;KAAK;IACrB,eAAe;IACf;AACF;AAEA,MAAM,mBAAyD;IAC7D,IAAI,6JAAA,CAAA,OAAI;IACR,IAAI,6JAAA,CAAA,OAAI;AACV;AAEO,MAAM,qBAAqB;IAChC,eAAe,6JAAA,CAAA,OAAI;IACnB;AACF", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/libs/I18nRouting.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\r\nimport { AppConfig } from '@/utils/AppConfig';\r\n\r\nexport const routing = defineRouting({\r\n  locales: AppConfig.locales,\r\n  localePrefix: AppConfig.localePrefix,\r\n  defaultLocale: AppConfig.defaultLocale,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,qOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,SAAS,4HAAA,CAAA,YAAS,CAAC,OAAO;IAC1B,cAAc,4HAAA,CAAA,YAAS,CAAC,YAAY;IACpC,eAAe,4HAAA,CAAA,YAAS,CAAC,aAAa;AACxC", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/libs/I18nNavigation.ts"], "sourcesContent": ["import { createNavigation } from 'next-intl/navigation';\r\nimport { routing } from './I18nRouting';\r\n\r\nexport const { usePathname } = createNavigation(routing);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,iQAAA,CAAA,mBAAgB,AAAD,EAAE,6HAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/LocaleSwitcher.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ChangeEventHandler } from 'react';\r\nimport { useLocale } from 'next-intl';\r\nimport { useRouter } from 'next/navigation';\r\nimport { usePathname } from '@/libs/I18nNavigation';\r\nimport { routing } from '@/libs/I18nRouting';\r\n\r\nexport const LocaleSwitcher = () => {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const locale = useLocale();\r\n\r\n  const handleChange: ChangeEventHandler<HTMLSelectElement> = (event) => {\r\n    router.push(`/${event.target.value}${pathname}`);\r\n    router.refresh(); // Ensure the page takes the new locale into account related to the issue #395\r\n  };\r\n\r\n  return (\r\n    <select\r\n      defaultValue={locale}\r\n      onChange={handleChange}\r\n      className=\"border border-gray-300 font-medium focus:outline-hidden focus-visible:ring-3\"\r\n      aria-label=\"lang-switcher\"\r\n    >\r\n      {routing.locales.map(elt => (\r\n        <option key={elt} value={elt}>\r\n          {elt.toUpperCase()}\r\n        </option>\r\n      ))}\r\n    </select>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;;;AANA;;;;;AAQO,MAAM,iBAAiB;;IAC5B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAsD,CAAC;QAC3D,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,KAAK,GAAG,UAAU;QAC/C,OAAO,OAAO,IAAI,8EAA8E;IAClG;IAEA,qBACE,6LAAC;QACC,cAAc;QACd,UAAU;QACV,WAAU;QACV,cAAW;kBAEV,6HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,oBACnB,6LAAC;gBAAiB,OAAO;0BACtB,IAAI,WAAW;eADL;;;;;;;;;;AAMrB;GAxBa;;QACI,qIAAA,CAAA,YAAS;QACP,gIAAA,CAAA,cAAW;QACb,qKAAA,CAAA,YAAS;;;KAHb", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useTranslations } from 'next-intl';\nimport Link from 'next/link';\nimport { LocaleSwitcher } from '@/components/LocaleSwitcher';\nimport { AppConfig } from '@/utils/AppConfig';\n\nexport const Navigation = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const t = useTranslations('RootLayout');\n\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\n\n  return (\n    <nav className=\"bg-white/95 backdrop-blur-sm border-b border-secondary-200 sticky top-0 z-50\">\n      <div className=\"container-wide\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">A</span>\n              </div>\n              <span className=\"text-xl font-bold text-secondary-900\">\n                {AppConfig.name}\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-8\">\n              <Link\n                href=\"/\"\n                className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {t('home_link')}\n              </Link>\n              <Link\n                href=\"/features/\"\n                className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {t('features_link')}\n              </Link>\n              <Link\n                href=\"/pricing/\"\n                className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {t('pricing_link')}\n              </Link>\n              <Link\n                href=\"/about/\"\n                className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {t('about_link')}\n              </Link>\n              <Link\n                href=\"/blog/\"\n                className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {t('blog_link')}\n              </Link>\n              <Link\n                href=\"/contact/\"\n                className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {t('contact_link')}\n              </Link>\n            </div>\n          </div>\n\n          {/* Desktop Right Side */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <LocaleSwitcher />\n            <Link\n              href=\"/sign-in/\"\n              className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n            >\n              {t('sign_in_link')}\n            </Link>\n            <Link\n              href=\"/sign-up/\"\n              className=\"btn-primary text-sm\"\n            >\n              {t('sign_up_link')}\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={toggleMenu}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-secondary-700 hover:text-primary-600 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\"\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {!isMenuOpen ? (\n                <svg className=\"block h-6 w-6\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" aria-hidden=\"true\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              ) : (\n                <svg className=\"block h-6 w-6\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" aria-hidden=\"true\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-secondary-200\">\n              <Link\n                href=\"/\"\n                className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('home_link')}\n              </Link>\n              <Link\n                href=\"/features/\"\n                className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('features_link')}\n              </Link>\n              <Link\n                href=\"/pricing/\"\n                className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('pricing_link')}\n              </Link>\n              <Link\n                href=\"/about/\"\n                className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('about_link')}\n              </Link>\n              <Link\n                href=\"/blog/\"\n                className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('blog_link')}\n              </Link>\n              <Link\n                href=\"/contact/\"\n                className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('contact_link')}\n              </Link>\n              <div className=\"border-t border-secondary-200 pt-4\">\n                <div className=\"flex items-center px-3 space-x-3\">\n                  <LocaleSwitcher />\n                </div>\n                <Link\n                  href=\"/sign-in/\"\n                  className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {t('sign_in_link')}\n                </Link>\n                <Link\n                  href=\"/sign-up/\"\n                  className=\"btn-primary block mx-3 mt-2 text-center\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {t('sign_up_link')}\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQO,MAAM,aAAa;;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAK,WAAU;kDACb,4HAAA,CAAA,YAAS,CAAC,IAAI;;;;;;;;;;;;;;;;;sCAMrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;;;;;;;;;;;;sCAMT,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uIAAA,CAAA,iBAAc;;;;;8CACf,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;8CAEL,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;;;;;;;sCAKP,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,iBAAc;;kDAEd,6LAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,CAAC,2BACA,6LAAC;wCAAI,WAAU;wCAAgB,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;wCAAe,eAAY;kDAClI,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;6DAGvE,6LAAC;wCAAI,WAAU;wCAAgB,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;wCAAe,eAAY;kDAClI,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ9E,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uIAAA,CAAA,iBAAc;;;;;;;;;;kDAEjB,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAE5B,EAAE;;;;;;kDAEL,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAE5B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GA7Ka;;QAED,yMAAA,CAAA,kBAAe;;;KAFd", "debugId": null}}]}