/**
 * The MIT License (MIT)
 * Copyright (c) 2017-present <PERSON> <<EMAIL>>
 */

'use strict';

function main() {
  console.info('\n  =========================================================\n  * CLI commands are moved to the \x1B[1mregexp-tree-cli\x1B[0m package *\n  =========================================================\n\n  \x1B[1mInstallation:\x1B[0m\n\n    npm install -g regexp-tree-cli\n\n  \x1B[1mUsage:\x1B[0m\n\n    regexp-tree-cli --help\n  ');
}

module.exports = main;

if (require.main === module) {
  main();
}