"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertEscalationBuilder = void 0;
// eslint-disable-next-line no-restricted-syntax
var AlertEscalationType;
(function (AlertEscalationType) {
    AlertEscalationType["RUN"] = "RUN_BASED";
    AlertEscalationType["TIME"] = "TIME_BASED";
})(AlertEscalationType || (AlertEscalationType = {}));
class AlertEscalationBuilder {
    static DEFAULT_RUN_BASED_ESCALATION = { failedRunThreshold: 1 };
    static DEFAULT_TIME_BASED_ESCALATION = { minutesFailingThreshold: 5 };
    static DEFAULT_REMINDERS = { amount: 0, interval: 5 };
    static DEFAULT_PARALLEL_RUN_FAILURE_THRESHOLD = { enabled: false, percentage: 10 };
    static runBasedEscalation(failedRunThreshold, reminders, parallelRunFailureThreshold) {
        const options = {
            runBasedEscalation: {
                failedRunThreshold,
            },
            reminders,
            parallelRunFailureThreshold,
        };
        return this.alertEscalation(AlertEscalationType.RUN, options);
    }
    static timeBasedEscalation(minutesFailingThreshold, reminders, parallelRunFailureThreshold) {
        const options = {
            timeBasedEscalation: {
                minutesFailingThreshold,
            },
            reminders,
            parallelRunFailureThreshold,
        };
        return this.alertEscalation(AlertEscalationType.TIME, options);
    }
    static alertEscalation(escalationType, options) {
        return {
            escalationType,
            runBasedEscalation: options.runBasedEscalation ?? this.DEFAULT_RUN_BASED_ESCALATION,
            timeBasedEscalation: options.timeBasedEscalation ?? this.DEFAULT_TIME_BASED_ESCALATION,
            reminders: options.reminders ?? this.DEFAULT_REMINDERS,
            parallelRunFailureThreshold: options.parallelRunFailureThreshold ?? this.DEFAULT_PARALLEL_RUN_FAILURE_THRESHOLD,
        };
    }
}
exports.AlertEscalationBuilder = AlertEscalationBuilder;
//# sourceMappingURL=alert-escalation-policy.js.map