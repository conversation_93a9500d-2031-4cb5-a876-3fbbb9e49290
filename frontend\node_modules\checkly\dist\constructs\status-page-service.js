"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatusPageService = exports.StatusPageServiceRef = void 0;
const construct_1 = require("./construct");
const project_1 = require("./project");
/**
 * Creates a reference to an existing Status Page Service.
 *
 * References link existing resources to a project without managing them.
 */
class StatusPageServiceRef extends construct_1.Construct {
    constructor(logicalId, physicalId) {
        super(StatusPageService.__checklyType, logicalId, physicalId, false);
        project_1.Session.registerConstruct(this);
    }
    synthesize() {
        return null;
    }
}
exports.StatusPageServiceRef = StatusPageServiceRef;
/**
 * Creates a Service for Status Pages
 */
class StatusPageService extends construct_1.Construct {
    name;
    static __checklyType = 'status-page-service';
    /**
     * Constructs the Status Page Service instance
     *
     * @param logicalId unique project-scoped resource name identification
     * @param props status page service configuration properties
     *
     * {@link https://checklyhq.com/docs/cli/constructs-reference/#statuspageservice Read more in the docs}
     */
    constructor(logicalId, props) {
        super(StatusPageService.__checklyType, logicalId);
        this.name = props.name;
        project_1.Session.registerConstruct(this);
    }
    static fromId(id) {
        return new StatusPageServiceRef(`status-page-service-${id}`, id);
    }
    synthesize() {
        return {
            name: this.name,
        };
    }
}
exports.StatusPageService = StatusPageService;
//# sourceMappingURL=status-page-service.js.map