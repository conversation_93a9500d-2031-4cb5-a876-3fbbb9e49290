Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

/*
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// eslint-disable-next-line jsdoc/require-jsdoc
class LCPEntryManager {
  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility

  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility, jsdoc/require-jsdoc
  _processEntry(entry) {
    this._onBeforeProcessingEntry?.(entry);
  }
}

exports.LCPEntryManager = LCPEntryManager;
//# sourceMappingURL=LCPEntryManager.js.map
