var luxon=function(e){"use strict";function L(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"==typeof e?e:String(e)}(r.key),r)}}function i(e,t,n){t&&L(e.prototype,t),n&&L(e,n),Object.defineProperty(e,"prototype",{writable:!1})}function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n,r=arguments[t];for(n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function o(e,t){e.prototype=Object.create(t.prototype),z(e.prototype.constructor=e,t)}function j(e){return(j=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function z(e,t){return(z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function A(e,t,n){return(A=function(){if("undefined"!=typeof Reflect&&Reflect.construct&&!Reflect.construct.sham){if("function"==typeof Proxy)return 1;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),1}catch(e){}}}()?Reflect.construct.bind():function(e,t,n){var r=[null];r.push.apply(r,t);t=new(Function.bind.apply(e,r));return n&&z(t,n.prototype),t}).apply(null,arguments)}function q(e){var n="function"==typeof Map?new Map:void 0;return function(e){if(null===e||-1===Function.toString.call(e).indexOf("[native code]"))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==n){if(n.has(e))return n.get(e);n.set(e,t)}function t(){return A(e,arguments,j(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),z(t,e)}(e)}function _(e,t){if(null==e)return{};for(var n,r={},i=Object.keys(e),o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||(r[n]=e[n]);return r}function U(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function R(e,t){var n,r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(r)return(r=r.call(e)).next.bind(r);if(Array.isArray(e)||(r=function(e,t){var n;if(e)return"string"==typeof e?U(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?U(e,t):void 0}(e))||t&&e&&"number"==typeof e.length)return r&&(e=r),n=0,function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var t=function(e){function t(){return e.apply(this,arguments)||this}return o(t,e),t}(q(Error)),P=function(t){function e(e){return t.call(this,"Invalid DateTime: "+e.toMessage())||this}return o(e,t),e}(t),Y=function(t){function e(e){return t.call(this,"Invalid Interval: "+e.toMessage())||this}return o(e,t),e}(t),H=function(t){function e(e){return t.call(this,"Invalid Duration: "+e.toMessage())||this}return o(e,t),e}(t),w=function(e){function t(){return e.apply(this,arguments)||this}return o(t,e),t}(t),J=function(t){function e(e){return t.call(this,"Invalid unit "+e)||this}return o(e,t),e}(t),u=function(e){function t(){return e.apply(this,arguments)||this}return o(t,e),t}(t),n=function(e){function t(){return e.call(this,"Zone is an abstract class")||this}return o(t,e),t}(t),t="numeric",r="short",a="long",G={year:t,month:t,day:t},$={year:t,month:r,day:t},B={year:t,month:r,day:t,weekday:r},Q={year:t,month:a,day:t},K={year:t,month:a,day:t,weekday:a},X={hour:t,minute:t},ee={hour:t,minute:t,second:t},te={hour:t,minute:t,second:t,timeZoneName:r},ne={hour:t,minute:t,second:t,timeZoneName:a},re={hour:t,minute:t,hourCycle:"h23"},ie={hour:t,minute:t,second:t,hourCycle:"h23"},oe={hour:t,minute:t,second:t,hourCycle:"h23",timeZoneName:r},ae={hour:t,minute:t,second:t,hourCycle:"h23",timeZoneName:a},se={year:t,month:t,day:t,hour:t,minute:t},ue={year:t,month:t,day:t,hour:t,minute:t,second:t},le={year:t,month:r,day:t,hour:t,minute:t},ce={year:t,month:r,day:t,hour:t,minute:t,second:t},fe={year:t,month:r,day:t,weekday:r,hour:t,minute:t},de={year:t,month:a,day:t,hour:t,minute:t,timeZoneName:r},he={year:t,month:a,day:t,hour:t,minute:t,second:t,timeZoneName:r},me={year:t,month:a,day:t,weekday:a,hour:t,minute:t,timeZoneName:a},ye={year:t,month:a,day:t,weekday:a,hour:t,minute:t,second:t,timeZoneName:a},s=function(){function e(){}var t=e.prototype;return t.offsetName=function(e,t){throw new n},t.formatOffset=function(e,t){throw new n},t.offset=function(e){throw new n},t.equals=function(e){throw new n},i(e,[{key:"type",get:function(){throw new n}},{key:"name",get:function(){throw new n}},{key:"ianaName",get:function(){return this.name}},{key:"isUniversal",get:function(){throw new n}},{key:"isValid",get:function(){throw new n}}]),e}(),ve=null,ge=function(e){function t(){return e.apply(this,arguments)||this}o(t,e);var n=t.prototype;return n.offsetName=function(e,t){return Ot(e,t.format,t.locale)},n.formatOffset=function(e,t){return Dt(this.offset(e),t)},n.offset=function(e){return-new Date(e).getTimezoneOffset()},n.equals=function(e){return"system"===e.type},i(t,[{key:"type",get:function(){return"system"}},{key:"name",get:function(){return(new Intl.DateTimeFormat).resolvedOptions().timeZone}},{key:"isUniversal",get:function(){return!1}},{key:"isValid",get:function(){return!0}}],[{key:"instance",get:function(){return ve=null===ve?new t:ve}}]),t}(s),pe=new Map;var ke={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};var we=new Map,c=function(n){function r(e){var t=n.call(this)||this;return t.zoneName=e,t.valid=r.isValidZone(e),t}o(r,n),r.create=function(e){var t=we.get(e);return void 0===t&&we.set(e,t=new r(e)),t},r.resetCache=function(){we.clear(),pe.clear()},r.isValidSpecifier=function(e){return this.isValidZone(e)},r.isValidZone=function(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch(e){return!1}};var e=r.prototype;return e.offsetName=function(e,t){return Ot(e,t.format,t.locale,this.name)},e.formatOffset=function(e,t){return Dt(this.offset(e),t)},e.offset=function(e){var t,n,r,i,o,a,s,u;return!this.valid||(e=new Date(e),isNaN(e))?NaN:(i=this.name,void 0===(o=pe.get(i))&&(o=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:i,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"}),pe.set(i,o)),a=(i=(i=o).formatToParts?function(e,t){for(var n=e.formatToParts(t),r=[],i=0;i<n.length;i++){var o=n[i],a=o.type,o=o.value,s=ke[a];"era"===a?r[s]=o:N(s)||(r[s]=parseInt(o,10))}return r}(i,e):(o=e,i=(i=i).format(o).replace(/\u200E/g,""),i=(o=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(i))[1],a=o[2],[o[3],i,a,o[4],o[5],o[6],o[7]]))[0],o=i[1],t=i[2],n=i[3],s=i[4],r=i[5],i=i[6],s=24===s?0:s,u=(e=+e)%1e3,(kt({year:a="BC"===n?1-Math.abs(a):a,month:o,day:t,hour:s,minute:r,second:i,millisecond:0})-(e-=0<=u?u:1e3+u))/6e4)},e.equals=function(e){return"iana"===e.type&&e.name===this.name},i(r,[{key:"type",get:function(){return"iana"}},{key:"name",get:function(){return this.zoneName}},{key:"isUniversal",get:function(){return!1}},{key:"isValid",get:function(){return this.valid}}]),r}(s),be=["base"],Se=["padTo","floor"],Oe={};var Te=new Map;function Ne(e,t){void 0===t&&(t={});var n=JSON.stringify([e,t]),r=Te.get(n);return void 0===r&&(r=new Intl.DateTimeFormat(e,t),Te.set(n,r)),r}var Me=new Map;var De=new Map;var Ie=null;var Ve=new Map;function Ee(e){var t=Ve.get(e);return void 0===t&&(t=new Intl.DateTimeFormat(e).resolvedOptions(),Ve.set(e,t)),t}var xe=new Map;function Fe(e,t,n,r){e=e.listingMode();return"error"===e?null:("en"===e?n:r)(t)}var Ce=function(){function e(e,t,n){this.padTo=n.padTo||0,this.floor=n.floor||!1,n.padTo,n.floor;var r=_(n,Se);(!t||0<Object.keys(r).length)&&(t=l({useGrouping:!1},n),0<n.padTo&&(t.minimumIntegerDigits=n.padTo),this.inf=(r=e,void 0===(n=t)&&(n={}),e=JSON.stringify([r,n]),void 0===(t=Me.get(e))&&(t=new Intl.NumberFormat(r,n),Me.set(e,t)),t))}return e.prototype.format=function(e){var t;return this.inf?(t=this.floor?Math.floor(e):e,this.inf.format(t)):m(this.floor?Math.floor(e):vt(e,3),this.padTo)},e}(),Ze=function(){function e(e,t,n){this.opts=n;var n=this.originalZone=void 0,r=(this.opts.timeZone?this.dt=e:"fixed"===e.zone.type?(r=0<=(r=e.offset/60*-1)?"Etc/GMT+"+r:"Etc/GMT"+r,0!==e.offset&&c.create(r).valid?(n=r,this.dt=e):(n="UTC",this.dt=0===e.offset?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)):"system"===e.zone.type?this.dt=e:"iana"===e.zone.type?n=(this.dt=e).zone.name:(this.dt=e.setZone(n="UTC").plus({minutes:e.offset}),this.originalZone=e.zone),l({},this.opts));r.timeZone=r.timeZone||n,this.dtf=Ne(t,r)}var t=e.prototype;return t.format=function(){return this.originalZone?this.formatToParts().map(function(e){return e.value}).join(""):this.dtf.format(this.dt.toJSDate())},t.formatToParts=function(){var t=this,e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map(function(e){return"timeZoneName"===e.type?l({},e,{value:t.originalZone.offsetName(t.dt.ts,{locale:t.dt.locale,format:t.opts.timeZoneName})}):e}):e},t.resolvedOptions=function(){return this.dtf.resolvedOptions()},e}(),We=function(){function e(e,t,n){var r;this.opts=l({style:"long"},n),!t&&ft()&&(this.rtf=(t=e,(n=e=void 0===(e=n)?{}:e).base,n=_(n=e,be),n=JSON.stringify([t,n]),void 0===(r=De.get(n))&&(r=new Intl.RelativeTimeFormat(t,e),De.set(n,r)),r))}var t=e.prototype;return t.format=function(e,t){if(this.rtf)return this.rtf.format(e,t);var n=t,t=e,e=this.opts.numeric,r="long"!==this.opts.style,i=(void 0===e&&(e="always"),void 0===r&&(r=!1),{years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]}),o=-1===["hours","minutes","seconds"].indexOf(n);if("auto"===e&&o){var a="days"===n;switch(t){case 1:return a?"tomorrow":"next "+i[n][0];case-1:return a?"yesterday":"last "+i[n][0];case 0:return a?"today":"this "+i[n][0]}}var e=Object.is(t,-0)||t<0,t=1===(o=Math.abs(t)),s=i[n],r=r?!t&&s[2]||s[1]:t?i[n][0]:n;return e?o+" "+r+" ago":"in "+o+" "+r},t.formatToParts=function(e,t){return this.rtf?this.rtf.formatToParts(e,t):[]},e}(),Le={firstDay:1,minimalDays:4,weekend:[6,7]},b=function(){function o(e,t,n,r,i){var e=function(t){var n=t.indexOf("-x-");if(-1===(n=(t=-1!==n?t.substring(0,n):t).indexOf("-u-")))return[t];try{r=Ne(t).resolvedOptions(),i=t}catch(e){var t=t.substring(0,n),r=Ne(t).resolvedOptions(),i=t}return[i,(n=r).numberingSystem,n.calendar]}(e),o=e[0],a=e[1],e=e[2];this.locale=o,this.numberingSystem=t||a||null,this.outputCalendar=n||e||null,this.weekSettings=r,this.intl=(o=this.locale,t=this.numberingSystem,((a=this.outputCalendar)||t)&&(o.includes("-u-")||(o+="-u"),a&&(o+="-ca-"+a),t)&&(o+="-nu-"+t),o),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=i,this.fastNumbersCached=null}o.fromOpts=function(e){return o.create(e.locale,e.numberingSystem,e.outputCalendar,e.weekSettings,e.defaultToEN)},o.create=function(e,t,n,r,i){void 0===i&&(i=!1);e=e||O.defaultLocale;return new o(e||(i?"en-US":Ie=Ie||(new Intl.DateTimeFormat).resolvedOptions().locale),t||O.defaultNumberingSystem,n||O.defaultOutputCalendar,mt(r)||O.defaultWeekSettings,e)},o.resetCache=function(){Ie=null,Te.clear(),Me.clear(),De.clear(),Ve.clear(),xe.clear()},o.fromObject=function(e){var e=void 0===e?{}:e,t=e.locale,n=e.numberingSystem,r=e.outputCalendar,e=e.weekSettings;return o.create(t,n,r,e)};var e=o.prototype;return e.listingMode=function(){var e=this.isEnglish(),t=!(null!==this.numberingSystem&&"latn"!==this.numberingSystem||null!==this.outputCalendar&&"gregory"!==this.outputCalendar);return e&&t?"en":"intl"},e.clone=function(e){return e&&0!==Object.getOwnPropertyNames(e).length?o.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,mt(e.weekSettings)||this.weekSettings,e.defaultToEN||!1):this},e.redefaultToEN=function(e){return this.clone(l({},e=void 0===e?{}:e,{defaultToEN:!0}))},e.redefaultToSystem=function(e){return this.clone(l({},e=void 0===e?{}:e,{defaultToEN:!1}))},e.months=function(n,r){var i=this;return void 0===r&&(r=!1),Fe(this,n,Ft,function(){var t=r?{month:n,day:"numeric"}:{month:n},e=r?"format":"standalone";return i.monthsCache[e][n]||(i.monthsCache[e][n]=function(e){for(var t=[],n=1;n<=12;n++){var r=W.utc(2009,n,1);t.push(e(r))}return t}(function(e){return i.extract(e,t,"month")})),i.monthsCache[e][n]})},e.weekdays=function(n,r){var i=this;return void 0===r&&(r=!1),Fe(this,n,Lt,function(){var t=r?{weekday:n,year:"numeric",month:"long",day:"numeric"}:{weekday:n},e=r?"format":"standalone";return i.weekdaysCache[e][n]||(i.weekdaysCache[e][n]=function(e){for(var t=[],n=1;n<=7;n++){var r=W.utc(2016,11,13+n);t.push(e(r))}return t}(function(e){return i.extract(e,t,"weekday")})),i.weekdaysCache[e][n]})},e.meridiems=function(){var n=this;return Fe(this,void 0,function(){return jt},function(){var t;return n.meridiemCache||(t={hour:"numeric",hourCycle:"h12"},n.meridiemCache=[W.utc(2016,11,13,9),W.utc(2016,11,13,19)].map(function(e){return n.extract(e,t,"dayperiod")})),n.meridiemCache})},e.eras=function(e){var n=this;return Fe(this,e,_t,function(){var t={era:e};return n.eraCache[e]||(n.eraCache[e]=[W.utc(-40,1,1),W.utc(2017,1,1)].map(function(e){return n.extract(e,t,"era")})),n.eraCache[e]})},e.extract=function(e,t,n){e=this.dtFormatter(e,t).formatToParts().find(function(e){return e.type.toLowerCase()===n});return e?e.value:null},e.numberFormatter=function(e){return new Ce(this.intl,(e=void 0===e?{}:e).forceSimple||this.fastNumbers,e)},e.dtFormatter=function(e,t){return new Ze(e,this.intl,t=void 0===t?{}:t)},e.relFormatter=function(e){return void 0===e&&(e={}),new We(this.intl,this.isEnglish(),e)},e.listFormatter=function(e){return void 0===e&&(e={}),t=this.intl,void 0===(e=e)&&(e={}),n=JSON.stringify([t,e]),(r=Oe[n])||(r=new Intl.ListFormat(t,e),Oe[n]=r),r;var t,n,r},e.isEnglish=function(){return"en"===this.locale||"en-us"===this.locale.toLowerCase()||Ee(this.intl).locale.startsWith("en-us")},e.getWeekSettings=function(){return this.weekSettings||(dt()?(e=this.locale,(n=xe.get(e))||("minimalDays"in(n="getWeekInfo"in(t=new Intl.Locale(e))?t.getWeekInfo():t.weekInfo)||(n=l({},Le,n)),xe.set(e,n)),n):Le);var e,t,n},e.getStartOfWeek=function(){return this.getWeekSettings().firstDay},e.getMinDaysInFirstWeek=function(){return this.getWeekSettings().minimalDays},e.getWeekendDays=function(){return this.getWeekSettings().weekend},e.equals=function(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar},e.toString=function(){return"Locale("+this.locale+", "+this.numberingSystem+", "+this.outputCalendar+")"},i(o,[{key:"fastNumbers",get:function(){var e;return null==this.fastNumbersCached&&(this.fastNumbersCached=(!(e=this).numberingSystem||"latn"===e.numberingSystem)&&("latn"===e.numberingSystem||!e.locale||e.locale.startsWith("en")||"latn"===Ee(e.locale).numberingSystem)),this.fastNumbersCached}}]),o}(),je=null,f=function(n){function t(e){var t=n.call(this)||this;return t.fixed=e,t}o(t,n),t.instance=function(e){return 0===e?t.utcInstance:new t(e)},t.parseSpecifier=function(e){if(e){e=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(e)return new t(Tt(e[1],e[2]))}return null};var e=t.prototype;return e.offsetName=function(){return this.name},e.formatOffset=function(e,t){return Dt(this.fixed,t)},e.offset=function(){return this.fixed},e.equals=function(e){return"fixed"===e.type&&e.fixed===this.fixed},i(t,[{key:"type",get:function(){return"fixed"}},{key:"name",get:function(){return 0===this.fixed?"UTC":"UTC"+Dt(this.fixed,"narrow")}},{key:"ianaName",get:function(){return 0===this.fixed?"Etc/UTC":"Etc/GMT"+Dt(-this.fixed,"narrow")}},{key:"isUniversal",get:function(){return!0}},{key:"isValid",get:function(){return!0}}],[{key:"utcInstance",get:function(){return je=null===je?new t(0):je}}]),t}(s),ze=function(n){function e(e){var t=n.call(this)||this;return t.zoneName=e,t}o(e,n);var t=e.prototype;return t.offsetName=function(){return null},t.formatOffset=function(){return""},t.offset=function(){return NaN},t.equals=function(){return!1},i(e,[{key:"type",get:function(){return"invalid"}},{key:"name",get:function(){return this.zoneName}},{key:"isUniversal",get:function(){return!1}},{key:"isValid",get:function(){return!1}}]),e}(s);function S(e,t){var n;return N(e)||null===e?t:e instanceof s?e:"string"==typeof e?"default"===(n=e.toLowerCase())?t:"local"===n||"system"===n?ge.instance:"utc"===n||"gmt"===n?f.utcInstance:f.parseSpecifier(n)||c.create(e):v(e)?f.instance(e):"object"==typeof e&&"offset"in e&&"function"==typeof e.offset?e:new ze(e)}var Ae={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},qe={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},_e=Ae.hanidec.replace(/[\[|\]]/g,"").split("");var Ue=new Map;function y(e,t){void 0===t&&(t="");var e=e.numberingSystem||"latn",n=Ue.get(e),r=(void 0===n&&(n=new Map,Ue.set(e,n)),n.get(t));return void 0===r&&(r=new RegExp(""+Ae[e]+t),n.set(t,r)),r}var Re,Pe=function(){return Date.now()},Ye="system",He=null,Je=null,Ge=null,$e=60,Be=null,O=function(){function e(){}return e.resetCaches=function(){b.resetCache(),c.resetCache(),W.resetCache(),Ue.clear()},i(e,null,[{key:"now",get:function(){return Pe},set:function(e){Pe=e}},{key:"defaultZone",get:function(){return S(Ye,ge.instance)},set:function(e){Ye=e}},{key:"defaultLocale",get:function(){return He},set:function(e){He=e}},{key:"defaultNumberingSystem",get:function(){return Je},set:function(e){Je=e}},{key:"defaultOutputCalendar",get:function(){return Ge},set:function(e){Ge=e}},{key:"defaultWeekSettings",get:function(){return Be},set:function(e){Be=mt(e)}},{key:"twoDigitCutoffYear",get:function(){return $e},set:function(e){$e=e%100}},{key:"throwOnInvalid",get:function(){return Re},set:function(e){Re=e}}]),e}(),d=function(){function e(e,t){this.reason=e,this.explanation=t}return e.prototype.toMessage=function(){return this.explanation?this.reason+": "+this.explanation:this.reason},e}(),Qe=[0,31,59,90,120,151,181,212,243,273,304,334],Ke=[0,31,60,91,121,152,182,213,244,274,305,335];function T(e,t){return new d("unit out of range","you specified "+t+" (of type "+typeof t+") as a "+e+", which is invalid")}function Xe(e,t,n){t=new Date(Date.UTC(e,t-1,n)),e<100&&0<=e&&t.setUTCFullYear(t.getUTCFullYear()-1900),n=t.getUTCDay();return 0===n?7:n}function et(e,t,n){return n+(gt(e)?Ke:Qe)[t-1]}function tt(e,t){var e=gt(e)?Ke:Qe,n=e.findIndex(function(e){return e<t});return{month:n+1,day:t-e[n]}}function nt(e,t){return(e-t+7)%7+1}function rt(e,t,n){void 0===t&&(t=4),void 0===n&&(n=1);var r,i=e.year,o=e.month,a=e.day,s=et(i,o,a),o=nt(Xe(i,o,a),n),a=Math.floor((s-o+14-t)/7);return a<1?a=bt(r=i-1,t,n):a>bt(i,t,n)?(r=i+1,a=1):r=i,l({weekYear:r,weekNumber:a,weekday:o},It(e))}function it(e,t,n){void 0===n&&(n=1);var r,i=e.weekYear,o=e.weekNumber,a=e.weekday,n=nt(Xe(i,1,t=void 0===t?4:t),n),s=D(i),o=7*o+a-n-7+t,a=(o<1?o+=D(r=i-1):s<o?(r=i+1,o-=D(i)):r=i,tt(r,o));return l({year:r,month:a.month,day:a.day},It(e))}function ot(e){var t=e.year;return l({year:t,ordinal:et(t,e.month,e.day)},It(e))}function at(e){var t=e.year,n=tt(t,e.ordinal);return l({year:t,month:n.month,day:n.day},It(e))}function st(e,t){if(N(e.localWeekday)&&N(e.localWeekNumber)&&N(e.localWeekYear))return{minDaysInFirstWeek:4,startOfWeek:1};if(N(e.weekday)&&N(e.weekNumber)&&N(e.weekYear))return N(e.localWeekday)||(e.weekday=e.localWeekday),N(e.localWeekNumber)||(e.weekNumber=e.localWeekNumber),N(e.localWeekYear)||(e.weekYear=e.localWeekYear),delete e.localWeekday,delete e.localWeekNumber,delete e.localWeekYear,{minDaysInFirstWeek:t.getMinDaysInFirstWeek(),startOfWeek:t.getStartOfWeek()};throw new w("Cannot mix locale-based week fields with ISO-based week fields")}function ut(e){var t=ct(e.year),n=M(e.month,1,12),r=M(e.day,1,pt(e.year,e.month));return t?n?!r&&T("day",e.day):T("month",e.month):T("year",e.year)}function lt(e){var t=e.hour,n=e.minute,r=e.second,e=e.millisecond,i=M(t,0,23)||24===t&&0===n&&0===r&&0===e,o=M(n,0,59),a=M(r,0,59),s=M(e,0,999);return i?o?a?!s&&T("millisecond",e):T("second",r):T("minute",n):T("hour",t)}function N(e){return void 0===e}function v(e){return"number"==typeof e}function ct(e){return"number"==typeof e&&e%1==0}function ft(){try{return"undefined"!=typeof Intl&&!!Intl.RelativeTimeFormat}catch(e){return!1}}function dt(){try{return"undefined"!=typeof Intl&&!!Intl.Locale&&("weekInfo"in Intl.Locale.prototype||"getWeekInfo"in Intl.Locale.prototype)}catch(e){return!1}}function ht(e,n,r){if(0!==e.length)return e.reduce(function(e,t){t=[n(t),t];return e&&r(e[0],t[0])===e[0]?e:t},null)[1]}function h(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function mt(e){if(null==e)return null;if("object"!=typeof e)throw new u("Week settings must be an object");if(M(e.firstDay,1,7)&&M(e.minimalDays,1,7)&&Array.isArray(e.weekend)&&!e.weekend.some(function(e){return!M(e,1,7)}))return{firstDay:e.firstDay,minimalDays:e.minimalDays,weekend:Array.from(e.weekend)};throw new u("Invalid week settings")}function M(e,t,n){return ct(e)&&t<=e&&e<=n}function m(e,t){void 0===t&&(t=2);e=e<0?"-"+(""+-e).padStart(t,"0"):(""+e).padStart(t,"0");return e}function g(e){if(!N(e)&&null!==e&&""!==e)return parseInt(e,10)}function p(e){if(!N(e)&&null!==e&&""!==e)return parseFloat(e)}function yt(e){if(!N(e)&&null!==e&&""!==e)return e=1e3*parseFloat("0."+e),Math.floor(e)}function vt(e,t,n){void 0===n&&(n=!1);t=Math.pow(10,t);return(n?Math.trunc:Math.round)(e*t)/t}function gt(e){return e%4==0&&(e%100!=0||e%400==0)}function D(e){return gt(e)?366:365}function pt(e,t){var n,r=(r=t-1)-(n=12)*Math.floor(r/n)+1;return 2==r?gt(e+(t-r)/12)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][r-1]}function kt(e){var t=Date.UTC(e.year,e.month-1,e.day,e.hour,e.minute,e.second,e.millisecond);return e.year<100&&0<=e.year&&(t=new Date(t)).setUTCFullYear(e.year,e.month-1,e.day),+t}function wt(e,t,n){return-nt(Xe(e,1,t),n)+t-1}function bt(e,t,n){var r=wt(e,t=void 0===t?4:t,n=void 0===n?1:n),t=wt(e+1,t,n);return(D(e)-r+t)/7}function St(e){return 99<e?e:e>O.twoDigitCutoffYear?1900+e:2e3+e}function Ot(e,t,n,r){void 0===r&&(r=null);var e=new Date(e),i={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"},r=(r&&(i.timeZone=r),l({timeZoneName:t},i)),t=new Intl.DateTimeFormat(n,r).formatToParts(e).find(function(e){return"timezonename"===e.type.toLowerCase()});return t?t.value:null}function Tt(e,t){e=parseInt(e,10),Number.isNaN(e)&&(e=0),t=parseInt(t,10)||0;return 60*e+(e<0||Object.is(e,-0)?-t:t)}function Nt(e){var t=Number(e);if("boolean"==typeof e||""===e||Number.isNaN(t))throw new u("Invalid unit value "+e);return t}function Mt(e,t){var n,r,i={};for(n in e)h(e,n)&&null!=(r=e[n])&&(i[t(n)]=Nt(r));return i}function Dt(e,t){var n=Math.trunc(Math.abs(e/60)),r=Math.trunc(Math.abs(e%60)),i=0<=e?"+":"-";switch(t){case"short":return i+m(n,2)+":"+m(r,2);case"narrow":return i+n+(0<r?":"+r:"");case"techie":return i+m(n,2)+m(r,2);default:throw new RangeError("Value format "+t+" is out of range for property format")}}function It(e){return n=e,["hour","minute","second","millisecond"].reduce(function(e,t){return e[t]=n[t],e},{});var n}var Vt=["January","February","March","April","May","June","July","August","September","October","November","December"],Et=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],xt=["J","F","M","A","M","J","J","A","S","O","N","D"];function Ft(e){switch(e){case"narrow":return[].concat(xt);case"short":return[].concat(Et);case"long":return[].concat(Vt);case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}var Ct=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Zt=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],Wt=["M","T","W","T","F","S","S"];function Lt(e){switch(e){case"narrow":return[].concat(Wt);case"short":return[].concat(Zt);case"long":return[].concat(Ct);case"numeric":return["1","2","3","4","5","6","7"];default:return null}}var jt=["AM","PM"],zt=["Before Christ","Anno Domini"],At=["BC","AD"],qt=["B","A"];function _t(e){switch(e){case"narrow":return[].concat(qt);case"short":return[].concat(At);case"long":return[].concat(zt);default:return null}}function Ut(e,t){for(var n="",r=R(e);!(i=r()).done;){var i=i.value;i.literal?n+=i.val:n+=t(i.val)}return n}var Rt={D:G,DD:$,DDD:Q,DDDD:K,t:X,tt:ee,ttt:te,tttt:ne,T:re,TT:ie,TTT:oe,TTTT:ae,f:se,ff:le,fff:de,ffff:me,F:ue,FF:ce,FFF:he,FFFF:ye},I=function(){function d(e,t){this.opts=t,this.loc=e,this.systemLoc=null}d.create=function(e,t){return new d(e,t=void 0===t?{}:t)},d.parseFormat=function(e){for(var t=null,n="",r=!1,i=[],o=0;o<e.length;o++){var a=e.charAt(o);"'"===a?(0<n.length&&i.push({literal:r||/^\s+$/.test(n),val:n}),t=null,n="",r=!r):r||a===t?n+=a:(0<n.length&&i.push({literal:/^\s+$/.test(n),val:n}),t=n=a)}return 0<n.length&&i.push({literal:r||/^\s+$/.test(n),val:n}),i},d.macroTokenToFormatOpts=function(e){return Rt[e]};var e=d.prototype;return e.formatWithSystemDefault=function(e,t){return null===this.systemLoc&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,l({},this.opts,t)).format()},e.dtFormatter=function(e,t){return this.loc.dtFormatter(e,l({},this.opts,t=void 0===t?{}:t))},e.formatDateTime=function(e,t){return this.dtFormatter(e,t).format()},e.formatDateTimeParts=function(e,t){return this.dtFormatter(e,t).formatToParts()},e.formatInterval=function(e,t){return this.dtFormatter(e.start,t).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())},e.resolvedOptions=function(e,t){return this.dtFormatter(e,t).resolvedOptions()},e.num=function(e,t){var n;return void 0===t&&(t=0),this.opts.forceSimple?m(e,t):(n=l({},this.opts),0<t&&(n.padTo=t),this.loc.numberFormatter(n).format(e))},e.formatDateTimeFromString=function(r,e){var n=this,i="en"===this.loc.listingMode(),t=this.loc.outputCalendar&&"gregory"!==this.loc.outputCalendar,o=function(e,t){return n.loc.extract(r,e,t)},a=function(e){return r.isOffsetFixed&&0===r.offset&&e.allowZ?"Z":r.isValid?r.zone.formatOffset(r.ts,e.format):""},s=function(){return i?jt[r.hour<12?0:1]:o({hour:"numeric",hourCycle:"h12"},"dayperiod")},u=function(e,t){return i?(n=r,Ft(e)[n.month-1]):o(t?{month:e}:{month:e,day:"numeric"},"month");var n},l=function(e,t){return i?(n=r,Lt(e)[n.weekday-1]):o(t?{weekday:e}:{weekday:e,month:"long",day:"numeric"},"weekday");var n},c=function(e){var t=d.macroTokenToFormatOpts(e);return t?n.formatWithSystemDefault(r,t):e},f=function(e){return i?(t=r,_t(e)[t.year<0?0:1]):o({era:e},"era");var t};return Ut(d.parseFormat(e),function(e){switch(e){case"S":return n.num(r.millisecond);case"u":case"SSS":return n.num(r.millisecond,3);case"s":return n.num(r.second);case"ss":return n.num(r.second,2);case"uu":return n.num(Math.floor(r.millisecond/10),2);case"uuu":return n.num(Math.floor(r.millisecond/100));case"m":return n.num(r.minute);case"mm":return n.num(r.minute,2);case"h":return n.num(r.hour%12==0?12:r.hour%12);case"hh":return n.num(r.hour%12==0?12:r.hour%12,2);case"H":return n.num(r.hour);case"HH":return n.num(r.hour,2);case"Z":return a({format:"narrow",allowZ:n.opts.allowZ});case"ZZ":return a({format:"short",allowZ:n.opts.allowZ});case"ZZZ":return a({format:"techie",allowZ:n.opts.allowZ});case"ZZZZ":return r.zone.offsetName(r.ts,{format:"short",locale:n.loc.locale});case"ZZZZZ":return r.zone.offsetName(r.ts,{format:"long",locale:n.loc.locale});case"z":return r.zoneName;case"a":return s();case"d":return t?o({day:"numeric"},"day"):n.num(r.day);case"dd":return t?o({day:"2-digit"},"day"):n.num(r.day,2);case"c":return n.num(r.weekday);case"ccc":return l("short",!0);case"cccc":return l("long",!0);case"ccccc":return l("narrow",!0);case"E":return n.num(r.weekday);case"EEE":return l("short",!1);case"EEEE":return l("long",!1);case"EEEEE":return l("narrow",!1);case"L":return t?o({month:"numeric",day:"numeric"},"month"):n.num(r.month);case"LL":return t?o({month:"2-digit",day:"numeric"},"month"):n.num(r.month,2);case"LLL":return u("short",!0);case"LLLL":return u("long",!0);case"LLLLL":return u("narrow",!0);case"M":return t?o({month:"numeric"},"month"):n.num(r.month);case"MM":return t?o({month:"2-digit"},"month"):n.num(r.month,2);case"MMM":return u("short",!1);case"MMMM":return u("long",!1);case"MMMMM":return u("narrow",!1);case"y":return t?o({year:"numeric"},"year"):n.num(r.year);case"yy":return t?o({year:"2-digit"},"year"):n.num(r.year.toString().slice(-2),2);case"yyyy":return t?o({year:"numeric"},"year"):n.num(r.year,4);case"yyyyyy":return t?o({year:"numeric"},"year"):n.num(r.year,6);case"G":return f("short");case"GG":return f("long");case"GGGGG":return f("narrow");case"kk":return n.num(r.weekYear.toString().slice(-2),2);case"kkkk":return n.num(r.weekYear,4);case"W":return n.num(r.weekNumber);case"WW":return n.num(r.weekNumber,2);case"n":return n.num(r.localWeekNumber);case"nn":return n.num(r.localWeekNumber,2);case"ii":return n.num(r.localWeekYear.toString().slice(-2),2);case"iiii":return n.num(r.localWeekYear,4);case"o":return n.num(r.ordinal);case"ooo":return n.num(r.ordinal,3);case"q":return n.num(r.quarter);case"qq":return n.num(r.quarter,2);case"X":return n.num(Math.floor(r.ts/1e3));case"x":return n.num(r.ts);default:return c(e)}})},e.formatDurationFromString=function(e,t){var n,r=this,i=function(e){switch(e[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},t=d.parseFormat(t),o=t.reduce(function(e,t){var n=t.literal,t=t.val;return n?e:e.concat(t)},[]),e=e.shiftTo.apply(e,o.map(i).filter(function(e){return e}));return Ut(t,(n=e,function(e){var t=i(e);return t?r.num(n.get(t),e.length):e}))},d}(),r=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function Pt(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.reduce(function(e,t){return e+t.source},"");return RegExp("^"+r+"$")}function Yt(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(o){return t.reduce(function(e,t){var n=e[0],r=e[1],e=e[2],t=t(o,e),e=t[0],i=t[1],t=t[2];return[l({},n,e),i||r,t]},[{},null,1]).slice(0,2)}}function Ht(e){if(null!=e){for(var t=arguments.length,n=new Array(1<t?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(var i=0,o=n;i<o.length;i++){var a=o[i],s=a[0],a=a[1],s=s.exec(e);if(s)return a(s)}}return[null,null]}function Jt(){for(var e=arguments.length,i=new Array(e),t=0;t<e;t++)i[t]=arguments[t];return function(e,t){for(var n={},r=0;r<i.length;r++)n[i[r]]=g(e[t+r]);return[n,null,t+r]}}var t=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,a=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,Gt=RegExp(a.source+("(?:"+t.source+"?(?:\\[("+r.source+")\\])?)?")),k=RegExp("(?:T"+Gt.source+")?"),$t=Jt("weekYear","weekNumber","weekDay"),Bt=Jt("year","ordinal"),t=RegExp(a.source+" ?(?:"+t.source+"|("+r.source+"))?"),r=RegExp("(?: "+t.source+")?");function Qt(e,t,n){e=e[t];return N(e)?n:g(e)}function Kt(e,t){return[{hours:Qt(e,t,0),minutes:Qt(e,t+1,0),seconds:Qt(e,t+2,0),milliseconds:yt(e[t+3])},null,t+4]}function Xt(e,t){var n=!e[t]&&!e[t+1],e=Tt(e[t+1],e[t+2]);return[{},n?null:f.instance(e),t+3]}function en(e,t){return[{},e[t]?c.create(e[t]):null,t+1]}var tn=RegExp("^T?"+a.source+"$"),nn=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function rn(e){function t(e,t){return void 0===t&&(t=!1),void 0!==e&&(t||e&&c)?-e:e}var n=e[0],r=e[1],i=e[2],o=e[3],a=e[4],s=e[5],u=e[6],l=e[7],e=e[8],c="-"===n[0],n=l&&"-"===l[0];return[{years:t(p(r)),months:t(p(i)),weeks:t(p(o)),days:t(p(a)),hours:t(p(s)),minutes:t(p(u)),seconds:t(p(l),"-0"===l),milliseconds:t(yt(e),n)}]}var on={GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function an(e,t,n,r,i,o,a){t={year:2===t.length?St(g(t)):g(t),month:Et.indexOf(n)+1,day:g(r),hour:g(i),minute:g(o)};return a&&(t.second=g(a)),e&&(t.weekday=3<e.length?Ct.indexOf(e)+1:Zt.indexOf(e)+1),t}var sn=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function un(e){var t=e[1],n=e[2],r=e[3],i=e[4],o=e[5],a=e[6],s=e[7],u=e[8],l=e[9],c=e[10],e=e[11],t=an(t,i,r,n,o,a,s),i=u?on[u]:l?0:Tt(c,e);return[t,new f(i)]}var ln=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,cn=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,fn=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function dn(e){var t=e[1],n=e[2],r=e[3];return[an(t,e[4],r,n,e[5],e[6],e[7]),f.utcInstance]}function hn(e){var t=e[1],n=e[2],r=e[3],i=e[4],o=e[5],a=e[6];return[an(t,e[7],n,r,i,o,a),f.utcInstance]}var mn=Pt(/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,k),yn=Pt(/(\d{4})-?W(\d\d)(?:-?(\d))?/,k),vn=Pt(/(\d{4})-?(\d{3})/,k),gn=Pt(Gt),pn=Yt(function(e,t){return[{year:Qt(e,t),month:Qt(e,t+1,1),day:Qt(e,t+2,1)},null,t+3]},Kt,Xt,en),kn=Yt($t,Kt,Xt,en),wn=Yt(Bt,Kt,Xt,en),bn=Yt(Kt,Xt,en);var Sn=Yt(Kt);var On=Pt(/(\d{4})-(\d\d)-(\d\d)/,r),Tn=Pt(t),Nn=Yt(Kt,Xt,en);var Mn="Invalid Duration",a={weeks:{days:7,hours:168,minutes:10080,seconds:604800,milliseconds:6048e5},days:{hours:24,minutes:1440,seconds:86400,milliseconds:864e5},hours:{minutes:60,seconds:3600,milliseconds:36e5},minutes:{seconds:60,milliseconds:6e4},seconds:{milliseconds:1e3}},Dn=l({years:{quarters:4,months:12,weeks:52,days:365,hours:8760,minutes:525600,seconds:31536e3,milliseconds:31536e6},quarters:{months:3,weeks:13,days:91,hours:2184,minutes:131040,seconds:7862400,milliseconds:78624e5},months:{weeks:4,days:30,hours:720,minutes:43200,seconds:2592e3,milliseconds:2592e6}},a),k=365.2425,Gt=30.436875,In=l({years:{quarters:4,months:12,weeks:k/7,days:k,hours:24*k,minutes:525949.2,seconds:525949.2*60,milliseconds:525949.2*60*1e3},quarters:{months:3,weeks:k/28,days:k/4,hours:24*k/4,minutes:131487.3,seconds:525949.2*60/4,milliseconds:7889237999.999999},months:{weeks:Gt/7,days:Gt,hours:24*Gt,minutes:43829.1,seconds:2629746,milliseconds:2629746e3}},a),V=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],Vn=V.slice(0).reverse();function E(e,t,n){n={values:(n=void 0===n?!1:n)?t.values:l({},e.values,t.values||{}),loc:e.loc.clone(t.loc),conversionAccuracy:t.conversionAccuracy||e.conversionAccuracy,matrix:t.matrix||e.matrix};return new x(n)}function En(e,t){for(var n,r=null!=(n=t.milliseconds)?n:0,i=R(Vn.slice(1));!(o=i()).done;){var o=o.value;t[o]&&(r+=t[o]*e[o].milliseconds)}return r}function xn(i,o){var a=En(i,o)<0?-1:1;V.reduceRight(function(e,t){var n,r;return N(o[t])?e:(e&&(r=o[e]*a,n=i[t][e],r=Math.floor(r/n),o[t]+=r*a,o[e]-=r*n*a),t)},null),V.reduce(function(e,t){var n;return N(o[t])?e:(e&&(n=o[e]%1,o[e]-=n,o[t]+=n*i[e][t]),t)},null)}var x=function(e){function m(e){var t="longterm"===e.conversionAccuracy||!1,n=t?In:Dn;e.matrix&&(n=e.matrix),this.values=e.values,this.loc=e.loc||b.create(),this.conversionAccuracy=t?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=n,this.isLuxonDuration=!0}m.fromMillis=function(e,t){return m.fromObject({milliseconds:e},t)},m.fromObject=function(e,t){if(void 0===t&&(t={}),null==e||"object"!=typeof e)throw new u("Duration.fromObject: argument expected to be an object, got "+(null===e?"null":typeof e));return new m({values:Mt(e,m.normalizeUnit),loc:b.fromObject(t),conversionAccuracy:t.conversionAccuracy,matrix:t.matrix})},m.fromDurationLike=function(e){if(v(e))return m.fromMillis(e);if(m.isDuration(e))return e;if("object"==typeof e)return m.fromObject(e);throw new u("Unknown duration argument "+e+" of type "+typeof e)},m.fromISO=function(e,t){var n=Ht(e,[nn,rn])[0];return n?m.fromObject(n,t):m.invalid("unparsable",'the input "'+e+"\" can't be parsed as ISO 8601")},m.fromISOTime=function(e,t){var n=Ht(e,[tn,Sn])[0];return n?m.fromObject(n,t):m.invalid("unparsable",'the input "'+e+"\" can't be parsed as ISO 8601")},m.invalid=function(e,t){if(void 0===t&&(t=null),!e)throw new u("need to specify a reason the Duration is invalid");e=e instanceof d?e:new d(e,t);if(O.throwOnInvalid)throw new H(e);return new m({invalid:e})},m.normalizeUnit=function(e){var t={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e&&e.toLowerCase()];if(t)return t;throw new J(e)},m.isDuration=function(e){return e&&e.isLuxonDuration||!1};var t=m.prototype;return t.toFormat=function(e,t){t=l({},t=void 0===t?{}:t,{floor:!1!==t.round&&!1!==t.floor});return this.isValid?I.create(this.loc,t).formatDurationFromString(this,e):Mn},t.toHuman=function(n){var e,r=this;return void 0===n&&(n={}),this.isValid?(e=V.map(function(e){var t=r.values[e];return N(t)?null:r.loc.numberFormatter(l({style:"unit",unitDisplay:"long"},n,{unit:e.slice(0,-1)})).format(t)}).filter(function(e){return e}),this.loc.listFormatter(l({type:"conjunction",style:n.listStyle||"narrow"},n)).format(e)):Mn},t.toObject=function(){return this.isValid?l({},this.values):{}},t.toISO=function(){var e;return this.isValid?(e="P",0!==this.years&&(e+=this.years+"Y"),0===this.months&&0===this.quarters||(e+=this.months+3*this.quarters+"M"),0!==this.weeks&&(e+=this.weeks+"W"),0!==this.days&&(e+=this.days+"D"),0===this.hours&&0===this.minutes&&0===this.seconds&&0===this.milliseconds||(e+="T"),0!==this.hours&&(e+=this.hours+"H"),0!==this.minutes&&(e+=this.minutes+"M"),0===this.seconds&&0===this.milliseconds||(e+=vt(this.seconds+this.milliseconds/1e3,3)+"S"),"P"===e&&(e+="T0S"),e):null},t.toISOTime=function(e){var t;return void 0===e&&(e={}),!this.isValid||(t=this.toMillis())<0||864e5<=t?null:(e=l({suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended"},e,{includeOffset:!1}),W.fromMillis(t,{zone:"UTC"}).toISOTime(e))},t.toJSON=function(){return this.toISO()},t.toString=function(){return this.toISO()},t[e]=function(){return this.isValid?"Duration { values: "+JSON.stringify(this.values)+" }":"Duration { Invalid, reason: "+this.invalidReason+" }"},t.toMillis=function(){return this.isValid?En(this.matrix,this.values):NaN},t.valueOf=function(){return this.toMillis()},t.plus=function(e){if(!this.isValid)return this;for(var t=m.fromDurationLike(e),n={},r=0,i=V;r<i.length;r++){var o=i[r];(h(t.values,o)||h(this.values,o))&&(n[o]=t.get(o)+this.get(o))}return E(this,{values:n},!0)},t.minus=function(e){return this.isValid?(e=m.fromDurationLike(e),this.plus(e.negate())):this},t.mapUnits=function(e){if(!this.isValid)return this;for(var t={},n=0,r=Object.keys(this.values);n<r.length;n++){var i=r[n];t[i]=Nt(e(this.values[i],i))}return E(this,{values:t},!0)},t.get=function(e){return this[m.normalizeUnit(e)]},t.set=function(e){return this.isValid?E(this,{values:l({},this.values,Mt(e,m.normalizeUnit))}):this},t.reconfigure=function(e){var e=void 0===e?{}:e,t=e.locale,n=e.numberingSystem,r=e.conversionAccuracy,e=e.matrix,t=this.loc.clone({locale:t,numberingSystem:n});return E(this,{loc:t,matrix:e,conversionAccuracy:r})},t.as=function(e){return this.isValid?this.shiftTo(e).get(e):NaN},t.normalize=function(){var e;return this.isValid?(e=this.toObject(),xn(this.matrix,e),E(this,{values:e},!0)):this},t.rescale=function(){var e;return this.isValid?(e=function(e){for(var t={},n=0,r=Object.entries(e);n<r.length;n++){var i=r[n],o=i[0],i=i[1];0!==i&&(t[o]=i)}return t}(this.normalize().shiftToAll().toObject()),E(this,{values:e},!0)):this},t.shiftTo=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(!this.isValid)return this;if(0===t.length)return this;for(var r,t=t.map(function(e){return m.normalizeUnit(e)}),i={},o={},a=this.toObject(),s=0,u=V;s<u.length;s++){var l=u[s];if(0<=t.indexOf(l)){var c,f=l,d=0;for(c in o)d+=this.matrix[c][l]*o[c],o[c]=0;v(a[l])&&(d+=a[l]);var h=Math.trunc(d);o[l]=(1e3*d-1e3*(i[l]=h))/1e3}else v(a[l])&&(o[l]=a[l])}for(r in o)0!==o[r]&&(i[f]+=r===f?o[r]:o[r]/this.matrix[f][r]);return xn(this.matrix,i),E(this,{values:i},!0)},t.shiftToAll=function(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this},t.negate=function(){if(!this.isValid)return this;for(var e={},t=0,n=Object.keys(this.values);t<n.length;t++){var r=n[t];e[r]=0===this.values[r]?0:-this.values[r]}return E(this,{values:e},!0)},t.equals=function(e){if(!this.isValid||!e.isValid)return!1;if(!this.loc.equals(e.loc))return!1;for(var t,n=0,r=V;n<r.length;n++){var i=r[n];if(t=this.values[i],i=e.values[i],!(void 0===t||0===t?void 0===i||0===i:t===i))return!1}return!0},i(m,[{key:"locale",get:function(){return this.isValid?this.loc.locale:null}},{key:"numberingSystem",get:function(){return this.isValid?this.loc.numberingSystem:null}},{key:"years",get:function(){return this.isValid?this.values.years||0:NaN}},{key:"quarters",get:function(){return this.isValid?this.values.quarters||0:NaN}},{key:"months",get:function(){return this.isValid?this.values.months||0:NaN}},{key:"weeks",get:function(){return this.isValid?this.values.weeks||0:NaN}},{key:"days",get:function(){return this.isValid?this.values.days||0:NaN}},{key:"hours",get:function(){return this.isValid?this.values.hours||0:NaN}},{key:"minutes",get:function(){return this.isValid?this.values.minutes||0:NaN}},{key:"seconds",get:function(){return this.isValid?this.values.seconds||0:NaN}},{key:"milliseconds",get:function(){return this.isValid?this.values.milliseconds||0:NaN}},{key:"isValid",get:function(){return null===this.invalid}},{key:"invalidReason",get:function(){return this.invalid?this.invalid.reason:null}},{key:"invalidExplanation",get:function(){return this.invalid?this.invalid.explanation:null}}]),m}(Symbol.for("nodejs.util.inspect.custom")),Fn="Invalid Interval";var Cn=function(e){function l(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}l.invalid=function(e,t){if(void 0===t&&(t=null),!e)throw new u("need to specify a reason the Interval is invalid");e=e instanceof d?e:new d(e,t);if(O.throwOnInvalid)throw new Y(e);return new l({invalid:e})},l.fromDateTimes=function(e,t){var n,e=br(e),t=br(t),r=(n=t,(r=e)&&r.isValid?n&&n.isValid?n<r?Cn.invalid("end before start","The end of an interval must be after its start, but you had start="+r.toISO()+" and end="+n.toISO()):null:Cn.invalid("missing or invalid end"):Cn.invalid("missing or invalid start"));return null==r?new l({start:e,end:t}):r},l.after=function(e,t){t=x.fromDurationLike(t),e=br(e);return l.fromDateTimes(e,e.plus(t))},l.before=function(e,t){t=x.fromDurationLike(t),e=br(e);return l.fromDateTimes(e.minus(t),e)},l.fromISO=function(e,t){var n,r,i,o=(e||"").split("/",2),a=o[0],s=o[1];if(a&&s){try{u=(n=W.fromISO(a,t)).isValid}catch(s){u=!1}try{i=(r=W.fromISO(s,t)).isValid}catch(s){i=!1}if(u&&i)return l.fromDateTimes(n,r);if(u){o=x.fromISO(s,t);if(o.isValid)return l.after(n,o)}else if(i){var u=x.fromISO(a,t);if(u.isValid)return l.before(r,u)}}return l.invalid("unparsable",'the input "'+e+"\" can't be parsed as ISO 8601")},l.isInterval=function(e){return e&&e.isLuxonInterval||!1};var t=l.prototype;return t.length=function(e){return void 0===e&&(e="milliseconds"),this.isValid?this.toDuration.apply(this,[e]).get(e):NaN},t.count=function(e,t){var n,r;return this.isValid?(n=this.start.startOf(e=void 0===e?"milliseconds":e,t),r=(r=null!=t&&t.useLocaleWeeks?this.end.reconfigure({locale:n.locale}):this.end).startOf(e,t),Math.floor(r.diff(n,e).get(e))+(r.valueOf()!==this.end.valueOf())):NaN},t.hasSame=function(e){return!!this.isValid&&(this.isEmpty()||this.e.minus(1).hasSame(this.s,e))},t.isEmpty=function(){return this.s.valueOf()===this.e.valueOf()},t.isAfter=function(e){return!!this.isValid&&this.s>e},t.isBefore=function(e){return!!this.isValid&&this.e<=e},t.contains=function(e){return!!this.isValid&&this.s<=e&&this.e>e},t.set=function(e){var e=void 0===e?{}:e,t=e.start,e=e.end;return this.isValid?l.fromDateTimes(t||this.s,e||this.e):this},t.splitAt=function(){var t=this;if(!this.isValid)return[];for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];for(var i=n.map(br).filter(function(e){return t.contains(e)}).sort(function(e,t){return e.toMillis()-t.toMillis()}),o=[],a=this.s,s=0;a<this.e;){var u=i[s]||this.e,u=+u>+this.e?this.e:u;o.push(l.fromDateTimes(a,u)),a=u,s+=1}return o},t.splitBy=function(e){var t=x.fromDurationLike(e);if(!this.isValid||!t.isValid||0===t.as("milliseconds"))return[];for(var n=this.s,r=1,i=[];n<this.e;){var o=this.start.plus(t.mapUnits(function(e){return e*r})),o=+o>+this.e?this.e:o;i.push(l.fromDateTimes(n,o)),n=o,r+=1}return i},t.divideEqually=function(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]},t.overlaps=function(e){return this.e>e.s&&this.s<e.e},t.abutsStart=function(e){return!!this.isValid&&+this.e==+e.s},t.abutsEnd=function(e){return!!this.isValid&&+e.e==+this.s},t.engulfs=function(e){return!!this.isValid&&this.s<=e.s&&this.e>=e.e},t.equals=function(e){return!(!this.isValid||!e.isValid)&&this.s.equals(e.s)&&this.e.equals(e.e)},t.intersection=function(e){var t;return this.isValid?(t=(this.s>e.s?this:e).s,(e=(this.e<e.e?this:e).e)<=t?null:l.fromDateTimes(t,e)):this},t.union=function(e){var t;return this.isValid?(t=(this.s<e.s?this:e).s,e=(this.e>e.e?this:e).e,l.fromDateTimes(t,e)):this},l.merge=function(e){var e=e.sort(function(e,t){return e.s-t.s}).reduce(function(e,t){var n=e[0],e=e[1];return e?e.overlaps(t)||e.abutsStart(t)?[n,e.union(t)]:[n.concat([e]),t]:[n,t]},[[],null]),t=e[0],e=e[1];return e&&t.push(e),t},l.xor=function(e){for(var t,n=null,r=0,i=[],e=e.map(function(e){return[{time:e.s,type:"s"},{time:e.e,type:"e"}]}),o=R((t=Array.prototype).concat.apply(t,e).sort(function(e,t){return e.time-t.time}));!(a=o()).done;)var a=a.value,n=1===(r+="s"===a.type?1:-1)?a.time:(n&&+n!=+a.time&&i.push(l.fromDateTimes(n,a.time)),null);return l.merge(i)},t.difference=function(){for(var t=this,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return l.xor([this].concat(n)).map(function(e){return t.intersection(e)}).filter(function(e){return e&&!e.isEmpty()})},t.toString=function(){return this.isValid?"["+this.s.toISO()+" – "+this.e.toISO()+")":Fn},t[e]=function(){return this.isValid?"Interval { start: "+this.s.toISO()+", end: "+this.e.toISO()+" }":"Interval { Invalid, reason: "+this.invalidReason+" }"},t.toLocaleString=function(e,t){return void 0===e&&(e=G),void 0===t&&(t={}),this.isValid?I.create(this.s.loc.clone(t),e).formatInterval(this):Fn},t.toISO=function(e){return this.isValid?this.s.toISO(e)+"/"+this.e.toISO(e):Fn},t.toISODate=function(){return this.isValid?this.s.toISODate()+"/"+this.e.toISODate():Fn},t.toISOTime=function(e){return this.isValid?this.s.toISOTime(e)+"/"+this.e.toISOTime(e):Fn},t.toFormat=function(e,t){t=(void 0===t?{}:t).separator,t=void 0===t?" – ":t;return this.isValid?""+this.s.toFormat(e)+t+this.e.toFormat(e):Fn},t.toDuration=function(e,t){return this.isValid?this.e.diff(this.s,e,t):x.invalid(this.invalidReason)},t.mapEndpoints=function(e){return l.fromDateTimes(e(this.s),e(this.e))},i(l,[{key:"start",get:function(){return this.isValid?this.s:null}},{key:"end",get:function(){return this.isValid?this.e:null}},{key:"lastDateTime",get:function(){return this.isValid&&this.e?this.e.minus(1):null}},{key:"isValid",get:function(){return null===this.invalidReason}},{key:"invalidReason",get:function(){return this.invalid?this.invalid.reason:null}},{key:"invalidExplanation",get:function(){return this.invalid?this.invalid.explanation:null}}]),l}(Symbol.for("nodejs.util.inspect.custom")),Zn=function(){function e(){}return e.hasDST=function(e){void 0===e&&(e=O.defaultZone);var t=W.now().setZone(e).set({month:12});return!e.isUniversal&&t.offset!==t.set({month:6}).offset},e.isValidIANAZone=function(e){return c.isValidZone(e)},e.normalizeZone=function(e){return S(e,O.defaultZone)},e.getStartOfWeek=function(e){var e=void 0===e?{}:e,t=e.locale,e=e.locObj;return((void 0===e?null:e)||b.create(void 0===t?null:t)).getStartOfWeek()},e.getMinimumDaysInFirstWeek=function(e){var e=void 0===e?{}:e,t=e.locale,e=e.locObj;return((void 0===e?null:e)||b.create(void 0===t?null:t)).getMinDaysInFirstWeek()},e.getWeekendWeekdays=function(e){var e=void 0===e?{}:e,t=e.locale,e=e.locObj;return((void 0===e?null:e)||b.create(void 0===t?null:t)).getWeekendDays().slice()},e.months=function(e,t){void 0===e&&(e="long");var t=void 0===t?{}:t,n=t.locale,r=t.numberingSystem,i=t.locObj,i=void 0===i?null:i,t=t.outputCalendar;return(i||b.create(void 0===n?null:n,void 0===r?null:r,void 0===t?"gregory":t)).months(e)},e.monthsFormat=function(e,t){void 0===e&&(e="long");var t=void 0===t?{}:t,n=t.locale,r=t.numberingSystem,i=t.locObj,i=void 0===i?null:i,t=t.outputCalendar;return(i||b.create(void 0===n?null:n,void 0===r?null:r,void 0===t?"gregory":t)).months(e,!0)},e.weekdays=function(e,t){void 0===e&&(e="long");var t=void 0===t?{}:t,n=t.locale,r=t.numberingSystem,t=t.locObj;return((void 0===t?null:t)||b.create(void 0===n?null:n,void 0===r?null:r,null)).weekdays(e)},e.weekdaysFormat=function(e,t){void 0===e&&(e="long");var t=void 0===t?{}:t,n=t.locale,r=t.numberingSystem,t=t.locObj;return((void 0===t?null:t)||b.create(void 0===n?null:n,void 0===r?null:r,null)).weekdays(e,!0)},e.meridiems=function(e){e=(void 0===e?{}:e).locale;return b.create(void 0===e?null:e).meridiems()},e.eras=function(e,t){void 0===e&&(e="short");t=(void 0===t?{}:t).locale;return b.create(void 0===t?null:t,null,"gregory").eras(e)},e.features=function(){return{relative:ft(),localeWeek:dt()}},e}();function Wn(e,t){function n(e){return e.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf()}t=n(t)-n(e);return Math.floor(x.fromMillis(t).as("days"))}function Ln(e,t,n,r){var e=function(e,t,n){for(var r,i,o={},a=e,s=0,u=[["years",function(e,t){return t.year-e.year}],["quarters",function(e,t){return t.quarter-e.quarter+4*(t.year-e.year)}],["months",function(e,t){return t.month-e.month+12*(t.year-e.year)}],["weeks",function(e,t){e=Wn(e,t);return(e-e%7)/7}],["days",Wn]];s<u.length;s++){var l=u[s],c=l[0],l=l[1];0<=n.indexOf(c)&&(o[r=c]=l(e,t),t<(i=a.plus(o))?(o[c]--,t<(e=a.plus(o))&&(i=e,o[c]--,e=a.plus(o))):e=i)}return[e,o,i,r]}(e,t,n),i=e[0],o=e[1],a=e[2],e=e[3],s=t-i,n=n.filter(function(e){return 0<=["hours","minutes","seconds","milliseconds"].indexOf(e)}),t=(0===n.length&&(a=a<t?i.plus(((t={})[e]=1,t)):a)!==i&&(o[e]=(o[e]||0)+s/(a-i)),x.fromObject(o,r));return 0<n.length?(e=x.fromMillis(s,r)).shiftTo.apply(e,n).plus(t):t}var jn="missing Intl.DateTimeFormat.formatToParts support";function F(e,t){return void 0===t&&(t=function(e){return e}),{regex:e,deser:function(e){e=e[0];return t(function(e){var t=parseInt(e,10);if(isNaN(t)){for(var t="",n=0;n<e.length;n++){var r=e.charCodeAt(n);if(-1!==e[n].search(Ae.hanidec))t+=_e.indexOf(e[n]);else for(var i in qe){var i=qe[i],o=i[0],i=i[1];o<=r&&r<=i&&(t+=r-o)}}return parseInt(t,10)}return t}(e))}}}var zn="[ "+String.fromCharCode(160)+"]",An=new RegExp(zn,"g");function qn(e){return e.replace(/\./g,"\\.?").replace(An,zn)}function _n(e){return e.replace(/\./g,"").replace(An," ").toLowerCase()}function C(n,r){return null===n?null:{regex:RegExp(n.map(qn).join("|")),deser:function(e){var t=e[0];return n.findIndex(function(e){return _n(t)===_n(e)})+r}}}function Un(e,t){return{regex:e,deser:function(e){return Tt(e[1],e[2])},groups:t}}function Rn(e){return{regex:e,deser:function(e){return e[0]}}}function Pn(t,n){function r(e){return{regex:RegExp(e.val.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")),deser:function(e){return e[0]},literal:!0}}var i=y(n),o=y(n,"{2}"),a=y(n,"{3}"),s=y(n,"{4}"),u=y(n,"{6}"),l=y(n,"{1,2}"),c=y(n,"{1,3}"),f=y(n,"{1,6}"),d=y(n,"{1,9}"),h=y(n,"{2,4}"),m=y(n,"{4,6}"),e=function(e){if(t.literal)return r(e);switch(e.val){case"G":return C(n.eras("short"),0);case"GG":return C(n.eras("long"),0);case"y":return F(f);case"yy":return F(h,St);case"yyyy":return F(s);case"yyyyy":return F(m);case"yyyyyy":return F(u);case"M":return F(l);case"MM":return F(o);case"MMM":return C(n.months("short",!0),1);case"MMMM":return C(n.months("long",!0),1);case"L":return F(l);case"LL":return F(o);case"LLL":return C(n.months("short",!1),1);case"LLLL":return C(n.months("long",!1),1);case"d":return F(l);case"dd":return F(o);case"o":return F(c);case"ooo":return F(a);case"HH":return F(o);case"H":return F(l);case"hh":return F(o);case"h":return F(l);case"mm":return F(o);case"m":case"q":return F(l);case"qq":return F(o);case"s":return F(l);case"ss":return F(o);case"S":return F(c);case"SSS":return F(a);case"u":return Rn(d);case"uu":return Rn(l);case"uuu":return F(i);case"a":return C(n.meridiems(),0);case"kkkk":return F(s);case"kk":return F(h,St);case"W":return F(l);case"WW":return F(o);case"E":case"c":return F(i);case"EEE":return C(n.weekdays("short",!1),1);case"EEEE":return C(n.weekdays("long",!1),1);case"ccc":return C(n.weekdays("short",!0),1);case"cccc":return C(n.weekdays("long",!0),1);case"Z":case"ZZ":return Un(new RegExp("([+-]"+l.source+")(?::("+o.source+"))?"),2);case"ZZZ":return Un(new RegExp("([+-]"+l.source+")("+o.source+")?"),2);case"z":return Rn(/[a-z_+-/]{1,256}?/i);case" ":return Rn(/[^\S\n\r]/);default:return r(e)}}(t)||{invalidReason:jn};return e.token=t,e}var Yn={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};var Hn=null;function Jn(e,n){var t;return(t=Array.prototype).concat.apply(t,e.map(function(e){return t=n,(e=e).literal||null==(t=Bn(I.macroTokenToFormatOpts(e.val),t))||t.includes(void 0)?e:t;var t}))}var Gn=function(){function e(t,e){var n;this.locale=t,this.format=e,this.tokens=Jn(I.parseFormat(e),t),this.units=this.tokens.map(function(e){return Pn(e,t)}),this.disqualifyingUnit=this.units.find(function(e){return e.invalidReason}),this.disqualifyingUnit||(n=(e=["^"+(e=this.units).map(function(e){return e.regex}).reduce(function(e,t){return e+"("+t.source+")"},"")+"$",e])[1],this.regex=RegExp(e[0],"i"),this.handlers=n)}return e.prototype.explainFromTokens=function(e){if(this.isValid){var t=function(e,t,n){var r=e.match(t);if(r){var i,o,a,s={},u=1;for(i in n)h(n,i)&&(a=(o=n[i]).groups?o.groups+1:1,!o.literal&&o.token&&(s[o.token.val[0]]=o.deser(r.slice(u,u+a))),u+=a);return[r,s]}return[r,{}]}(e,this.regex,this.handlers),n=t[0],t=t[1],r=t?(r=null,N((s=t).z)||(r=c.create(s.z)),N(s.Z)||(r=r||new f(s.Z),i=s.Z),N(s.q)||(s.M=3*(s.q-1)+1),N(s.h)||(s.h<12&&1===s.a?s.h+=12:12===s.h&&0===s.a&&(s.h=0)),0===s.G&&s.y&&(s.y=-s.y),N(s.u)||(s.S=yt(s.u)),[Object.keys(s).reduce(function(e,t){var n=function(e){switch(e){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}}(t);return n&&(e[n]=s[t]),e},{}),r,i]):[null,null,void 0],i=r[0],o=r[1],a=r[2];if(h(t,"a")&&h(t,"H"))throw new w("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:this.tokens,regex:this.regex,rawMatches:n,matches:t,result:i,zone:o,specificOffset:a}}return{input:e,tokens:this.tokens,invalidReason:this.invalidReason};var s,i,r},i(e,[{key:"isValid",get:function(){return!this.disqualifyingUnit}},{key:"invalidReason",get:function(){return this.disqualifyingUnit?this.disqualifyingUnit.invalidReason:null}}]),e}();function $n(e,t,n){return new Gn(e,n).explainFromTokens(t)}function Bn(o,e){var t,a;return o?(t=(e=I.create(e,o).dtFormatter(Hn=Hn||W.fromMillis(1555555555555))).formatToParts(),a=e.resolvedOptions(),t.map(function(e){return t=o,n=a,i=(e=e).type,e=e.value,"literal"===i?{literal:!(r=/^\s+$/.test(e)),val:r?" ":e}:(r=t[i],"hour"===(e=i)&&(e=null!=t.hour12?t.hour12?"hour12":"hour24":null!=t.hourCycle?"h11"===t.hourCycle||"h12"===t.hourCycle?"hour12":"hour24":n.hour12?"hour12":"hour24"),(i="object"==typeof(i=Yn[e])?i[r]:i)?{literal:!1,val:i}:void 0);var t,n,r,i})):null}var Qn="Invalid DateTime";function Kn(e){return new d("unsupported zone",'the zone "'+e.name+'" is not supported')}function Xn(e){return null===e.weekData&&(e.weekData=rt(e.c)),e.weekData}function er(e){return null===e.localWeekData&&(e.localWeekData=rt(e.c,e.loc.getMinDaysInFirstWeek(),e.loc.getStartOfWeek())),e.localWeekData}function Z(e,t){e={ts:e.ts,zone:e.zone,c:e.c,o:e.o,loc:e.loc,invalid:e.invalid};return new W(l({},e,t,{old:e}))}function tr(e,t,n){var r=e-60*t*1e3,i=n.offset(r);return t===i?[r,t]:i===(n=n.offset(r-=60*(i-t)*1e3))?[r,i]:[e-60*Math.min(i,n)*1e3,Math.max(i,n)]}function nr(e,t){e+=60*t*1e3;t=new Date(e);return{year:t.getUTCFullYear(),month:t.getUTCMonth()+1,day:t.getUTCDate(),hour:t.getUTCHours(),minute:t.getUTCMinutes(),second:t.getUTCSeconds(),millisecond:t.getUTCMilliseconds()}}function rr(e,t,n){return tr(kt(e),t,n)}function ir(e,t){var n=e.o,r=e.c.year+Math.trunc(t.years),i=e.c.month+Math.trunc(t.months)+3*Math.trunc(t.quarters),r=l({},e.c,{year:r,month:i,day:Math.min(e.c.day,pt(r,i))+Math.trunc(t.days)+7*Math.trunc(t.weeks)}),i=x.fromObject({years:t.years-Math.trunc(t.years),quarters:t.quarters-Math.trunc(t.quarters),months:t.months-Math.trunc(t.months),weeks:t.weeks-Math.trunc(t.weeks),days:t.days-Math.trunc(t.days),hours:t.hours,minutes:t.minutes,seconds:t.seconds,milliseconds:t.milliseconds}).as("milliseconds"),t=tr(kt(r),n,e.zone),r=t[0],n=t[1];return 0!==i&&(n=e.zone.offset(r+=i)),{ts:r,o:n}}function or(e,t,n,r,i,o){var a=n.setZone,s=n.zone;return e&&0!==Object.keys(e).length||t?(e=W.fromObject(e,l({},n,{zone:t||s,specificOffset:o})),a?e:e.setZone(s)):W.invalid(new d("unparsable",'the input "'+i+"\" can't be parsed as "+r))}function ar(e,t,n){return void 0===n&&(n=!0),e.isValid?I.create(b.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(e,t):null}function sr(e,t){var n=9999<e.c.year||e.c.year<0,r="";return n&&0<=e.c.year&&(r+="+"),r+=m(e.c.year,n?6:4),r=t?(r=(r+="-")+m(e.c.month)+"-")+m(e.c.day):(r+=m(e.c.month))+m(e.c.day)}function ur(e,t,n,r,i,o){var a=m(e.c.hour);return t?(a=(a+=":")+m(e.c.minute),0===e.c.millisecond&&0===e.c.second&&n||(a+=":")):a+=m(e.c.minute),0===e.c.millisecond&&0===e.c.second&&n||(a+=m(e.c.second),0===e.c.millisecond&&r)||(a=(a+=".")+m(e.c.millisecond,3)),i&&(e.isOffsetFixed&&0===e.offset&&!o?a+="Z":a=e.o<0?(a=(a+="-")+m(Math.trunc(-e.o/60))+":")+m(Math.trunc(-e.o%60)):(a=(a+="+")+m(Math.trunc(e.o/60))+":")+m(Math.trunc(e.o%60))),o&&(a+="["+e.zone.ianaName+"]"),a}var lr,cr={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},fr={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},dr={ordinal:1,hour:0,minute:0,second:0,millisecond:0},hr=["year","month","day","hour","minute","second","millisecond"],mr=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],yr=["year","ordinal","hour","minute","second","millisecond"];function vr(e){switch(e.toLowerCase()){case"localweekday":case"localweekdays":return"localWeekday";case"localweeknumber":case"localweeknumbers":return"localWeekNumber";case"localweekyear":case"localweekyears":return"localWeekYear";default:var t=e,n={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[t.toLowerCase()];if(n)return n;throw new J(t)}}function gr(e,t){var n=S(t.zone,O.defaultZone);if(!n.isValid)return W.invalid(Kn(n));var r,t=b.fromObject(t);if(N(e.year))u=O.now();else{for(var i=0,o=hr;i<o.length;i++){var a=o[i];N(e[a])&&(e[a]=cr[a])}var s=ut(e)||lt(e);if(s)return W.invalid(s);s=n,void 0===lr&&(lr=O.now());var s=rr(e,"iana"!==s.type?s.offset(lr):(l=s.name,void 0===(r=wr.get(l))&&(r=s.offset(lr),wr.set(l,r)),r),n),u=s[0],l=s[1]}return new W({ts:u,zone:n,loc:t,o:l})}function pr(t,n,r){function e(e,t){return e=vt(e,o||r.calendary?0:2,!0),n.loc.clone(r).relFormatter(r).format(e,t)}function i(e){return r.calendary?n.hasSame(t,e)?0:n.startOf(e).diff(t.startOf(e),e).get(e):n.diff(t,e).get(e)}var o=!!N(r.round)||r.round;if(r.unit)return e(i(r.unit),r.unit);for(var a=R(r.units);!(s=a()).done;){var s=s.value,u=i(s);if(1<=Math.abs(u))return e(u,s)}return e(n<t?-0:0,r.units[r.units.length-1])}function kr(e){var t={},e=0<e.length&&"object"==typeof e[e.length-1]?(t=e[e.length-1],Array.from(e).slice(0,e.length-1)):Array.from(e);return[t,e]}var wr=new Map,W=function(e){function k(e){var t,n=e.zone||O.defaultZone,r=e.invalid||(Number.isNaN(e.ts)?new d("invalid input"):null)||(n.isValid?null:Kn(n)),i=(this.ts=N(e.ts)?O.now():e.ts,null),o=null;r||(o=e.old&&e.old.ts===this.ts&&e.old.zone.equals(n)?(i=(t=[e.old.c,e.old.o])[0],t[1]):(t=v(e.o)&&!e.old?e.o:n.offset(this.ts),i=nr(this.ts,t),i=(r=Number.isNaN(i.year)?new d("invalid input"):null)?null:i,r?null:t)),this._zone=n,this.loc=e.loc||b.create(),this.invalid=r,this.weekData=null,this.localWeekData=null,this.c=i,this.o=o,this.isLuxonDateTime=!0}k.now=function(){return new k({})},k.local=function(){var e=kr(arguments),t=e[0],e=e[1];return gr({year:e[0],month:e[1],day:e[2],hour:e[3],minute:e[4],second:e[5],millisecond:e[6]},t)},k.utc=function(){var e=kr(arguments),t=e[0],e=e[1],n=e[0],r=e[1],i=e[2],o=e[3],a=e[4],s=e[5],e=e[6];return t.zone=f.utcInstance,gr({year:n,month:r,day:i,hour:o,minute:a,second:s,millisecond:e},t)},k.fromJSDate=function(e,t){void 0===t&&(t={});var n,e="[object Date]"===Object.prototype.toString.call(e)?e.valueOf():NaN;return Number.isNaN(e)?k.invalid("invalid input"):(n=S(t.zone,O.defaultZone)).isValid?new k({ts:e,zone:n,loc:b.fromObject(t)}):k.invalid(Kn(n))},k.fromMillis=function(e,t){if(void 0===t&&(t={}),v(e))return e<-864e13||864e13<e?k.invalid("Timestamp out of range"):new k({ts:e,zone:S(t.zone,O.defaultZone),loc:b.fromObject(t)});throw new u("fromMillis requires a numerical input, but received a "+typeof e+" with value "+e)},k.fromSeconds=function(e,t){if(void 0===t&&(t={}),v(e))return new k({ts:1e3*e,zone:S(t.zone,O.defaultZone),loc:b.fromObject(t)});throw new u("fromSeconds requires a numerical input")},k.fromObject=function(e,t){e=e||{};var n=S((t=void 0===t?{}:t).zone,O.defaultZone);if(!n.isValid)return k.invalid(Kn(n));var r=b.fromObject(t),i=Mt(e,vr),o=st(i,r),a=o.minDaysInFirstWeek,o=o.startOfWeek,s=O.now(),t=N(t.specificOffset)?n.offset(s):t.specificOffset,u=!N(i.ordinal),l=!N(i.year),c=!N(i.month)||!N(i.day),l=l||c,f=i.weekYear||i.weekNumber;if((l||u)&&f)throw new w("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(c&&u)throw new w("Can't mix ordinal dates with month/day");for(var d,c=f||i.weekday&&!l,h=nr(s,t),m=(c?(p=mr,d=fr,h=rt(h,a,o)):u?(p=yr,d=dr,h=ot(h)):(p=hr,d=cr),!1),y=R(p);!(v=y()).done;){var v=v.value;N(i[v])?i[v]=(m?d:h)[v]:m=!0}var g,p=(c?(f=a,s=o,g=ct((p=i).weekYear),f=M(p.weekNumber,1,bt(p.weekYear,f=void 0===f?4:f,s=void 0===s?1:s)),s=M(p.weekday,1,7),g?f?!s&&T("weekday",p.weekday):T("week",p.weekNumber):T("weekYear",p.weekYear)):u?(f=ct((g=i).year),s=M(g.ordinal,1,D(g.year)),f?!s&&T("ordinal",g.ordinal):T("year",g.year)):ut(i))||lt(i);return p?k.invalid(p):(s=new k({ts:(f=rr(c?it(i,a,o):u?at(i):i,t,n))[0],zone:n,o:f[1],loc:r}),i.weekday&&l&&e.weekday!==s.weekday?k.invalid("mismatched weekday","you can't specify both a weekday of "+i.weekday+" and a date of "+s.toISO()):s.isValid?s:k.invalid(s.invalid))},k.fromISO=function(e,t){void 0===t&&(t={});var n=Ht(e,[mn,pn],[yn,kn],[vn,wn],[gn,bn]);return or(n[0],n[1],t,"ISO 8601",e)},k.fromRFC2822=function(e,t){void 0===t&&(t={});var n=Ht(e.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim(),[sn,un]);return or(n[0],n[1],t,"RFC 2822",e)},k.fromHTTP=function(e,t){void 0===t&&(t={});e=Ht(e,[ln,dn],[cn,dn],[fn,hn]);return or(e[0],e[1],t,"HTTP",t)},k.fromFormat=function(e,t,n){if(void 0===n&&(n={}),N(e)||N(t))throw new u("fromFormat requires an input string and a format");var r=n,i=r.locale,r=r.numberingSystem,i=b.fromOpts({locale:void 0===i?null:i,numberingSystem:void 0===r?null:r,defaultToEN:!0}),i=[(r=$n(r=i,e,t)).result,r.zone,r.specificOffset,r.invalidReason],r=i[0],o=i[1],a=i[2],i=i[3];return i?k.invalid(i):or(r,o,n,"format "+t,e,a)},k.fromString=function(e,t,n){return k.fromFormat(e,t,n=void 0===n?{}:n)},k.fromSQL=function(e,t){void 0===t&&(t={});var n=Ht(e,[On,pn],[Tn,Nn]);return or(n[0],n[1],t,"SQL",e)},k.invalid=function(e,t){if(void 0===t&&(t=null),!e)throw new u("need to specify a reason the DateTime is invalid");e=e instanceof d?e:new d(e,t);if(O.throwOnInvalid)throw new P(e);return new k({invalid:e})},k.isDateTime=function(e){return e&&e.isLuxonDateTime||!1},k.parseFormatForOpts=function(e,t){e=Bn(e,b.fromObject(t=void 0===t?{}:t));return e?e.map(function(e){return e?e.val:null}).join(""):null},k.expandFormat=function(e,t){return void 0===t&&(t={}),Jn(I.parseFormat(e),b.fromObject(t)).map(function(e){return e.val}).join("")},k.resetCache=function(){lr=void 0,wr.clear()};var t=k.prototype;return t.get=function(e){return this[e]},t.getPossibleOffsets=function(){var e,t,n,r;return this.isValid&&!this.isOffsetFixed&&(e=kt(this.c),n=this.zone.offset(e-864e5),r=this.zone.offset(e+864e5),(n=this.zone.offset(e-6e4*n))!==(r=this.zone.offset(e-6e4*r)))&&(t=e-6e4*r,n=nr(e=e-6e4*n,n),r=nr(t,r),n.hour===r.hour)&&n.minute===r.minute&&n.second===r.second&&n.millisecond===r.millisecond?[Z(this,{ts:e}),Z(this,{ts:t})]:[this]},t.resolvedLocaleOptions=function(e){e=I.create(this.loc.clone(e=void 0===e?{}:e),e).resolvedOptions(this);return{locale:e.locale,numberingSystem:e.numberingSystem,outputCalendar:e.calendar}},t.toUTC=function(e,t){return void 0===t&&(t={}),this.setZone(f.instance(e=void 0===e?0:e),t)},t.toLocal=function(){return this.setZone(O.defaultZone)},t.setZone=function(e,t){var n,t=void 0===t?{}:t,r=t.keepLocalTime,r=void 0!==r&&r,t=t.keepCalendarTime,t=void 0!==t&&t;return(e=S(e,O.defaultZone)).equals(this.zone)?this:e.isValid?(n=this.ts,(r||t)&&(r=e.offset(this.ts),n=rr(this.toObject(),r,e)[0]),Z(this,{ts:n,zone:e})):k.invalid(Kn(e))},t.reconfigure=function(e){var e=void 0===e?{}:e,t=e.locale,n=e.numberingSystem,e=e.outputCalendar,t=this.loc.clone({locale:t,numberingSystem:n,outputCalendar:e});return Z(this,{loc:t})},t.setLocale=function(e){return this.reconfigure({locale:e})},t.set=function(e){if(!this.isValid)return this;var t,e=Mt(e,vr),n=st(e,this.loc),r=n.minDaysInFirstWeek,n=n.startOfWeek,i=!N(e.weekYear)||!N(e.weekNumber)||!N(e.weekday),o=!N(e.ordinal),a=!N(e.year),s=!N(e.month)||!N(e.day),u=e.weekYear||e.weekNumber;if((a||s||o)&&u)throw new w("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(s&&o)throw new w("Can't mix ordinal dates with month/day");i?t=it(l({},rt(this.c,r,n),e),r,n):N(e.ordinal)?(t=l({},this.toObject(),e),N(e.day)&&(t.day=Math.min(pt(t.year,t.month),t.day))):t=at(l({},ot(this.c),e));a=rr(t,this.o,this.zone);return Z(this,{ts:a[0],o:a[1]})},t.plus=function(e){return this.isValid?Z(this,ir(this,x.fromDurationLike(e))):this},t.minus=function(e){return this.isValid?Z(this,ir(this,x.fromDurationLike(e).negate())):this},t.startOf=function(e,t){t=(void 0===t?{}:t).useLocaleWeeks,t=void 0!==t&&t;if(!this.isValid)return this;var n={},e=x.normalizeUnit(e);switch(e){case"years":n.month=1;case"quarters":case"months":n.day=1;case"weeks":case"days":n.hour=0;case"hours":n.minute=0;case"minutes":n.second=0;case"seconds":n.millisecond=0}return"weeks"===e&&(t?(t=this.loc.getStartOfWeek(),this.weekday<t&&(n.weekNumber=this.weekNumber-1),n.weekday=t):n.weekday=1),"quarters"===e&&(t=Math.ceil(this.month/3),n.month=3*(t-1)+1),this.set(n)},t.endOf=function(e,t){var n;return this.isValid?this.plus(((n={})[e]=1,n)).startOf(e,t).minus(1):this},t.toFormat=function(e,t){return void 0===t&&(t={}),this.isValid?I.create(this.loc.redefaultToEN(t)).formatDateTimeFromString(this,e):Qn},t.toLocaleString=function(e,t){return void 0===e&&(e=G),void 0===t&&(t={}),this.isValid?I.create(this.loc.clone(t),e).formatDateTime(this):Qn},t.toLocaleParts=function(e){return void 0===e&&(e={}),this.isValid?I.create(this.loc.clone(e),e).formatDateTimeParts(this):[]},t.toISO=function(e){var t,e=void 0===e?{}:e,n=e.format,r=e.suppressSeconds,r=void 0!==r&&r,i=e.suppressMilliseconds,i=void 0!==i&&i,o=e.includeOffset,o=void 0===o||o,e=e.extendedZone,e=void 0!==e&&e;return this.isValid?(t=sr(this,n="extended"===(void 0===n?"extended":n)),(t+="T")+ur(this,n,r,i,o,e)):null},t.toISODate=function(e){e=(void 0===e?{}:e).format;return this.isValid?sr(this,"extended"===(void 0===e?"extended":e)):null},t.toISOWeekDate=function(){return ar(this,"kkkk-'W'WW-c")},t.toISOTime=function(e){var e=void 0===e?{}:e,t=e.suppressMilliseconds,n=e.suppressSeconds,r=e.includeOffset,i=e.includePrefix,o=e.extendedZone,e=e.format;return this.isValid?(void 0!==i&&i?"T":"")+ur(this,"extended"===(void 0===e?"extended":e),void 0!==n&&n,void 0!==t&&t,void 0===r||r,void 0!==o&&o):null},t.toRFC2822=function(){return ar(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)},t.toHTTP=function(){return ar(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")},t.toSQLDate=function(){return this.isValid?sr(this,!0):null},t.toSQLTime=function(e){var e=void 0===e?{}:e,t=e.includeOffset,t=void 0===t||t,n=e.includeZone,n=void 0!==n&&n,e=e.includeOffsetSpace,r="HH:mm:ss.SSS";return(n||t)&&((void 0===e||e)&&(r+=" "),n?r+="z":t&&(r+="ZZ")),ar(this,r,!0)},t.toSQL=function(e){return void 0===e&&(e={}),this.isValid?this.toSQLDate()+" "+this.toSQLTime(e):null},t.toString=function(){return this.isValid?this.toISO():Qn},t[e]=function(){return this.isValid?"DateTime { ts: "+this.toISO()+", zone: "+this.zone.name+", locale: "+this.locale+" }":"DateTime { Invalid, reason: "+this.invalidReason+" }"},t.valueOf=function(){return this.toMillis()},t.toMillis=function(){return this.isValid?this.ts:NaN},t.toSeconds=function(){return this.isValid?this.ts/1e3:NaN},t.toUnixInteger=function(){return this.isValid?Math.floor(this.ts/1e3):NaN},t.toJSON=function(){return this.toISO()},t.toBSON=function(){return this.toJSDate()},t.toObject=function(e){var t;return void 0===e&&(e={}),this.isValid?(t=l({},this.c),e.includeConfig&&(t.outputCalendar=this.outputCalendar,t.numberingSystem=this.loc.numberingSystem,t.locale=this.loc.locale),t):{}},t.toJSDate=function(){return new Date(this.isValid?this.ts:NaN)},t.diff=function(e,t,n){var r;return void 0===t&&(t="milliseconds"),void 0===n&&(n={}),this.isValid&&e.isValid?(n=l({locale:this.locale,numberingSystem:this.numberingSystem},n),t=t,t=(Array.isArray(t)?t:[t]).map(x.normalizeUnit),e=Ln((r=e.valueOf()>this.valueOf())?this:e,r?e:this,t,n),r?e.negate():e):x.invalid("created by diffing an invalid DateTime")},t.diffNow=function(e,t){return void 0===e&&(e="milliseconds"),void 0===t&&(t={}),this.diff(k.now(),e,t)},t.until=function(e){return this.isValid?Cn.fromDateTimes(this,e):this},t.hasSame=function(e,t,n){var r;return!!this.isValid&&(r=e.valueOf(),(e=this.setZone(e.zone,{keepLocalTime:!0})).startOf(t,n)<=r)&&r<=e.endOf(t,n)},t.equals=function(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)},t.toRelative=function(e){var t,n,r,i;return this.isValid?(t=(e=void 0===e?{}:e).base||k.fromObject({},{zone:this.zone}),n=e.padding?this<t?-e.padding:e.padding:0,r=["years","months","days","hours","minutes","seconds"],i=e.unit,Array.isArray(e.unit)&&(r=e.unit,i=void 0),pr(t,this.plus(n),l({},e,{numeric:"always",units:r,unit:i}))):null},t.toRelativeCalendar=function(e){return void 0===e&&(e={}),this.isValid?pr(e.base||k.fromObject({},{zone:this.zone}),this,l({},e,{numeric:"auto",units:["years","months","days"],calendary:!0})):null},k.min=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(t.every(k.isDateTime))return ht(t,function(e){return e.valueOf()},Math.min);throw new u("min requires all arguments be DateTimes")},k.max=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(t.every(k.isDateTime))return ht(t,function(e){return e.valueOf()},Math.max);throw new u("max requires all arguments be DateTimes")},k.fromFormatExplain=function(e,t,n){var n=n=void 0===n?{}:n,r=n.locale,n=n.numberingSystem;return $n(b.fromOpts({locale:void 0===r?null:r,numberingSystem:void 0===n?null:n,defaultToEN:!0}),e,t)},k.fromStringExplain=function(e,t,n){return k.fromFormatExplain(e,t,n=void 0===n?{}:n)},k.buildFormatParser=function(e,t){var t=t=void 0===t?{}:t,n=t.locale,t=t.numberingSystem,n=b.fromOpts({locale:void 0===n?null:n,numberingSystem:void 0===t?null:t,defaultToEN:!0});return new Gn(n,e)},k.fromFormatParser=function(e,t,n){if(void 0===n&&(n={}),N(e)||N(t))throw new u("fromFormatParser requires an input string and a format parser");var r,i,o,a=n,s=a.locale,a=a.numberingSystem,s=b.fromOpts({locale:void 0===s?null:s,numberingSystem:void 0===a?null:a,defaultToEN:!0});if(s.equals(t.locale))return r=(a=t.explainFromTokens(e)).result,i=a.zone,o=a.specificOffset,(a=a.invalidReason)?k.invalid(a):or(r,i,n,"format "+t.format,e,o);throw new u("fromFormatParser called with a locale of "+s+", but the format parser was created for "+t.locale)},i(k,[{key:"isValid",get:function(){return null===this.invalid}},{key:"invalidReason",get:function(){return this.invalid?this.invalid.reason:null}},{key:"invalidExplanation",get:function(){return this.invalid?this.invalid.explanation:null}},{key:"locale",get:function(){return this.isValid?this.loc.locale:null}},{key:"numberingSystem",get:function(){return this.isValid?this.loc.numberingSystem:null}},{key:"outputCalendar",get:function(){return this.isValid?this.loc.outputCalendar:null}},{key:"zone",get:function(){return this._zone}},{key:"zoneName",get:function(){return this.isValid?this.zone.name:null}},{key:"year",get:function(){return this.isValid?this.c.year:NaN}},{key:"quarter",get:function(){return this.isValid?Math.ceil(this.c.month/3):NaN}},{key:"month",get:function(){return this.isValid?this.c.month:NaN}},{key:"day",get:function(){return this.isValid?this.c.day:NaN}},{key:"hour",get:function(){return this.isValid?this.c.hour:NaN}},{key:"minute",get:function(){return this.isValid?this.c.minute:NaN}},{key:"second",get:function(){return this.isValid?this.c.second:NaN}},{key:"millisecond",get:function(){return this.isValid?this.c.millisecond:NaN}},{key:"weekYear",get:function(){return this.isValid?Xn(this).weekYear:NaN}},{key:"weekNumber",get:function(){return this.isValid?Xn(this).weekNumber:NaN}},{key:"weekday",get:function(){return this.isValid?Xn(this).weekday:NaN}},{key:"isWeekend",get:function(){return this.isValid&&this.loc.getWeekendDays().includes(this.weekday)}},{key:"localWeekday",get:function(){return this.isValid?er(this).weekday:NaN}},{key:"localWeekNumber",get:function(){return this.isValid?er(this).weekNumber:NaN}},{key:"localWeekYear",get:function(){return this.isValid?er(this).weekYear:NaN}},{key:"ordinal",get:function(){return this.isValid?ot(this.c).ordinal:NaN}},{key:"monthShort",get:function(){return this.isValid?Zn.months("short",{locObj:this.loc})[this.month-1]:null}},{key:"monthLong",get:function(){return this.isValid?Zn.months("long",{locObj:this.loc})[this.month-1]:null}},{key:"weekdayShort",get:function(){return this.isValid?Zn.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}},{key:"weekdayLong",get:function(){return this.isValid?Zn.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}},{key:"offset",get:function(){return this.isValid?+this.o:NaN}},{key:"offsetNameShort",get:function(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}},{key:"offsetNameLong",get:function(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}},{key:"isOffsetFixed",get:function(){return this.isValid?this.zone.isUniversal:null}},{key:"isInDST",get:function(){return!this.isOffsetFixed&&(this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset)}},{key:"isInLeapYear",get:function(){return gt(this.year)}},{key:"daysInMonth",get:function(){return pt(this.year,this.month)}},{key:"daysInYear",get:function(){return this.isValid?D(this.year):NaN}},{key:"weeksInWeekYear",get:function(){return this.isValid?bt(this.weekYear):NaN}},{key:"weeksInLocalWeekYear",get:function(){return this.isValid?bt(this.localWeekYear,this.loc.getMinDaysInFirstWeek(),this.loc.getStartOfWeek()):NaN}}],[{key:"DATE_SHORT",get:function(){return G}},{key:"DATE_MED",get:function(){return $}},{key:"DATE_MED_WITH_WEEKDAY",get:function(){return B}},{key:"DATE_FULL",get:function(){return Q}},{key:"DATE_HUGE",get:function(){return K}},{key:"TIME_SIMPLE",get:function(){return X}},{key:"TIME_WITH_SECONDS",get:function(){return ee}},{key:"TIME_WITH_SHORT_OFFSET",get:function(){return te}},{key:"TIME_WITH_LONG_OFFSET",get:function(){return ne}},{key:"TIME_24_SIMPLE",get:function(){return re}},{key:"TIME_24_WITH_SECONDS",get:function(){return ie}},{key:"TIME_24_WITH_SHORT_OFFSET",get:function(){return oe}},{key:"TIME_24_WITH_LONG_OFFSET",get:function(){return ae}},{key:"DATETIME_SHORT",get:function(){return se}},{key:"DATETIME_SHORT_WITH_SECONDS",get:function(){return ue}},{key:"DATETIME_MED",get:function(){return le}},{key:"DATETIME_MED_WITH_SECONDS",get:function(){return ce}},{key:"DATETIME_MED_WITH_WEEKDAY",get:function(){return fe}},{key:"DATETIME_FULL",get:function(){return de}},{key:"DATETIME_FULL_WITH_SECONDS",get:function(){return he}},{key:"DATETIME_HUGE",get:function(){return me}},{key:"DATETIME_HUGE_WITH_SECONDS",get:function(){return ye}}]),k}(Symbol.for("nodejs.util.inspect.custom"));function br(e){if(W.isDateTime(e))return e;if(e&&e.valueOf&&v(e.valueOf()))return W.fromJSDate(e);if(e&&"object"==typeof e)return W.fromObject(e);throw new u("Unknown datetime argument: "+e+", of type "+typeof e)}return e.DateTime=W,e.Duration=x,e.FixedOffsetZone=f,e.IANAZone=c,e.Info=Zn,e.Interval=Cn,e.InvalidZone=ze,e.Settings=O,e.SystemZone=ge,e.VERSION="3.6.1",e.Zone=s,Object.defineProperty(e,"__esModule",{value:!0}),e}({});