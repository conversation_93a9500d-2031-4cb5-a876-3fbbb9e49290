"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArgumentsValueBuilder = exports.args = void 0;
const args_1 = require("./args");
const arraybuilder_1 = require("./arraybuilder");
const boolean_1 = require("./boolean");
const exprbuilder_1 = require("./exprbuilder");
const identifier_1 = require("./identifier");
const null_1 = require("./null");
const number_1 = require("./number");
const objectbuilder_1 = require("./objectbuilder");
const string_1 = require("./string");
const undefined_1 = require("./undefined");
function args(build) {
    const builder = new ArgumentsValueBuilder();
    build(builder);
    return builder.build();
}
exports.args = args;
// Avoid extending ArrayValueBuilder due to circular dependencies.
class ArgumentsValueBuilder {
    #elements = [];
    empty() {
        this.#elements = [];
        return this;
    }
    string(value) {
        try {
            return this.value(new string_1.StringValue(value));
        }
        catch (cause) {
            throw new Error(`Failed to add argument #${this.#elements.length} (string): ${cause}`, { cause });
        }
    }
    boolean(value) {
        try {
            return this.value(new boolean_1.BooleanValue(value));
        }
        catch (cause) {
            throw new Error(`Failed to add argument #${this.#elements.length} (boolean): ${cause}`, { cause });
        }
    }
    number(value) {
        try {
            return this.value(new number_1.NumberValue(value));
        }
        catch (cause) {
            throw new Error(`Failed to add argument #${this.#elements.length} (number): ${cause}`, { cause });
        }
    }
    null() {
        try {
            return this.value(new null_1.NullValue());
        }
        catch (cause) {
            throw new Error(`Failed to add argument #${this.#elements.length} (null): ${cause}`, { cause });
        }
    }
    undefined() {
        try {
            return this.value(new undefined_1.UndefinedValue());
        }
        catch (cause) {
            throw new Error(`Failed to add argument #${this.#elements.length} (undefined): ${cause}`, { cause });
        }
    }
    ident(value) {
        try {
            return this.value(new identifier_1.IdentifierValue(value));
        }
        catch (cause) {
            throw new Error(`Failed to add argument #${this.#elements.length} (identifier): ${cause}`, { cause });
        }
    }
    array(build) {
        try {
            return this.value((0, arraybuilder_1.array)(build));
        }
        catch (cause) {
            throw new Error(`Failed to add argument #${this.#elements.length} (array): ${cause}`, { cause });
        }
    }
    object(build) {
        try {
            return this.value((0, objectbuilder_1.object)(build));
        }
        catch (cause) {
            throw new Error(`Failed to add argument #${this.#elements.length} (object): ${cause}`, { cause });
        }
    }
    expr(context, build) {
        try {
            return this.value((0, exprbuilder_1.expr)(context, build));
        }
        catch (cause) {
            throw new Error(`Failed to add argument #${this.#elements.length} (expr): ${cause}`, { cause });
        }
    }
    value(value) {
        this.#elements.push(value);
        return this;
    }
    get elements() {
        return this.#elements;
    }
    build() {
        return new args_1.ArgumentsValue([...this.#elements]);
    }
}
exports.ArgumentsValueBuilder = ArgumentsValueBuilder;
//# sourceMappingURL=argsbuilder.js.map