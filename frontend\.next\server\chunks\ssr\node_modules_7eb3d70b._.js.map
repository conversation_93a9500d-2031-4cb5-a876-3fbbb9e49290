{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40swc/helpers/cjs/_class_private_field_loose_base.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _class_private_field_loose_base(receiver, privateKey) {\n    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {\n        throw new TypeError(\"attempted to use private field on non-instance\");\n    }\n\n    return receiver;\n}\nexports._ = _class_private_field_loose_base;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,gCAAgC,QAAQ,EAAE,UAAU;IACzD,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,aAAa;QAC7D,MAAM,IAAI,UAAU;IACxB;IAEA,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40swc/helpers/cjs/_class_private_field_loose_key.cjs"], "sourcesContent": ["\"use strict\";\n\nvar id = 0;\n\nfunction _class_private_field_loose_key(name) {\n    return \"__private_\" + id++ + \"_\" + name;\n}\nexports._ = _class_private_field_loose_key;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,KAAK;AAET,SAAS,+BAA+B,IAAI;IACxC,OAAO,eAAe,OAAO,MAAM;AACvC;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40swc/helpers/cjs/_tagged_template_literal_loose.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _tagged_template_literal_loose(strings, raw) {\n    if (!raw) raw = strings.slice(0);\n\n    strings.raw = raw;\n\n    return strings;\n}\nexports._ = _tagged_template_literal_loose;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,+BAA+B,OAAO,EAAE,GAAG;IAChD,IAAI,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC;IAE9B,QAAQ,GAAG,GAAG;IAEd,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/routing/config.js"], "sourcesContent": ["function receiveRoutingConfig(input) {\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: input.localeDetection ?? true,\n    alternateLinks: input.alternateLinks ?? true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return localeCookie ?? true ? {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\nexport { receiveRoutingConfig };\n"], "names": [], "mappings": ";;;AAAA,SAAS,qBAAqB,KAAK;IACjC,OAAO;QACL,GAAG,KAAK;QACR,cAAc,0BAA0B,MAAM,YAAY;QAC1D,cAAc,oBAAoB,MAAM,YAAY;QACpD,iBAAiB,MAAM,eAAe,IAAI;QAC1C,gBAAgB,MAAM,cAAc,IAAI;IAC1C;AACF;AACA,SAAS,oBAAoB,YAAY;IACvC,OAAO,gBAAgB,OAAO;QAC5B,MAAM;QACN,UAAU;QACV,GAAI,OAAO,iBAAiB,YAAY,YAAY;IAItD,IAAI;AACN;AACA,SAAS,0BAA0B,YAAY;IAC7C,OAAO,OAAO,iBAAiB,WAAW,eAAe;QACvD,MAAM,gBAAgB;IACxB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/shared/use.js"], "sourcesContent": ["import * as react from 'react';\n\n// @ts-expect-error -- Ooof, Next.js doesn't make this easy.\n// `use` is only available in React 19 canary, but we can\n// use it in Next.js already as Next.js \"vendors\" a fixed\n// version of React. However, if we'd simply put `use` in\n// ESM code, then the build doesn't work since React does\n// not export `use` officially. Therefore, we have to use\n// something that is not statically analyzable. Once React\n// 19 is out, we can remove this in the next major version.\nvar use = react['use'.trim()];\n\nexport { use as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,4DAA4D;AAC5D,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,0DAA0D;AAC1D,2DAA2D;AAC3D,IAAI,MAAM,qMAAK,CAAC,MAAM,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/shared/utils.js"], "sourcesContent": ["function isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(`^${prefix}`), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch {\n    return false;\n  }\n}\nfunction getLocalizedTemplate(pathnameConfig, locale, internalTemplate) {\n  return typeof pathnameConfig === 'string' ? pathnameConfig : pathnameConfig[locale] || internalTemplate;\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  return localePrefix.mode !== 'never' && localePrefix.prefixes?.[locale] ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(`^${regexPattern}$`);\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\nexport { getLocaleAsPrefix, getLocalePrefix, getLocalizedTemplate, getSortedPathnames, hasPathnamePrefixed, isLocalizableHref, isPromise, matchesPathname, normalizeTrailingSlash, prefixPathname, templateToRegex, unprefixPathname };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,SAAS,eAAe,IAAI;IAC1B,MAAM,WAAW,OAAO,SAAS,WAAW,KAAK,QAAQ,GAAG;IAC5D,OAAO,YAAY,QAAQ,CAAC,SAAS,UAAU,CAAC;AAClD;AACA,SAAS,YAAY,IAAI;IACvB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,KAAK,IAAI,IAAI,QAAQ,KAAK,QAAQ,IAAI;IAC/C,OAAO;QACL,MAAM,cAAc,YAAY,IAAI,CAAC;QACrC,OAAO,CAAC;IACV;AACF;AACA,SAAS,kBAAkB,IAAI;IAC7B,OAAO,YAAY,SAAS,CAAC,eAAe;AAC9C;AACA,SAAS,iBAAiB,QAAQ,EAAE,MAAM;IACxC,OAAO,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,QAAQ,GAAG,OAAO;AAC3D;AACA,SAAS,eAAe,MAAM,EAAE,QAAQ;IACtC,IAAI,gBAAgB;IAEpB,yBAAyB;IACzB,IAAI,cAAc,IAAI,CAAC,WAAW;QAChC,WAAW,SAAS,KAAK,CAAC;IAC5B;IACA,iBAAiB;IACjB,OAAO;AACT;AACA,SAAS,oBAAoB,MAAM,EAAE,QAAQ;IAC3C,OAAO,aAAa,UAAU,SAAS,UAAU,CAAC,GAAG,OAAO,CAAC,CAAC;AAChE;AACA,SAAS;IACP,IAAI;QACF,gEAAgE;QAChE,OAAO,QAAQ,GAAG,CAAC,yBAAyB,KAAK;IACnD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AACA,SAAS,qBAAqB,cAAc,EAAE,MAAM,EAAE,gBAAgB;IACpE,OAAO,OAAO,mBAAmB,WAAW,iBAAiB,cAAc,CAAC,OAAO,IAAI;AACzF;AACA,SAAS,uBAAuB,QAAQ;IACtC,MAAM,gBAAgB;IACtB,IAAI,aAAa,KAAK;QACpB,MAAM,wBAAwB,SAAS,QAAQ,CAAC;QAChD,IAAI,iBAAiB,CAAC,uBAAuB;YAC3C,YAAY;QACd,OAAO,IAAI,CAAC,iBAAiB,uBAAuB;YAClD,WAAW,SAAS,KAAK,CAAC,GAAG,CAAC;QAChC;IACF;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,sCAAsC,GAC/D,QAAQ,EAAE,0BAA0B,GACpC,QAAQ;IACN,MAAM,qBAAqB,uBAAuB;IAClD,MAAM,qBAAqB,uBAAuB;IAClD,MAAM,QAAQ,gBAAgB;IAC9B,OAAO,MAAM,IAAI,CAAC;AACpB;AACA,SAAS,gBAAgB,MAAM,EAAE,YAAY;IAC3C,OAAO,aAAa,IAAI,KAAK,WAAW,aAAa,QAAQ,EAAE,CAAC,OAAO,IACvE,sEAAsE;IACtE,8BAA8B;IAC9B,kBAAkB;AACpB;AACA,SAAS,kBAAkB,MAAM;IAC/B,OAAO,MAAM;AACf;AACA,SAAS,gBAAgB,QAAQ;IAC/B,MAAM,eAAe,QACrB,4CAA4C;KAC3C,OAAO,CAAC,2BAA2B,QACpC,iCAAiC;KAChC,OAAO,CAAC,uBAAuB,OAChC,uCAAuC;KACtC,OAAO,CAAC,iBAAiB;IAC1B,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;AACvC;AACA,SAAS,0BAA0B,QAAQ;IACzC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AACA,SAAS,kBAAkB,QAAQ;IACjC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AACA,SAAS,iBAAiB,QAAQ;IAChC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AACA,SAAS,qBAAqB,CAAC,EAAE,CAAC;IAChC,MAAM,QAAQ,EAAE,KAAK,CAAC;IACtB,MAAM,QAAQ,EAAE,KAAK,CAAC;IACtB,MAAM,YAAY,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,MAAM,MAAM;IACrD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;QAClC,MAAM,WAAW,KAAK,CAAC,EAAE;QACzB,MAAM,WAAW,KAAK,CAAC,EAAE;QAEzB,wDAAwD;QACxD,IAAI,CAAC,YAAY,UAAU,OAAO,CAAC;QACnC,IAAI,YAAY,CAAC,UAAU,OAAO;QAClC,IAAI,CAAC,YAAY,CAAC,UAAU;QAE5B,mDAAmD;QACnD,IAAI,CAAC,iBAAiB,aAAa,iBAAiB,WAAW,OAAO,CAAC;QACvE,IAAI,iBAAiB,aAAa,CAAC,iBAAiB,WAAW,OAAO;QAEtE,4DAA4D;QAC5D,IAAI,CAAC,kBAAkB,aAAa,kBAAkB,WAAW,OAAO,CAAC;QACzE,IAAI,kBAAkB,aAAa,CAAC,kBAAkB,WAAW,OAAO;QAExE,8EAA8E;QAC9E,IAAI,CAAC,0BAA0B,aAAa,0BAA0B,WAAW;YAC/E,OAAO,CAAC;QACV;QACA,IAAI,0BAA0B,aAAa,CAAC,0BAA0B,WAAW;YAC/E,OAAO;QACT;QACA,IAAI,aAAa,UAAU;IAC7B;IAEA,uCAAuC;IACvC,OAAO;AACT;AACA,SAAS,mBAAmB,SAAS;IACnC,OAAO,UAAU,IAAI,CAAC;AACxB;AACA,SAAS,UAAU,KAAK;IACtB,kDAAkD;IAClD,OAAO,OAAO,MAAM,IAAI,KAAK;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/navigation/shared/utils.js"], "sourcesContent": ["import { getSortedPathnames, matchesPathname, isLocalizableHref, prefixPathname, getLocalizedTemplate, normalizeTrailingSlash, getLocalePrefix } from '../../shared/utils.js';\n\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n\n// For `Link`\n\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\n\nfunction normalizeNameOrNameWithParams(href) {\n  return typeof href === 'string' ? {\n    pathname: href\n  } : href;\n}\nfunction serializeSearchParams(searchParams) {\n  function serializeValue(value) {\n    return String(value);\n  }\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(searchParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(cur => {\n        urlSearchParams.append(key, serializeValue(cur));\n      });\n    } else {\n      urlSearchParams.set(key, serializeValue(value));\n    }\n  }\n  return '?' + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname({\n  pathname,\n  locale,\n  params,\n  pathnames,\n  query\n}) {\n  function getNamedPath(value) {\n    let namedPath = pathnames[value];\n    if (!namedPath) {\n      // Unknown pathnames\n      namedPath = value;\n    }\n    return namedPath;\n  }\n  function compilePath(namedPath, internalPathname) {\n    const template = getLocalizedTemplate(namedPath, locale, internalPathname);\n    let compiled = template;\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        let regexp, replacer;\n        if (Array.isArray(value)) {\n          regexp = `(\\\\[)?\\\\[...${key}\\\\](\\\\])?`;\n          replacer = value.map(v => String(v)).join('/');\n        } else {\n          regexp = `\\\\[${key}\\\\]`;\n          replacer = String(value);\n        }\n        compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);\n      });\n    }\n\n    // Clean up optional catch-all segments that were not replaced\n    compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, '');\n    compiled = normalizeTrailingSlash(compiled);\n    if (compiled.includes('[')) {\n      // Next.js throws anyway, therefore better provide a more helpful error message\n      throw new Error(`Insufficient params provided for localized pathname.\\nTemplate: ${template}\\nParams: ${JSON.stringify(params)}`);\n    }\n    if (query) {\n      compiled += serializeSearchParams(query);\n    }\n    return compiled;\n  }\n  if (typeof pathname === 'string') {\n    const namedPath = getNamedPath(pathname);\n    const compiled = compilePath(namedPath, pathname);\n    return compiled;\n  } else {\n    const {\n      pathname: internalPathname,\n      ...rest\n    } = pathname;\n    const namedPath = getNamedPath(internalPathname);\n    const compiled = compilePath(namedPath, internalPathname);\n    const result = {\n      ...rest,\n      pathname: compiled\n    };\n    return result;\n  }\n}\nfunction getRoute(locale, pathname, pathnames) {\n  const sortedPathnames = getSortedPathnames(Object.keys(pathnames));\n  const decoded = decodeURI(pathname);\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if (matchesPathname(localizedPathname, decoded)) {\n        return internalPathname;\n      }\n    } else {\n      if (matchesPathname(getLocalizedTemplate(localizedPathnamesOrPathname, locale, internalPathname), decoded)) {\n        return internalPathname;\n      }\n    }\n  }\n  return pathname;\n}\nfunction getBasePath(pathname, windowPathname = window.location.pathname) {\n  if (pathname === '/') {\n    return windowPathname;\n  } else {\n    return windowPathname.replace(pathname, '');\n  }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, force) {\n  const {\n    mode\n  } = routing.localePrefix;\n  let shouldPrefix;\n  if (force !== undefined) {\n    shouldPrefix = force;\n  } else if (isLocalizableHref(pathname)) {\n    if (mode === 'always') {\n      shouldPrefix = true;\n    } else if (mode === 'as-needed') {\n      shouldPrefix = routing.domains ?\n      // Since locales are unique per domain, any locale that is a\n      // default locale of a domain doesn't require a prefix\n      !routing.domains.some(cur => cur.defaultLocale === locale) : locale !== routing.defaultLocale;\n    }\n  }\n  return shouldPrefix ? prefixPathname(getLocalePrefix(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n  if (config.localePrefix?.mode === 'as-needed' && !('defaultLocale' in config)) {\n    throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n  }\n}\n\nexport { applyPathnamePrefix, compileLocalizedPathname, getBasePath, getRoute, normalizeNameOrNameWithParams, serializeSearchParams, validateReceivedConfig };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA,2DAA2D;AAC3D,8CAA8C;AAE9C,aAAa;AAEb,2EAA2E;AAE3E,SAAS,8BAA8B,IAAI;IACzC,OAAO,OAAO,SAAS,WAAW;QAChC,UAAU;IACZ,IAAI;AACN;AACA,SAAS,sBAAsB,YAAY;IACzC,SAAS,eAAe,KAAK;QAC3B,OAAO,OAAO;IAChB;IACA,MAAM,kBAAkB,IAAI;IAC5B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,cAAe;QACvD,IAAI,MAAM,OAAO,CAAC,QAAQ;YACxB,MAAM,OAAO,CAAC,CAAA;gBACZ,gBAAgB,MAAM,CAAC,KAAK,eAAe;YAC7C;QACF,OAAO;YACL,gBAAgB,GAAG,CAAC,KAAK,eAAe;QAC1C;IACF;IACA,OAAO,MAAM,gBAAgB,QAAQ;AACvC;AACA,SAAS,yBAAyB,EAChC,QAAQ,EACR,MAAM,EACN,MAAM,EACN,SAAS,EACT,KAAK,EACN;IACC,SAAS,aAAa,KAAK;QACzB,IAAI,YAAY,SAAS,CAAC,MAAM;QAChC,IAAI,CAAC,WAAW;YACd,oBAAoB;YACpB,YAAY;QACd;QACA,OAAO;IACT;IACA,SAAS,YAAY,SAAS,EAAE,gBAAgB;QAC9C,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,QAAQ;QACzD,IAAI,WAAW;QACf,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,QAAQ;gBACZ,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,SAAS,CAAC,YAAY,EAAE,IAAI,SAAS,CAAC;oBACtC,WAAW,MAAM,GAAG,CAAC,CAAA,IAAK,OAAO,IAAI,IAAI,CAAC;gBAC5C,OAAO;oBACL,SAAS,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;oBACvB,WAAW,OAAO;gBACpB;gBACA,WAAW,SAAS,OAAO,CAAC,IAAI,OAAO,QAAQ,MAAM;YACvD;QACF;QAEA,8DAA8D;QAC9D,WAAW,SAAS,OAAO,CAAC,qBAAqB;QACjD,WAAW,CAAA,GAAA,6KAAA,CAAA,yBAAsB,AAAD,EAAE;QAClC,IAAI,SAAS,QAAQ,CAAC,MAAM;YAC1B,+EAA+E;YAC/E,MAAM,IAAI,MAAM,CAAC,gEAAgE,EAAE,SAAS,UAAU,EAAE,KAAK,SAAS,CAAC,SAAS;QAClI;QACA,IAAI,OAAO;YACT,YAAY,sBAAsB;QACpC;QACA,OAAO;IACT;IACA,IAAI,OAAO,aAAa,UAAU;QAChC,MAAM,YAAY,aAAa;QAC/B,MAAM,WAAW,YAAY,WAAW;QACxC,OAAO;IACT,OAAO;QACL,MAAM,EACJ,UAAU,gBAAgB,EAC1B,GAAG,MACJ,GAAG;QACJ,MAAM,YAAY,aAAa;QAC/B,MAAM,WAAW,YAAY,WAAW;QACxC,MAAM,SAAS;YACb,GAAG,IAAI;YACP,UAAU;QACZ;QACA,OAAO;IACT;AACF;AACA,SAAS,SAAS,MAAM,EAAE,QAAQ,EAAE,SAAS;IAC3C,MAAM,kBAAkB,CAAA,GAAA,6KAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,IAAI,CAAC;IACvD,MAAM,UAAU,UAAU;IAC1B,KAAK,MAAM,oBAAoB,gBAAiB;QAC9C,MAAM,+BAA+B,SAAS,CAAC,iBAAiB;QAChE,IAAI,OAAO,iCAAiC,UAAU;YACpD,MAAM,oBAAoB;YAC1B,IAAI,CAAA,GAAA,6KAAA,CAAA,kBAAe,AAAD,EAAE,mBAAmB,UAAU;gBAC/C,OAAO;YACT;QACF,OAAO;YACL,IAAI,CAAA,GAAA,6KAAA,CAAA,kBAAe,AAAD,EAAE,CAAA,GAAA,6KAAA,CAAA,uBAAoB,AAAD,EAAE,8BAA8B,QAAQ,mBAAmB,UAAU;gBAC1G,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,YAAY,QAAQ,EAAE,iBAAiB,OAAO,QAAQ,CAAC,QAAQ;IACtE,IAAI,aAAa,KAAK;QACpB,OAAO;IACT,OAAO;QACL,OAAO,eAAe,OAAO,CAAC,UAAU;IAC1C;AACF;AACA,SAAS,oBAAoB,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;IAC3D,MAAM,EACJ,IAAI,EACL,GAAG,QAAQ,YAAY;IACxB,IAAI;IACJ,IAAI,UAAU,WAAW;QACvB,eAAe;IACjB,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;QACtC,IAAI,SAAS,UAAU;YACrB,eAAe;QACjB,OAAO,IAAI,SAAS,aAAa;YAC/B,eAAe,QAAQ,OAAO,GAC9B,4DAA4D;YAC5D,sDAAsD;YACtD,CAAC,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,aAAa,KAAK,UAAU,WAAW,QAAQ,aAAa;QAC/F;IACF;IACA,OAAO,eAAe,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,GAAA,6KAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,QAAQ,YAAY,GAAG,YAAY;AAClG;AACA,SAAS,uBAAuB,MAAM;IACpC,IAAI,OAAO,YAAY,EAAE,SAAS,eAAe,CAAC,CAAC,mBAAmB,MAAM,GAAG;QAC7E,MAAM,IAAI,MAAM;IAClB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js"], "sourcesContent": ["import { getBasePath } from './utils.js';\n\n/**\n * We have to keep the cookie value in sync as Next.js might\n * skip a request to the server due to its router cache.\n * See https://github.com/amannn/next-intl/issues/786.\n */\nfunction syncLocaleCookie(localeCookie, pathname, locale, nextLocale) {\n  const isSwitchingLocale = nextLocale !== locale && nextLocale != null;\n  if (!localeCookie || !isSwitchingLocale ||\n  // Theoretical case, we always have a pathname in a real app,\n  // only not when running e.g. in a simulated test environment\n  !pathname) {\n    return;\n  }\n  const basePath = getBasePath(pathname);\n  const hasBasePath = basePath !== '';\n  const defaultPath = hasBasePath ? basePath : '/';\n  const {\n    name,\n    ...rest\n  } = localeCookie;\n  if (!rest.path) {\n    rest.path = defaultPath;\n  }\n  let localeCookieString = `${name}=${nextLocale};`;\n  for (const [key, value] of Object.entries(rest)) {\n    // Map object properties to cookie properties.\n    // Interestingly, `maxAge` corresponds to `max-age`,\n    // while `sameSite` corresponds to `SameSite`.\n    // Also, keys are case-insensitive.\n    const targetKey = key === 'maxAge' ? 'max-age' : key;\n    localeCookieString += `${targetKey}`;\n    if (typeof value !== 'boolean') {\n      localeCookieString += '=' + value;\n    }\n\n    // A trailing \";\" is allowed by browsers\n    localeCookieString += ';';\n  }\n\n  // Note that writing to `document.cookie` doesn't overwrite all\n  // cookies, but only the ones referenced via the name here.\n  document.cookie = localeCookieString;\n}\n\nexport { syncLocaleCookie as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;;CAIC,GACD,SAAS,iBAAiB,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU;IAClE,MAAM,oBAAoB,eAAe,UAAU,cAAc;IACjE,IAAI,CAAC,gBAAgB,CAAC,qBACtB,6DAA6D;IAC7D,6DAA6D;IAC7D,CAAC,UAAU;QACT;IACF;IACA,MAAM,WAAW,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM,cAAc,aAAa;IACjC,MAAM,cAAc,cAAc,WAAW;IAC7C,MAAM,EACJ,IAAI,EACJ,GAAG,MACJ,GAAG;IACJ,IAAI,CAAC,KAAK,IAAI,EAAE;QACd,KAAK,IAAI,GAAG;IACd;IACA,IAAI,qBAAqB,GAAG,KAAK,CAAC,EAAE,WAAW,CAAC,CAAC;IACjD,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,MAAO;QAC/C,8CAA8C;QAC9C,oDAAoD;QACpD,8CAA8C;QAC9C,mCAAmC;QACnC,MAAM,YAAY,QAAQ,WAAW,YAAY;QACjD,sBAAsB,GAAG,WAAW;QACpC,IAAI,OAAO,UAAU,WAAW;YAC9B,sBAAsB,MAAM;QAC9B;QAEA,wCAAwC;QACxC,sBAAsB;IACxB;IAEA,+DAA+D;IAC/D,2DAA2D;IAC3D,SAAS,MAAM,GAAG;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js"], "sourcesContent": ["\"use client\";\nimport NextLink from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { forwardRef } from 'react';\nimport { useLocale } from 'use-intl';\nimport syncLocaleCookie from './syncLocaleCookie.js';\nimport { jsx } from 'react/jsx-runtime';\n\nfunction BaseLink({\n  href,\n  locale,\n  localeCookie,\n  onClick,\n  prefetch,\n  ...rest\n}, ref) {\n  const curLocale = useLocale();\n  const isChangingLocale = locale != null && locale !== curLocale;\n\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n  const pathname = usePathname();\n  function onLinkClick(event) {\n    syncLocaleCookie(localeCookie, pathname, curLocale, locale);\n    if (onClick) onClick(event);\n  }\n  if (isChangingLocale) {\n    if (prefetch && \"development\" !== 'production') {\n      console.error('The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`');\n    }\n    prefetch = false;\n  }\n\n  // Somehow the types for `next/link` don't work as expected\n  // when `moduleResolution: \"nodenext\"` is used.\n  const Link = NextLink;\n  return /*#__PURE__*/jsx(Link, {\n    ref: ref,\n    href: href,\n    hrefLang: isChangingLocale ? locale : undefined,\n    onClick: onLinkClick,\n    prefetch: prefetch,\n    ...rest\n  });\n}\nvar BaseLink$1 = /*#__PURE__*/forwardRef(BaseLink);\n\nexport { BaseLink$1 as default };\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,SAAS,SAAS,EAChB,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,OAAO,EACP,QAAQ,EACR,GAAG,MACJ,EAAE,GAAG;IACJ,MAAM,YAAY,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IAC1B,MAAM,mBAAmB,UAAU,QAAQ,WAAW;IAEtD,6DAA6D;IAC7D,4DAA4D;IAC5D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,SAAS,YAAY,KAAK;QACxB,CAAA,GAAA,sMAAA,CAAA,UAAgB,AAAD,EAAE,cAAc,UAAU,WAAW;QACpD,IAAI,SAAS,QAAQ;IACvB;IACA,IAAI,kBAAkB;QACpB,IAAI,YAAY,kBAAkB,cAAc;YAC9C,QAAQ,KAAK,CAAC;QAChB;QACA,WAAW;IACb;IAEA,2DAA2D;IAC3D,+CAA+C;IAC/C,MAAM,OAAO,4JAAA,CAAA,UAAQ;IACrB,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,MAAM;QAC5B,KAAK;QACL,MAAM;QACN,UAAU,mBAAmB,SAAS;QACtC,SAAS;QACT,UAAU;QACV,GAAG,IAAI;IACT;AACF;AACA,IAAI,aAAa,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js"], "sourcesContent": ["import { redirect, permanentRedirect } from 'next/navigation';\nimport { forwardRef } from 'react';\nimport { receiveRoutingConfig } from '../../routing/config.js';\nimport use from '../../shared/use.js';\nimport { isLocalizableHref, isPromise } from '../../shared/utils.js';\nimport BaseLink from './BaseLink.js';\nimport { validateReceivedConfig, serializeSearchParams, compileLocalizedPathname, applyPathnamePrefix, normalizeNameOrNameWithParams } from './utils.js';\nimport { jsx } from 'react/jsx-runtime';\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config = receiveRoutingConfig(routing || {});\n  {\n    validateReceivedConfig(config);\n  }\n  const pathnames = config.pathnames;\n  function Link({\n    href,\n    locale,\n    ...rest\n  }, ref) {\n    let pathname, params;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = isLocalizableHref(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = isPromise(localePromiseOrValue) ? use(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname({\n      locale: locale || curLocale,\n      // @ts-expect-error -- This is ok\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      },\n      // Always include a prefix when changing locales\n      forcePrefix: locale != null || undefined\n    }) : pathname;\n    return /*#__PURE__*/jsx(BaseLink, {\n      ref: ref\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config.localeCookie,\n      ...rest\n    });\n  }\n  const LinkWithRef = /*#__PURE__*/forwardRef(Link);\n  function getPathname(args) {\n    const {\n      forcePrefix,\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += serializeSearchParams(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = compileLocalizedPathname({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...normalizeNameOrNameWithParams(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config.pathnames\n      });\n    }\n    return applyPathnamePrefix(pathname, locale, config, forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args, ...rest) {\n      return fn(getPathname(args), ...rest);\n    };\n  }\n  const redirect$1 = getRedirectFn(redirect);\n  const permanentRedirect$1 = getRedirectFn(permanentRedirect);\n  return {\n    config,\n    Link: LinkWithRef,\n    redirect: redirect$1,\n    permanentRedirect: permanentRedirect$1,\n    getPathname\n  };\n}\n\nexport { createSharedNavigationFns as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA;;CAEC,GACD,SAAS,0BAA0B,SAAS,EAAE,OAAO;IACnD,MAAM,SAAS,CAAA,GAAA,+KAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,CAAC;IAChD;QACE,CAAA,GAAA,2LAAA,CAAA,yBAAsB,AAAD,EAAE;IACzB;IACA,MAAM,YAAY,OAAO,SAAS;IAClC,SAAS,KAAK,EACZ,IAAI,EACJ,MAAM,EACN,GAAG,MACJ,EAAE,GAAG;QACJ,IAAI,UAAU;QACd,IAAI,OAAO,SAAS,UAAU;YAC5B,WAAW,KAAK,QAAQ;YACxB,iCAAiC;YACjC,SAAS,KAAK,MAAM;QACtB,OAAO;YACL,WAAW;QACb;QAEA,iCAAiC;QACjC,MAAM,gBAAgB,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE;QACxC,MAAM,uBAAuB;QAC7B,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,wBAAwB,CAAA,GAAA,2KAAA,CAAA,UAAG,AAAD,EAAE,wBAAwB;QAChF,MAAM,gBAAgB,gBAAgB,YAAY;YAChD,QAAQ,UAAU;YAClB,iCAAiC;YACjC,MAAM,aAAa,OAAO,WAAW;gBACnC;gBACA;YACF;YACA,gDAAgD;YAChD,aAAa,UAAU,QAAQ;QACjC,KAAK;QACL,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,8LAAA,CAAA,UAAQ,EAAE;YAChC,KAAK;YAGL,MAAM,OAAO,SAAS,WAAW;gBAC/B,GAAG,IAAI;gBACP,UAAU;YACZ,IAAI;YACJ,QAAQ;YACR,cAAc,OAAO,YAAY;YACjC,GAAG,IAAI;QACT;IACF;IACA,MAAM,cAAc,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC5C,SAAS,YAAY,IAAI;QACvB,MAAM,EACJ,WAAW,EACX,IAAI,EACJ,MAAM,EACP,GAAG;QACJ,IAAI;QACJ,IAAI,aAAa,MAAM;YACrB,IAAI,OAAO,SAAS,UAAU;gBAC5B,WAAW,KAAK,QAAQ;gBACxB,IAAI,KAAK,KAAK,EAAE;oBACd,YAAY,CAAA,GAAA,2LAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,KAAK;gBAC9C;YACF,OAAO;gBACL,WAAW;YACb;QACF,OAAO;YACL,WAAW,CAAA,GAAA,2LAAA,CAAA,2BAAwB,AAAD,EAAE;gBAClC;gBACA,iCAAiC;gBACjC,GAAG,CAAA,GAAA,2LAAA,CAAA,gCAA6B,AAAD,EAAE,KAAK;gBACtC,iCAAiC;gBACjC,WAAW,OAAO,SAAS;YAC7B;QACF;QACA,OAAO,CAAA,GAAA,2LAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,QAAQ,QAAQ;IACvD;IACA,SAAS,cAAc,EAAE;QACvB,gEAAgE,GAChE,OAAO,SAAS,WAAW,IAAI,EAAE,GAAG,IAAI;YACtC,OAAO,GAAG,YAAY,UAAU;QAClC;IACF;IACA,MAAM,aAAa,cAAc,kIAAA,CAAA,WAAQ;IACzC,MAAM,sBAAsB,cAAc,kIAAA,CAAA,oBAAiB;IAC3D,OAAO;QACL;QACA,MAAM;QACN,UAAU;QACV,mBAAmB;QACnB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js"], "sourcesContent": ["import { usePathname } from 'next/navigation';\nimport { useMemo } from 'react';\nimport { useLocale } from 'use-intl';\nimport { hasPathnamePrefixed, unprefixPathname, getLocalePrefix, getLocaleAsPrefix } from '../../shared/utils.js';\n\nfunction useBasePathname(config) {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n\n  // Notes on `useNextPathname`:\n  // - Types aren't entirely correct. Outside of Next.js the\n  //   hook will return `null` (e.g. unit tests)\n  // - A base path is stripped from the result\n  // - Rewrites *are* taken into account (i.e. the pathname\n  //   that the user sees in the browser is returned)\n  const pathname = usePathname();\n  const locale = useLocale();\n  return useMemo(() => {\n    if (!pathname) return pathname;\n    let unlocalizedPathname = pathname;\n    const prefix = getLocalePrefix(locale, config.localePrefix);\n    const isPathnamePrefixed = hasPathnamePrefixed(prefix, pathname);\n    if (isPathnamePrefixed) {\n      unlocalizedPathname = unprefixPathname(pathname, prefix);\n    } else if (config.localePrefix.mode === 'as-needed' && config.localePrefix.prefixes) {\n      // Workaround for https://github.com/vercel/next.js/issues/73085\n      const localeAsPrefix = getLocaleAsPrefix(locale);\n      if (hasPathnamePrefixed(localeAsPrefix, pathname)) {\n        unlocalizedPathname = unprefixPathname(pathname, localeAsPrefix);\n      }\n    }\n    return unlocalizedPathname;\n  }, [config.localePrefix, locale, pathname]);\n}\n\nexport { useBasePathname as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,SAAS,gBAAgB,MAAM;IAC7B,6DAA6D;IAC7D,4DAA4D;IAE5D,8BAA8B;IAC9B,0DAA0D;IAC1D,8CAA8C;IAC9C,4CAA4C;IAC5C,yDAAyD;IACzD,mDAAmD;IACnD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IACvB,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,sBAAsB;QAC1B,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,OAAO,YAAY;QAC1D,MAAM,qBAAqB,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ;QACvD,IAAI,oBAAoB;YACtB,sBAAsB,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;QACnD,OAAO,IAAI,OAAO,YAAY,CAAC,IAAI,KAAK,eAAe,OAAO,YAAY,CAAC,QAAQ,EAAE;YACnF,gEAAgE;YAChE,MAAM,iBAAiB,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE;YACzC,IAAI,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB,WAAW;gBACjD,sBAAsB,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;YACnD;QACF;QACA,OAAO;IACT,GAAG;QAAC,OAAO,YAAY;QAAE;QAAQ;KAAS;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js"], "sourcesContent": ["import { useRouter, usePathname } from 'next/navigation';\nimport { useMemo } from 'react';\nimport { useLocale } from 'use-intl';\nimport createSharedNavigationFns from '../shared/createSharedNavigationFns.js';\nimport syncLocaleCookie from '../shared/syncLocaleCookie.js';\nimport { getRoute } from '../shared/utils.js';\nimport useBasePathname from './useBasePathname.js';\n\nfunction createNavigation(routing) {\n  const {\n    Link,\n    config,\n    getPathname,\n    ...redirects\n  } = createSharedNavigationFns(useLocale, routing);\n\n  /** @see https://next-intl.dev/docs/routing/navigation#usepathname */\n  function usePathname$1() {\n    const pathname = useBasePathname(config);\n    const locale = useLocale();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return useMemo(() => pathname &&\n    // @ts-expect-error -- This is fine\n    config.pathnames ? getRoute(locale, pathname,\n    // @ts-expect-error -- This is fine\n    config.pathnames) : pathname, [locale, pathname]);\n  }\n  function useRouter$1() {\n    const router = useRouter();\n    const curLocale = useLocale();\n    const nextPathname = usePathname();\n    return useMemo(() => {\n      function createHandler(fn) {\n        return function handler(href, options) {\n          const {\n            locale: nextLocale,\n            ...rest\n          } = options || {};\n          const pathname = getPathname({\n            href,\n            locale: nextLocale || curLocale\n          });\n          const args = [pathname];\n          if (Object.keys(rest).length > 0) {\n            // @ts-expect-error -- This is fine\n            args.push(rest);\n          }\n          fn(...args);\n          syncLocaleCookie(config.localeCookie, nextPathname, curLocale, nextLocale);\n        };\n      }\n      return {\n        ...router,\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        push: createHandler(router.push),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        replace: createHandler(router.replace),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        prefetch: createHandler(router.prefetch)\n      };\n    }, [curLocale, nextPathname, router]);\n  }\n  return {\n    ...redirects,\n    Link,\n    usePathname: usePathname$1,\n    useRouter: useRouter$1,\n    getPathname\n  };\n}\n\nexport { createNavigation as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,SAAS,iBAAiB,OAAO;IAC/B,MAAM,EACJ,IAAI,EACJ,MAAM,EACN,WAAW,EACX,GAAG,WACJ,GAAG,CAAA,GAAA,+MAAA,CAAA,UAAyB,AAAD,EAAE,kKAAA,CAAA,YAAS,EAAE;IAEzC,mEAAmE,GACnE,SAAS;QACP,MAAM,WAAW,CAAA,GAAA,8MAAA,CAAA,UAAe,AAAD,EAAE;QACjC,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;QAEvB,sLAAsL;QACtL,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,YACrB,mCAAmC;YACnC,OAAO,SAAS,GAAG,CAAA,GAAA,2LAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,UACpC,mCAAmC;YACnC,OAAO,SAAS,IAAI,UAAU;YAAC;YAAQ;SAAS;IAClD;IACA,SAAS;QACP,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;QACvB,MAAM,YAAY,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;QAC1B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;QAC/B,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;YACb,SAAS,cAAc,EAAE;gBACvB,OAAO,SAAS,QAAQ,IAAI,EAAE,OAAO;oBACnC,MAAM,EACJ,QAAQ,UAAU,EAClB,GAAG,MACJ,GAAG,WAAW,CAAC;oBAChB,MAAM,WAAW,YAAY;wBAC3B;wBACA,QAAQ,cAAc;oBACxB;oBACA,MAAM,OAAO;wBAAC;qBAAS;oBACvB,IAAI,OAAO,IAAI,CAAC,MAAM,MAAM,GAAG,GAAG;wBAChC,mCAAmC;wBACnC,KAAK,IAAI,CAAC;oBACZ;oBACA,MAAM;oBACN,CAAA,GAAA,sMAAA,CAAA,UAAgB,AAAD,EAAE,OAAO,YAAY,EAAE,cAAc,WAAW;gBACjE;YACF;YACA,OAAO;gBACL,GAAG,MAAM;gBACT,iEAAiE,GACjE,MAAM,cAAc,OAAO,IAAI;gBAC/B,iEAAiE,GACjE,SAAS,cAAc,OAAO,OAAO;gBACrC,iEAAiE,GACjE,UAAU,cAAc,OAAO,QAAQ;YACzC;QACF,GAAG;YAAC;YAAW;YAAc;SAAO;IACtC;IACA,OAAO;QACL,GAAG,SAAS;QACZ;QACA,aAAa;QACb,WAAW;QACX;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/routing/defineRouting.js"], "sourcesContent": ["function defineRouting(config) {\n  if (config.domains) {\n    validateUniqueLocalesPerDomain(config.domains);\n  }\n  return config;\n}\nfunction validateUniqueLocalesPerDomain(domains) {\n  const domainsByLocale = new Map();\n  for (const {\n    domain,\n    locales\n  } of domains) {\n    for (const locale of locales) {\n      const localeDomains = domainsByLocale.get(locale) || new Set();\n      localeDomains.add(domain);\n      domainsByLocale.set(locale, localeDomains);\n    }\n  }\n  const duplicateLocaleMessages = Array.from(domainsByLocale.entries()).filter(([, localeDomains]) => localeDomains.size > 1).map(([locale, localeDomains]) => `- \"${locale}\" is used by: ${Array.from(localeDomains).join(', ')}`);\n  if (duplicateLocaleMessages.length > 0) {\n    console.warn('Locales are expected to be unique per domain, but found overlap:\\n' + duplicateLocaleMessages.join('\\n') + '\\nPlease see https://next-intl.dev/docs/routing#domains');\n  }\n}\n\nexport { defineRouting as default };\n"], "names": [], "mappings": ";;;AAAA,SAAS,cAAc,MAAM;IAC3B,IAAI,OAAO,OAAO,EAAE;QAClB,+BAA+B,OAAO,OAAO;IAC/C;IACA,OAAO;AACT;AACA,SAAS,+BAA+B,OAAO;IAC7C,MAAM,kBAAkB,IAAI;IAC5B,KAAK,MAAM,EACT,MAAM,EACN,OAAO,EACR,IAAI,QAAS;QACZ,KAAK,MAAM,UAAU,QAAS;YAC5B,MAAM,gBAAgB,gBAAgB,GAAG,CAAC,WAAW,IAAI;YACzD,cAAc,GAAG,CAAC;YAClB,gBAAgB,GAAG,CAAC,QAAQ;QAC9B;IACF;IACA,MAAM,0BAA0B,MAAM,IAAI,CAAC,gBAAgB,OAAO,IAAI,MAAM,CAAC,CAAC,GAAG,cAAc,GAAK,cAAc,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,cAAc,GAAK,CAAC,GAAG,EAAE,OAAO,cAAc,EAAE,MAAM,IAAI,CAAC,eAAe,IAAI,CAAC,OAAO;IAChO,IAAI,wBAAwB,MAAM,GAAG,GAAG;QACtC,QAAQ,IAAI,CAAC,uEAAuE,wBAAwB,IAAI,CAAC,QAAQ;IAC3H;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/react-client/index.js"], "sourcesContent": ["import { useFormatter as useFormatter$1, useTranslations as useTranslations$1 } from 'use-intl';\nexport * from 'use-intl';\n\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */\n\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n  return (...args) => {\n    try {\n      return hook(...args);\n    } catch {\n      throw new Error(`Failed to call \\`${name}\\` because the context from \\`NextIntlClientProvider\\` was not found.\n\nThis can happen because:\n1) You intended to render this component as a Server Component, the render\n   failed, and therefore <PERSON><PERSON> attempted to render the component on the client\n   instead. If this is the case, check the console for server errors.\n2) You intended to render this component on the client side, but no context was found.\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context` );\n    }\n  };\n}\nconst useTranslations = callHook('useTranslations', useTranslations$1);\nconst useFormatter = callHook('useFormatter', useFormatter$1);\n\nexport { useFormatter, useTranslations };\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA;;;;;;;;CAQC,GAGD,sEAAsE;AACtE,SAAS,SAAS,IAAI,EAAE,IAAI;IAC1B,OAAO,CAAC,GAAG;QACT,IAAI;YACF,OAAO,QAAQ;QACjB,EAAE,OAAM;YACN,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK;;;;;;;qHAOsE,CAAC;QAClH;IACF;AACF;AACA,MAAM,kBAAkB,SAAS,mBAAmB,kKAAA,CAAA,kBAAiB;AACrE,MAAM,eAAe,SAAS,gBAAgB,kKAAA,CAAA,eAAc", "ignoreList": [0], "debugId": null}}]}