'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { LocaleSwitcher } from '@/components/LocaleSwitcher';
import { AppConfig } from '@/utils/AppConfig';

export const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const t = useTranslations('RootLayout');

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  return (
    <nav className="bg-white/95 backdrop-blur-sm border-b border-secondary-200 sticky top-0 z-50">
      <div className="container-wide">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">A</span>
              </div>
              <span className="text-xl font-bold text-secondary-900">
                {AppConfig.name}
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-8">
              <Link
                href="/"
                className="text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
              >
                {t('home_link')}
              </Link>
              <Link
                href="/features/"
                className="text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
              >
                {t('features_link')}
              </Link>
              <Link
                href="/pricing/"
                className="text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
              >
                {t('pricing_link')}
              </Link>
              <Link
                href="/about/"
                className="text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
              >
                {t('about_link')}
              </Link>
              <Link
                href="/blog/"
                className="text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
              >
                {t('blog_link')}
              </Link>
              <Link
                href="/contact/"
                className="text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
              >
                {t('contact_link')}
              </Link>
            </div>
          </div>

          {/* Desktop Right Side */}
          <div className="hidden md:flex items-center space-x-4">
            <LocaleSwitcher />
            <Link
              href="/sign-in/"
              className="text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
            >
              {t('sign_in_link')}
            </Link>
            <Link
              href="/sign-up/"
              className="btn-primary text-sm"
            >
              {t('sign_up_link')}
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-secondary-700 hover:text-primary-600 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              {!isMenuOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-secondary-200">
              <Link
                href="/"
                className="text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('home_link')}
              </Link>
              <Link
                href="/features/"
                className="text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('features_link')}
              </Link>
              <Link
                href="/pricing/"
                className="text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('pricing_link')}
              </Link>
              <Link
                href="/about/"
                className="text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('about_link')}
              </Link>
              <Link
                href="/blog/"
                className="text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('blog_link')}
              </Link>
              <Link
                href="/contact/"
                className="text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('contact_link')}
              </Link>
              <div className="border-t border-secondary-200 pt-4">
                <div className="flex items-center px-3 space-x-3">
                  <LocaleSwitcher />
                </div>
                <Link
                  href="/sign-in/"
                  className="text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {t('sign_in_link')}
                </Link>
                <Link
                  href="/sign-up/"
                  className="btn-primary block mx-3 mt-2 text-center"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {t('sign_up_link')}
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};
