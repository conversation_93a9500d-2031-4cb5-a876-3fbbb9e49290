self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7f4a02af96e1bb6aa73a8043a67d03e71bf5a0b1fc\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page\": \"action-browser\"\n      }\n    },\n    \"7f77a91b277141486faa446cc5de3827f9d835b4e4\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page\": \"action-browser\"\n      }\n    },\n    \"7ff46c643d4772dc8ee990c6d15e55cd365348ec79\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page\": \"action-browser\"\n      }\n    },\n    \"7f004e23488fbd4b4c13c8ce70e3fc1b5cfb4136dd\": {\n      \"workers\": {\n        \"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"XjGYJUiSdTJvDZZBTSTKjBIFXTdH8hZFqDZiLcWR8Ao=\"\n}"