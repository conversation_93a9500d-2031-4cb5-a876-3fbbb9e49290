const CHUNK_PUBLIC_PATH = "server/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page.js";
const runtime = require("../../../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_20decf41._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__292a8cba._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/src_app_global-error_tsx_d6ef94b5._.js");
runtime.loadChunk("server/chunks/ssr/src_locales_0ef6aa65._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_1aaf2150._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@clerk_localizations_dist_index_mjs_4da605b8._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_04c073e2._.js");
runtime.loadChunk("server/chunks/ssr/src_d8577361._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_6c61f139._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_41dd3cf0._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__6daf9afa._.js");
runtime.loadChunk("server/chunks/ssr/src_app_[locale]_(auth)_(center)_layout_tsx_3836434e._.js");
runtime.loadChunk("server/chunks/ssr/_b41a2b86._.js");
runtime.loadChunk("server/chunks/ssr/src_295d1884._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page { GLOBAL_ERROR_MODULE => \"[project]/src/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_0 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/src/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/[locale]/(auth)/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/src/app/[locale]/(auth)/(center)/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/src/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page { GLOBAL_ERROR_MODULE => \"[project]/src/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_0 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/src/app/global-error.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/[locale]/(auth)/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/src/app/[locale]/(auth)/(center)/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/src/app/[locale]/(auth)/(center)/sign-in/[[...sign-in]]/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
