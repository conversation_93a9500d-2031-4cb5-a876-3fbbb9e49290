"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileLoader = void 0;
class FileLoader {
    loader;
    cache = new Map();
    constructor(loader) {
        this.loader = loader;
    }
    async load(filePath) {
        if (this.cache.has(filePath)) {
            return this.cache.get(filePath);
        }
        const file = await this.loader(filePath);
        this.cache.set(filePath, file);
        return file;
    }
}
exports.FileLoader = FileLoader;
//# sourceMappingURL=loader.js.map