"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/hu-HU.ts
var hu_HU_exports = {};
__export(hu_HU_exports, {
  huHU: () => huHU
});
module.exports = __toCommonJS(hu_HU_exports);
var huHU = {
  locale: "hu-HU",
  backButton: "Vissza",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Alap\xE9rtelmezett",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "M\xE1sik megszem\xE9lyes\xEDt\u0151 eszk\xF6z",
  badge__primary: "Els\u0151dleges",
  badge__renewsAt: void 0,
  badge__requiresAction: "Beavatkoz\xE1s sz\xFCks\xE9ges",
  badge__startsAt: void 0,
  badge__thisDevice: "Ez az eszk\xF6z",
  badge__unverified: "Nem ellen\u0151rz\xF6tt",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Felhaszn\xE1l\xF3i eszk\xF6z",
  badge__you: "Te",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Szervezet l\xE9trehoz\xE1sa",
    invitePage: {
      formButtonReset: "Kihagy\xE1s"
    },
    title: "Szervezet l\xE9trehoz\xE1sa"
  },
  dates: {
    lastDay: "Tegnap {{ date | timeString('hu-HU') }}-kor",
    next6Days: "{{ date | weekday('hu-HU','long') }} {{ date | timeString('hu-HU') }}-kor",
    nextDay: "Holnap {{ date | timeString('hu-HU') }}-kor",
    numeric: "{{ date | timeString('hu-HU') }}",
    previous6Days: "Elm\xFAlt {{ date | weekday('hu-HU','long') }} {{ date | timeString('hu-HU') }}-kor",
    sameDay: "Ma {{ date | timeString('hu-HU') }}-kor"
  },
  dividerText: "vagy",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "M\xE1sik m\xF3dszer haszn\xE1lata",
  footerPageLink__help: "S\xFAg\xF3",
  footerPageLink__privacy: "Adatv\xE9delem",
  footerPageLink__terms: "Felhaszn\xE1l\xE1si felt\xE9telek",
  formButtonPrimary: "Tov\xE1bb",
  formButtonPrimary__verify: "Ellen\u0151rz\xE9s",
  formFieldAction__forgotPassword: "Elfelejtetted a jelszavad?",
  formFieldError__matchingPasswords: "A jelszavak megegyeznek",
  formFieldError__notMatchingPasswords: "A jelszavak nem egyeznek",
  formFieldError__verificationLinkExpired: "A meger\u0151s\xEDt\u0151 link lej\xE1rt. K\xE9rlek k\xE9rj egy \xFAjat.",
  formFieldHintText__optional: "Nem k\xF6telez\u0151",
  formFieldHintText__slug: "A slug egy egyedi azonos\xEDt\xF3, amelyet \xE1ltal\xE1ban URL-ben haszn\xE1lunk.",
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Fi\xF3k t\xF6rl\xE9se",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "<EMAIL>, <EMAIL>",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: "my-org",
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__automaticInvitations: "Automatikus megh\xEDv\xE1sok enged\xE9lyez\xE9se ezen a domainen",
  formFieldLabel__backupCode: "Tartal\xE9k k\xF3d",
  formFieldLabel__confirmDeletion: "Meger\u0151s\xEDt\xE9s",
  formFieldLabel__confirmPassword: "Jelsz\xF3 meger\u0151s\xEDt\xE9se",
  formFieldLabel__currentPassword: "Jelenlegi jelsz\xF3",
  formFieldLabel__emailAddress: "Email c\xEDm",
  formFieldLabel__emailAddress_username: "Email c\xEDm vagy felhaszn\xE1l\xF3n\xE9v",
  formFieldLabel__emailAddresses: "Email c\xEDmek",
  formFieldLabel__firstName: "Keresztn\xE9v",
  formFieldLabel__lastName: "Vezet\xE9kn\xE9v",
  formFieldLabel__newPassword: "\xDAj jelsz\xF3",
  formFieldLabel__organizationDomain: "Domain",
  formFieldLabel__organizationDomainDeletePending: "F\xFCgg\u0151ben l\xE9v\u0151 megh\xEDv\xE1sok \xE9s javaslatok t\xF6rl\xE9se",
  formFieldLabel__organizationDomainEmailAddress: "Visszaigazol\xF3 email c\xEDm",
  formFieldLabel__organizationDomainEmailAddressDescription: "\xCDrj be egy email c\xEDmet ez alatt a domain alatt, hogy visszaigazold a domaint.",
  formFieldLabel__organizationName: "Szervezet neve",
  formFieldLabel__organizationSlug: "Slug",
  formFieldLabel__passkeyName: "Passkey neve",
  formFieldLabel__password: "Jelsz\xF3",
  formFieldLabel__phoneNumber: "Telefonsz\xE1m",
  formFieldLabel__role: "Beoszt\xE1s",
  formFieldLabel__signOutOfOtherSessions: "Kijelentkeztet\xE9s minden m\xE1s eszk\xF6z\xF6kr\u0151l",
  formFieldLabel__username: "Felhaszn\xE1l\xF3n\xE9v",
  impersonationFab: {
    action__signOut: "Kijelentkez\xE9s",
    title: "Bejelntkezve mint {{identifier}}"
  },
  maintenanceMode: "Jelenleg karbantart\xE1s alatt \xE1llunk, de ne agg\xF3dj, ez nem tart tov\xE1bb p\xE1r percn\xE9l!",
  membershipRole__admin: "Adminisztr\xE1tor",
  membershipRole__basicMember: "Tag",
  membershipRole__guestMember: "Vend\xE9g",
  organizationList: {
    action__createOrganization: "Szervezet l\xE9trehoz\xE1sa",
    action__invitationAccept: "Csatlakoz\xE1s",
    action__suggestionsAccept: "Csatlakoz\xE1s k\xE9r\xE9se",
    createOrganization: "Szervezet l\xE9trehoz\xE1sa",
    invitationAcceptedLabel: "Megh\xEDv\xE1s elfogadva",
    subtitle: "Amivel folytathatod a(z) {{applicationName}}",
    suggestionsAcceptedLabel: "Elfogad\xE1sra v\xE1r",
    title: "V\xE1lassz egy fi\xF3kot",
    titleWithoutPersonal: "V\xE1lassz egy szervezetet"
  },
  organizationProfile: {
    badge__automaticInvitation: "Automatikus megh\xEDv\xE1sok",
    badge__automaticSuggestion: "Automatikus javaslatok",
    badge__manualInvitation: "Nincs automatikus felv\xE9tel",
    badge__unverified: "Nincs visszaigazolva",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Add meg a visszaigazoland\xF3 domain nev\xE9t. Minden email c\xEDm err\u0151l a tartom\xE1nyj\xF3l automatikusan tud csatlakozni a szervezethez.",
      title: "Domain hozz\xE1ad\xE1sa"
    },
    invitePage: {
      detailsTitle__inviteFailed: "A megh\xEDvok k\xFCld\xE9se sikertelen. M\xE1r vannak f\xFCgg\u0151ben l\xE9v\u0151 megh\xEDv\xF3k a k\xF6vetkez\u0151 c\xEDmekre: {{email_addresses}}",
      formButtonPrimary__continue: "Megh\xEDv\xF3k k\xFCld\xE9se",
      selectDropdown__role: "V\xE1lassz beoszt\xE1st",
      subtitle: "\xCDrj be vagy illessz be egy vagy t\xF6bb email c\xEDmet, vessz\u0151vel, vagy sz\xF3k\xF6zzel elv\xE1lasztva.",
      successMessage: "A megh\xEDv\xF3k sikeresen elk\xFCldve",
      title: "\xDAj tagok megh\xEDv\xE1sa"
    },
    membersPage: {
      action__invite: "Megh\xEDv\xE1s",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Tag elt\xE1vol\xEDt\xE1sa",
        tableHeader__actions: void 0,
        tableHeader__joined: "Csatlakozott",
        tableHeader__role: "Beoszt\xE1s",
        tableHeader__user: "Felhaszn\xE1l\xF3"
      },
      detailsTitle__emptyRow: "Nincsenek list\xE1zhat\xF3 tagok",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Adj meg egy email domaint, hogy megh\xEDvhass tagokat. B\xE1rki aki err\u0151l a tartom\xE1nyr\xF3l regisztr\xE1l, b\xE1rmikor, csatlakozhat a szervezethez b\xE1rmikor",
          headerTitle: "Automatikus megh\xEDv\xE1sok",
          primaryButton: "Visszaigazolt domainek kezel\xE9se"
        },
        table__emptyRow: "Nincsenek list\xE1zhat\xF3 megh\xEDv\xE1sok"
      },
      invitedMembersTab: {
        menuAction__revoke: "Megh\xEDv\xF3 visszavon\xE1sa",
        tableHeader__invited: "Megh\xEDvva"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Azok a felhaszn\xE1l\xF3k, akik, egyez\u0151 email domainnel regisztr\xE1lnak, l\xE1tni fognak egy javaslatot, hogy csatlakozzanak a szervezetedhez.",
          headerTitle: "Automatikus javaslat",
          primaryButton: "Visszaigazolt domainek kezel\xE9se"
        },
        menuAction__approve: "Elfogad\xE1s",
        menuAction__reject: "Elutas\xEDt\xE1s",
        tableHeader__requested: "Hozz\xE1f\xE9r\xE9s k\xE9r\xE9se",
        table__emptyRow: "Nincsenek list\xE1zhat\xF3 k\xE9r\xE9sek"
      },
      start: {
        headerTitle__invitations: "Megh\xEDv\xF3k",
        headerTitle__members: "Tagok",
        headerTitle__requests: "K\xE9r\xE9sek"
      }
    },
    navbar: {
      billing: void 0,
      description: "A szervezeted kezel\xE9se",
      general: "\xC1ltal\xE1nos",
      members: "Tagok",
      title: "Szervezet"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: '\xCDrd be, hogy: "{{organizationName}}" a folytat\xE1shoz',
          messageLine1: "Biztosan t\xF6r\xF6lni szeretn\xE9d ez a szervezetet?",
          messageLine2: "Ez a m\u0171velet v\xE9gleges \xE9s visszaford\xEDthatatlan.",
          successMessage: "Kit\xF6r\xF6lted a szervezetet.",
          title: "Szervezet t\xF6rl\xE9se"
        },
        leaveOrganization: {
          actionDescription: '\xCDrd be, hogy: "{{organizationName}}" a folytat\xE1shoz',
          messageLine1: "Biztos vagy benne, hogy el szeretn\xE9d hagyni a szervezetet? Elvesz\xEDted a hozz\xE1f\xE9r\xE9st a szervezethez, \xE9s alkamaz\xE1saihoz.",
          messageLine2: "Ez a m\u0171velet v\xE9gleges \xE9s visszaford\xEDthatatlan.",
          successMessage: "Elhagytad a szervezetet.",
          title: "Szervezet elhagy\xE1sa"
        },
        title: "Vesz\xE9ly"
      },
      domainSection: {
        menuAction__manage: "Kezel\xE9s",
        menuAction__remove: "T\xF6rl\xE9s",
        menuAction__verify: "Visszaigazol\xE1s",
        primaryButton: "Domain hozz\xE1ad\xE1sa",
        subtitle: "Endeg\xE9lyezd, hogy a felhaszn\xE1l\xF3k automatikusan csatlakozhassanak a szervezetedhez, vagy hozz\xE1f\xE9r\xE9st k\xE9rjenek, az email domainj\xFCk alapj\xE1n.",
        title: "Visszaigazolt domainek"
      },
      successMessage: "A szervezet friss\xEDtve",
      title: "Profil friss\xEDt\xE9se"
    },
    removeDomainPage: {
      messageLine1: "Az email domain {{domain}} el lesz t\xE1vol\xEDtva",
      messageLine2: "Ez ut\xE1n a felhaszn\xE1l\xF3k nem tudnak automatikusan csatlakozni",
      successMessage: "{{domain}} t\xF6r\xF6lve lett",
      title: "Domain t\xF6rl\xE9se"
    },
    start: {
      headerTitle__general: "\xC1ltal\xE1nos",
      headerTitle__members: "Tagok",
      profileSection: {
        primaryButton: "Profil friss\xEDt\xE9se",
        title: "Szervezet Profil",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "A domain t\xF6rl\xE9se befoly\xE1solja a megh\xEDvott felhaszn\xE1l\xF3kat",
        removeDomainActionLabel__remove: "Domain t\xF6rl\xE9se",
        removeDomainSubtitle: "Domain t\xF6rl\xE9se a visszaigazolt domainek list\xE1j\xE1r\xF3l",
        removeDomainTitle: "Domain t\xF6rl\xE9se"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "A felhaszn\xE1l\xF3k automatikusan meg lesznek h\xEDvva a szervezetbe, amikor regisztr\xE1lnak, \xE9s b\xE1rmikor csatlakozhatnak.",
        automaticInvitationOption__label: "Automatikus megh\xEDv\xE1sok",
        automaticSuggestionOption__description: "A felaszn\xE1l\xF3k kapnak egy javaslatot, hogy k\xE9rjenek hozz\xE1f\xE9r\xE9st, de el\u0151bb egy adminisztr\xE1tornak j\xF3v\xE1 kell hagynia, miel\u0151tt csatlakozhatnak.",
        automaticSuggestionOption__label: "Automatikus javaslatok",
        calloutInfoLabel: "A felv\xE9teli m\xF3d megv\xE1ltoztat\xE1sa csak az \xFAj felhaszn\xE1l\xF3kra lesz hat\xE1ssal",
        calloutInvitationCountLabel: "F\xFCgg\u0151ben l\xE9v\u0151 megh\xEDv\xF3k: {{count}}",
        calloutSuggestionCountLabel: "F\xFCgg\u0151ben l\xE9v\u0151 javaslatok: {{count}}",
        manualInvitationOption__description: "Felhaszn\xE1l\xF3kat csak manu\xE1lisan lehet megh\xEDvni a szervezetbe.",
        manualInvitationOption__label: "Nincs automatikus felv\xE9tel",
        subtitle: "V\xE1laszd ki, hogy a felhaszn\xE1l\xF3k, hogyan csatlakozhatnak szervezethez."
      },
      start: {
        headerTitle__danger: "Vesz\xE9ly",
        headerTitle__enrollment: "Felv\xE9teli opci\xF3k"
      },
      subtitle: "A domain {{domain}} visszaigazolva. A folytat\xE1shoz v\xE1lassz felv\xE9teli m\xF3dot",
      title: "{{domain}} friss\xEDt\xE9se"
    },
    verifyDomainPage: {
      formSubtitle: "\xCDrd be a meger\u0151s\xEDt\u0151 k\xF3dot, amit elk\xFCldt\xFCnk az email c\xEDmedre",
      formTitle: "Meger\u0151s\xEDt\u0151 k\xF3d",
      resendButton: "Nem kapt\xE1l k\xF3dot? \xDAjrak\xFCld\xE9s",
      subtitle: "A domain {{domainName}}-t emaillel j\xF3v\xE1 kell hagyni.",
      subtitleVerificationCodeScreen: "Elk\xFCldt\xFCk a meger\u0151s\xEDt\u0151 k\xF3dot a(z) {{emailAddress}} c\xEDmre. A folytat\xE1shoz \xEDrd be a k\xF3dot",
      title: "Domain visszaigazol\xE1sa"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Szervezet l\xE9trehoz\xE1sa",
    action__invitationAccept: "Csatlakoz\xE1s",
    action__manageOrganization: "Kezel\xE9s",
    action__suggestionsAccept: "Csatlakoz\xE1s k\xE9r\xE9se",
    notSelected: "Nincs szervezet kiv\xE1lasztva",
    personalWorkspace: "Szem\xE9lyes fi\xF3k",
    suggestionsAcceptedLabel: "Elfogad\xE1sra v\xE1r"
  },
  paginationButton__next: "K\xF6vetkez\u0151",
  paginationButton__previous: "El\u0151z\u0151",
  paginationRowText__displaying: "Mutat",
  paginationRowText__of: "-b\xF3l/-b\u0151l",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Fi\xF3k hozz\xE1ad\xE1sa",
      action__signOutAll: "Kijelentkez\xE9s minden fi\xF3kb\xF3l",
      subtitle: "V\xE1laszd ki a fi\xF3kot amivel folytatni szeretn\xE9d",
      title: "V\xE1lassz egy fi\xF3kot"
    },
    alternativeMethods: {
      actionLink: "Seg\xEDts\xE9g k\xE9r\xE9se",
      actionText: "Nincs ezekb\u0151l egyik sem ?",
      blockButton__backupCode: "Tartal\xE9k k\xF3d haszn\xE1lata",
      blockButton__emailCode: "Email k\xF3d a {{identifier}}-nak/nek",
      blockButton__emailLink: "Email link a {{identifier}}-nak/nek",
      blockButton__passkey: "Bejelentkez\xE9s passkey-vel",
      blockButton__password: "Bejelentkez\xE9s jelsz\xF3val",
      blockButton__phoneCode: "SMS k\xF3d {{identifier}}-nak/nek",
      blockButton__totp: "Hiteles\xEDt\u0151 app haszn\xE1lata",
      getHelp: {
        blockButton__emailSupport: "Seg\xEDts\xE9g k\xE9r\xE9se emailben",
        content: "Ha b\xE1rmilyen probl\xE9m\xE1d van a bejelentkez\xE9ssel a fi\xF3kodba, k\xFCldj nek\xFCnk egy emailt, \xE9s vissza\xE1ll\xEDtjuk a fi\xF3kodat, amint lehets\xE9ges.",
        title: "Seg\xEDts\xE9g k\xE9r\xE9s"
      },
      subtitle: "Probl\xE9m\xE1d akadt? Ezek k\xF6z\xFCl b\xE1rmelyik bejelentkez\xE9si m\xF3dot v\xE1laszthatod.",
      title: "Bejelentkez\xE9s m\xE1s m\xF3don"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "A tartal\xE9k k\xF3d az, amit akkor kapt\xE1l, amikor be\xE1ll\xEDtottad a k\xE9tl\xE9pcs\u0151s azonos\xEDt\xE1st",
      title: "\xCDrd be a tartal\xE9k k\xF3dot"
    },
    emailCode: {
      formTitle: "Visszaigazol\xF3 k\xF3d",
      resendButton: "Nem kaptad meg a k\xF3dot? \xDAjrak\xFCld\xE9s",
      subtitle: "hogy folytathasd a(z) {{applicationName}}",
      title: "Ellen\u0151rizd az emailed"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "Menj vissza az eredeti lapra a folytat\xE1shoz.",
        title: "Ez a meger\u0151s\xEDt\u0151 link lej\xE1rt"
      },
      failed: {
        subtitle: "Menj vissza az eredeti lapra a folytat\xE1shoz.",
        title: "Ez a meger\u0151s\xEDt\u0151 link \xE9rv\xE9nytelen"
      },
      formSubtitle: "Haszn\xE1ld a meger\u0151s\xEDt\u0151 linket, amit a emailben kapt\xE1l",
      formTitle: "Meger\u0151s\xEDt\u0151 link",
      loading: {
        subtitle: "Hamarosan \xE1tir\xE1ny\xEDtunk",
        title: "Bejelentkez\xE9s folyamatban..."
      },
      resendButton: "Nem kapt\xE1l linket? \xDAjrak\xFCld\xE9s",
      subtitle: "hogy folytathasd a(z) {{applicationName}}",
      title: "Ellen\u0151rizd az emailed",
      unusedTab: {
        title: "Ezt a lapot bez\xE1rhatod"
      },
      verified: {
        subtitle: "Hamarosan \xE1tir\xE1ny\xEDtunk",
        title: "Sikeres bejelentkez\xE9s"
      },
      verifiedSwitchTab: {
        subtitle: "Menj vissza az eredeti lapra a folyat\xE1shoz",
        subtitleNewTab: "Menj \xE1t az \xFAjonan megnyitott lapra a folytat\xE1shoz",
        titleNewTab: "Egy m\xE1sik lapon bejelezkezt\xE9l be"
      }
    },
    forgotPassword: {
      formTitle: "Jelsz\xF3 vissza\xE1ll\xEDt\xF3 k\xF3d",
      resendButton: "Nem kaptad meg a k\xF3dot? \xDAjrak\xFCld\xE9s",
      subtitle: "hogy vissza\xE1ll\xEDthasd a jelszavad",
      subtitle_email: "El\u0151sz\xF6r, \xEDrd be a k\xF3dot amit emailben kapt\xE1l",
      subtitle_phone: "El\u0151sz\xF6r, \xEDrd be a k\xF3dot amit a telefonodra kapt\xE1l",
      title: "Jelsz\xF3 vissza\xE1ll\xEDt\xE1s"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "\xC1ll\xEDtsd vissza a jelszavad",
      label__alternativeMethods: "Vagy jelentkezz be m\xE1s m\xF3don",
      title: "Efelejtetted a jelszavad?"
    },
    noAvailableMethods: {
      message: "Nem lehet bejelentkezni. Nincs el\xE9rhet\u0151 hiteles\xEDt\u0151 t\xE9nyez\u0151.",
      subtitle: "Hiba t\xF6rt\xE9nt",
      title: "Nem lehetett bejelentkezni"
    },
    passkey: {
      subtitle: "A Passkey-d haszn\xE1lata meger\u0151s\xEDti, hogy te vagy az. Az eszk\xF6z\xF6d k\xE9rheti az ujjlenyomatod, arcod vagy a k\xE9perny\u0151z\xE1rad.",
      title: "Haszn\xE1ld a passkeydet"
    },
    password: {
      actionLink: "M\xE1sik m\xF3d haszn\xE1lata",
      subtitle: "\xCDrd be a fi\xF3khoz tartoz\xF3 jelszavad",
      title: "\xCDrd be a jelszavad"
    },
    passwordPwned: {
      title: "Jelsz\xF3 kompromit\xE1l\xF3dott"
    },
    phoneCode: {
      formTitle: "Visszaigazol\xF3 k\xF3d",
      resendButton: "Nem kaptad meg a k\xF3dot? \xDAjrak\xFCld\xE9s",
      subtitle: "hogy folytathasd a(z) {{applicationName}}",
      title: "Ellen\u0151rizd a telefonod"
    },
    phoneCodeMfa: {
      formTitle: "Visszaigazol\xF3 k\xF3d",
      resendButton: "Nem kaptad meg a k\xF3dot? \xDAjrak\xFCld\xE9s",
      subtitle: "A folytat\xE1shoz, k\xE9rlek \xEDrd be a visszaigazol\xF3 k\xF3dot, amit a telefonodra k\xFCldt\xFCnk.",
      title: "Ellen\u0151rizd a telefonod"
    },
    resetPassword: {
      formButtonPrimary: "Jelsz\xF3 vissza\xE1ll\xEDt\xE1sa",
      requiredMessage: "Biztons\xE1gi okokb\xF3l, musz\xE1j megv\xE1ltoztatnod a jelszavadat.",
      successMessage: "A jelszavad sikeresen megv\xE1ltozott. A bejelentkez\xE9s folyamatban, k\xE9rlek v\xE1rj.",
      title: "\xDAj jelsz\xF3 be\xE1ll\xEDt\xE1sa"
    },
    resetPasswordMfa: {
      detailsLabel: "Vissza kell igazolnod az identit\xE1sod, miel\u0151tt vissza\xE1ll\xEDtod a jelszavad"
    },
    start: {
      actionLink: "Regisztr\xE1ci\xF3",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "Email haszn\xE1lata",
      actionLink__use_email_username: "Haszn\xE1ld az emailded vagy a felhaszn\xE1l\xF3neved",
      actionLink__use_passkey: "Passkey haszn\xE1lata",
      actionLink__use_phone: "Telefon haszn\xE1lata",
      actionLink__use_username: "Felhaszn\xE1l\xF3n\xE9v haszn\xE1lata",
      actionText: "Nincs fi\xF3kod?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "\xDCdv \xFAjra! A folytat\xE1shoz k\xE9rlek jelentkezz be.",
      subtitleCombined: void 0,
      title: "Bejelentkez\xE9s a(z) {{applicationName}} fi\xF3kba",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Visszaigazol\xF3 k\xF3d",
      subtitle: "A folytat\xE1shoz, k\xE9rlek \xEDrd be a visszaigazol\xF3 k\xF3dot, amit a hiteles\xEDt\u0151 app k\xE9sz\xEDtett.",
      title: "K\xE9t l\xE9p\xE9cs\u0151s azonos\xEDt\xE1s"
    }
  },
  signInEnterPasswordTitle: "\xCDrd be a jelszavad",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Bejelentkez\xE9s",
      actionText: "Van m\xE1r fi\xF3kod?",
      subtitle: "K\xE9rlek t\xF6ltsd ki a h\xE1tral\xE9v\u0151 mez\u0151ket a folytat\xE1shoz",
      title: "T\xF6ltsd ki a hi\xE1nyz\xF3 mez\u0151ket"
    },
    emailCode: {
      formSubtitle: "\xCDrd be a visszaigazol\xF3 k\xF3dot, amit emailben kapt\xE1l",
      formTitle: "Viszaigazol\xF3 k\xF3d",
      resendButton: "Nem kaptad meg a k\xF3dot? \xDAjrak\xFCld\xE9s",
      subtitle: "\xCDrd be a visszaigazol\xF3 k\xF3dot, amit emailben kapt\xE1l",
      title: "Email meger\u0151s\xEDt\xE9se"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "Haszn\xE1ld a  visszaigazol\xF3 linket, amit a emailben kapt\xE1l",
      formTitle: "Visszaigazol\xF3 link",
      loading: {
        title: "Regisztr\xE1l\xE1s..."
      },
      resendButton: "Nem kaptad meg a k\xF3dot? \xDAjrak\xFCld\xE9s",
      subtitle: "hogy folytasd a(z) {{applicationName}}",
      title: "Er\u0151s\xEDtsd meg az email c\xEDmed",
      verified: {
        title: "Sikeres regisztr\xE1ci\xF3"
      },
      verifiedSwitchTab: {
        subtitle: "Menj az \xFAjonan nyitott lapra, a folytat\xE1shoz",
        subtitleNewTab: "Menj vissza az el\u0151z\u0151 lapra a folytat\xE1shoz",
        title: "Sikeresen meger\u0151s\xEDtetted az email c\xEDmed"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "\xCDrd be a visszaigazol\xF3 k\xF3dot, amit a telefondra kapt\xE1l",
      formTitle: "Visszaigazol\xF3 k\xF3d",
      resendButton: "Nem kaptad meg a k\xF3dot? \xDAjrak\xFCld\xE9s",
      subtitle: "\xCDrd be a visszaigazol\xF3 k\xF3dot, amit a telefonodra kapt\xE1l",
      title: "Er\u0151s\xEDtsd meg a telefonsz\xE1mod"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "Bejelentkez\xE9s",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "Van m\xE1r fi\xF3kod?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "\xDCdv! K\xE9rlek add meg az adatokat, hogy elkezdhesd.",
      subtitleCombined: "\xDCdv! K\xE9rlek add meg az adatokat, hogy elkezdhesd.",
      title: "Fi\xF3k l\xE9trehoz\xE1sa",
      titleCombined: "Fi\xF3k l\xE9trehoz\xE1sa"
    }
  },
  socialButtonsBlockButton: "Folytat\xE1s {{provider|titleize}} seg\xEDts\xE9g\xE9vel",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "Biztons\xE1gi okokb\xF3l a regisztr\xE1ci\xF3 sikertelen volt. K\xE9rlek friss\xEDtsd az oldalt, hogy \xFAjra pr\xF3b\xE1lhasd, vagy k\xE9rj t\xE1mogat\xE1st.",
    captcha_unavailable: "Bot \xE9rv\xE9nyes\xEDt\xE9se miatt, a regisztr\xE1ci\xF3 sikertelen volt. K\xE9rlek friss\xEDtsd az oldalt, hogy \xFAjra pr\xF3b\xE1lhasd, vagy k\xE9rj t\xE1mogat\xE1st.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: "Ez az email c\xEDm m\xE1r foglalt. K\xE9rlek pr\xF3b\xE1lj egy m\xE1sikat.",
    form_identifier_exists__phone_number: "Ez a telefonsz\xE1m m\xE1r foglalt. K\xE9rlek pr\xF3b\xE1lj egy m\xE1sikat.",
    form_identifier_exists__username: "Ez a felhaszn\xE1l\xF3n\xE9v m\xE1r foglalt. K\xE9rlek pr\xF3b\xE1lj egy m\xE1sikat.",
    form_identifier_not_found: "Nem tal\xE1ltunk fi\xF3kot ezekkel a r\xE9szletekkel.",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "Az email c\xEDmnek \xE9rv\xE9nyes email c\xEDmnek kell lennie.",
    form_param_format_invalid__phone_number: "A telefonsz\xE1mnak \xE9rv\xE9nyes telefonsz\xE1mnak kell lennie.",
    form_param_max_length_exceeded__first_name: "A keresztn\xE9v nem lehet hosszabb, mint 256 karakter.",
    form_param_max_length_exceeded__last_name: "A vezet\xE9kn\xE9v nem lehet hosszabb, mint 256 karakter.",
    form_param_max_length_exceeded__name: "A n\xE9v nem lehet hosszabb mint 256 karakter.",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "A jelsz\xF3 nem el\xE9g er\u0151s",
    form_password_pwned: "\xDAgy l\xE1tjuk, hogy ez a jelsz\xF3 kisziv\xE1rgott, ez\xE9rt ezt nem haszn\xE1lhatod, k\xE9rlek pr\xF3b\xE1lj egy m\xE1sik jelsz\xF3t.",
    form_password_pwned__sign_in: "\xDAgy l\xE1tjuk, hogy ez a jelsz\xF3 kisziv\xE1rgott, ez\xE9rt ezt nem haszn\xE1lhatod, k\xE9rlek \xE1ll\xEDtsd \xE1t a jelszavad.",
    form_password_size_in_bytes_exceeded: "A jelszavad t\xF6bb b\xE1jtot tartalmaz mint a megadott maximum, k\xE9rlek r\xF6vid\xEDtsd vagy t\xF6r\xF6lj ki n\xE9h\xE1ny speci\xE1lis karaktert.",
    form_password_validation_failed: "Helytelen jelsz\xF3",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "Nem t\xF6r\xF6lheted ki az utols\xF3 azonos\xEDt\xF3d.",
    not_allowed_access: "Az email c\xEDmed vagy a telefonsz\xE1mod nem haszn\xE1lhat\xF3 regisztr\xE1ci\xF3hoz. Ez lehet, mert az email c\xEDmedben vagy a telefonsz\xE1modban szerepel a '+', '=', '#' vagy '.' karakter, vagy az email c\xEDmedben vagy a telefonsz\xE1modban szerepel egy id\u0151z\xEDtett email szolg\xE1ltat\xF3 vagy kiz\xE1rt tartom\xE1ny. Ha \xFAgy gondolja, hogy ez hiba, vegye fel vel\xFCnk a kapcsolatot.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: "Egy passkey m\xE1r regisztr\xE1lva van ehhez az eszk\xF6zh\xF6z.",
    passkey_not_supported: "Passkeyk nem t\xE1mogatottak ezen az eszk\xF6z\xF6n.",
    passkey_pa_not_supported: "A regisztr\xE1ci\xF3hoz egy platform hiteles\xEDt\u0151 kell, de ez az eszk\xF6z ezt nem t\xE1mogatja.",
    passkey_registration_cancelled: "Passkey regisztr\xE1ci\xF3 megszakadt vagy lej\xE1rt.",
    passkey_retrieval_cancelled: "Passkey visszaigazol\xE1s megszakadt vagy lej\xE1rt.",
    passwordComplexity: {
      maximumLength: "kevesebb mint {{length}} karaktert",
      minimumLength: "{{length}} vagy t\xF6bb karaktert",
      requireLowercase: "egy kisbet\u0171t",
      requireNumbers: "egy sz\xE1mot",
      requireSpecialCharacter: "egy speci\xE1lis karaktert",
      requireUppercase: "egy nagybet\u0171",
      sentencePrefix: "A jelszavadnak tartalmaznia kell"
    },
    phone_number_exists: "Ez a telefonsz\xE1m m\xE1r foglalt. K\xE9rlek pr\xF3b\xE1lj meg egy m\xE1sikat.",
    session_exists: "M\xE1r be vagy jelentkezve.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "A jelszavad, j\xF3, de lehetne er\u0151sebb. Adj hozz\xE1 t\xF6bb karaktert.",
      goodPassword: "A jelszavad megfelel az elv\xE1r\xE1soknak.",
      notEnough: "Nem el\xE9g er\u0151s a jelszavad.",
      suggestions: {
        allUppercase: "V\xE1ltoztass meg n\xE9h\xE1ny bet\u0171t nagybet\u0171re, de ne mindet.",
        anotherWord: "Adj hozz\xE1 kev\xE9sb\xE9 gyakori szavakat.",
        associatedYears: "Ker\xFCld el a hozz\xE1d kapcsolhat\xF3 \xE9vek haszn\xE1lat\xE1t",
        capitalization: "Ne csak az els\u0151 bet\u0171 legyen nagy bet\u0171.",
        dates: "Ker\xFCld el a hozz\xE1d k\xF6thet\u0151 d\xE1tumok \xE9s \xE9vek haszn\xE1lat\xE1t.",
        l33t: "Ker\xFCld el a kisz\xE1m\xEDthat\xF3 bet\u0171 behelyettes\xEDt\xE9seket, mint a '@' az 'a' helyett.",
        longerKeyboardPattern: "Haszn\xE1lj hosszabb billenty\u0171zet mint\xE1kat, \xE9s v\xE1ltoztasd meg a g\xE9pel\xE9s ir\xE1ny\xE1t t\xF6bbsz\xF6r.",
        noNeed: "\xDAgy is l\xE9trehozhatsz er\u0151s jelsz\xF3t, hogy nem haszn\xE1lsz speci\xE1lis karaktereket, sz\xE1mokat, vagy nagybet\u0171ket.",
        pwned: "Ha m\xE1shol is haszn\xE1lod ezt a jelsz\xF3t, akkor v\xE1ltoztasd meg.",
        recentYears: "Ker\xFCld el a k\xF6zelm\xFAlt \xE9vek haszn\xE1lat\xE1t.",
        repeated: "Ker\xFCld el a sz\xF3- vagy karakterism\xE9tl\xE9st",
        reverseWords: "Ker\xFCld el a szavak visszafel\xE9 \xEDr\xE1s\xE1t.",
        sequences: "Ker\xFCld el a gyakori karakter sorozatokat.",
        useWords: "Haszn\xE1lj t\xF6bb sz\xF3t, de ker\xFCld el a gyakori kifejez\xE9seket."
      },
      warnings: {
        common: "Ez egy gyakran haszn\xE1lt jelsz\xF3",
        commonNames: "A gyakori nevek k\xF6nnyen kital\xE1lhat\xF3ak.",
        dates: "A d\xE1tumokat k\xF6nny\u0171 kital\xE1lni.",
        extendedRepeat: 'Ism\xE9tl\u0151d\u0151 karakter sorozatok, mint "abcabcabc" k\xF6nnyen kital\xE1lhat\xF3ak.',
        keyPattern: "A r\xF6vid billenty\u0171zetmint\xE1k k\xF6nnyen kital\xE1lhat\xF3ak.",
        namesByThemselves: "A nevek k\xF6nnyen kital\xE1lhat\xF3ak.",
        pwned: "A jelszavad kisziv\xE1rgott egy adatsziv\xE1rg\xE1s sor\xE1n az Interneten. V\xE1lassz egy m\xE1sikat.",
        recentYears: "Az elm\xFAlt \xE9vek k\xF6nnyen kital\xE1lhat\xF3ak.",
        sequences: 'Gyakori karakter sorozatok, mint "abc" k\xF6nnyen kital\xE1lhat\xF3ak.',
        similarToCommon: "Ez hasonl\xEDt egy gyakran haszn\xE1lt jelsz\xF3hoz.",
        simpleRepeat: 'Ism\xE9tl\u0151d\u0151 karakterek, mint az "aaa" k\xF6nnyen kital\xE1lhat\xF3ak.',
        straightRow: 'Egyenes sor a billenty\u0171zeten, mint az "asdf" k\xF6nnyen kital\xE1lhat\xF3ak.',
        topHundred: "Ez egy gyakran haszn\xE1lt jelsz\xF3",
        topTen: "Ez egy nagyon gyakori jelsz\xF3",
        userInputs: "Ne tartalmazzon, szem\xE9lyes, vagy az oldalhoz k\xF6thet\u0151 inform\xE1ci\xF3t.",
        wordByItself: "Egyszavas jelszavak k\xF6nnyen kital\xE1lhat\xF3ak."
      }
    }
  },
  userButton: {
    action__addAccount: "Fi\xF3k hozz\xE1ad\xE1sa",
    action__manageAccount: "Fi\xF3k kezel\xE9se",
    action__signOut: "Kijelentkez\xE9s",
    action__signOutAll: "Kijelentkez\xE9s minden fi\xF3kb\xF3l"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "Kim\xE1solva!",
      actionLabel__copy: "Az \xF6sszes kim\xE1sol\xE1sa",
      actionLabel__download: ".txt let\xF6lt\xE9se",
      actionLabel__print: "Nyomtat\xE1s",
      infoText1: "A tartal\xE9k k\xF3dok be lesznek kapcsolva ehhez a fi\xF3khoz.",
      infoText2: "A tartal\xE9k k\xF3dokat tartsd titokban, \xE9s t\xE1rold biztons\xE1gos helyen. \xDAjragener\xE1lhatod a tartal\xE9k k\xF3dokat, ha azt gondolod, hogy kisziv\xE1rogtak.",
      subtitle__codelist: "T\xE1rold \u0151ket biztons\xE1gos helyen, \xE9s tartsd titokban.",
      successMessage: "A tartal\xE9k k\xF3dok bekapcsolva. Haszn\xE1lhatod ezeket, hogy bel\xE9pj a fi\xF3kodba, ha nem f\xE9rsz hozz\xE1 a hiteles\xEDt\u0151 eszk\xF6zh\xF6z. Mindegyik k\xF3dot egyszer tudod haszn\xE1lni.",
      successSubtitle: "Haszn\xE1lhatod ezeket is, hogy bel\xE9pj a fi\xF3kodba, ha nem f\xE9rsz hozz\xE1 a hiteles\xEDt\u0151 eszk\xF6z\xF6dh\xF6z.",
      title: "Tartal\xE9k k\xF3d meger\u0151s\xEDt\xE9s hozz\xE1ad\xE1sa",
      title__codelist: "Tartal\xE9k k\xF3dok"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "V\xE1lassz egy szolg\xE1ltat\xF3t, amit \xF6sszek\xF6tsz a fi\xF3koddal.",
      formHint__noAccounts: "Nincs el\xE9rhet\u0151 k\xFCls\u0151 fi\xF3k szolg\xE1ltat\xF3.",
      removeResource: {
        messageLine1: "{{identifier}} el lesz t\xE1vol\xEDtva ebb\u0151l a fi\xF3kb\xF3l.",
        messageLine2: "Nem fogod tudni haszn\xE1lni ezt a kapcsolt fi\xF3kot. B\xE1rmilyen ett\u0151l f\xFCgg\u0151 szolg\xE1ltat\xE1s nem fog m\u0171k\xF6dni.",
        successMessage: "{{connectedAccount}} elt\xE1vol\xEDtva a fi\xF3kdb\xF3l.",
        title: "Kapcsolt fi\xF3k elt\xE1vol\xEDt\xE1sa"
      },
      socialButtonsBlockButton: "{{provider|titleize}}",
      successMessage: "A szolg\xE1tat\xF3 hozz\xE1 lett adva a fi\xF3kodhoz.",
      title: "Kapcsolt fi\xF3k hozz\xE1ad\xE1sa"
    },
    deletePage: {
      actionDescription: '\xCDrd be, hogy "Delete account" a folytat\xE1shoz.',
      confirm: "Fi\xF3k t\xF6rl\xE9se",
      messageLine1: "Biztos vagy benne, hogy t\xF6r\xF6lni szeretn\xE9d a fi\xF3kod?",
      messageLine2: "Ez a m\u0171velet v\xE9gleges \xE9s visszaford\xEDthatatlan.",
      title: "Fi\xF3k t\xF6rl\xE9se"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Egy visszaigazol\xF3 k\xF3dot tartalmaz\xF3 emailt fogunk k\xFCldeni erre az email c\xEDmre.",
        formSubtitle: "\xCDrd be a visszaigazol\xF3 k\xF3dot, amit a(z) {{identifier}} c\xEDmre k\xFCldt\xFCnk.",
        formTitle: "Visszaigazol\xF3 k\xF3d",
        resendButton: "Nem kaptad meg a k\xF3dot? \xDAjrak\xFCld\xE9s",
        successMessage: "Az email: {{identifier}} hozz\xE1 lett adva a fi\xF3kodhoz."
      },
      emailLink: {
        formHint: "Egy visszaigazol\xF3 linket tartalmaz\xF3 emailt fogunk k\xFCldeni erre az email c\xEDmre.",
        formSubtitle: "Kattints a visszaigazol\xF3 linkre az emailben, amit ide k\xFCldt\xFCnk: {{identifier}}",
        formTitle: "Visszaigazol\xF3 link",
        resendButton: "Nem kaptad meg a linket? \xDAjrak\xFCld\xE9s",
        successMessage: "Az email: {{identifier}} hozz\xE1 lett adva a fi\xF3kodhoz."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} el lesz t\xE1vol\xEDtva ebb\u0151l a fi\xF3kb\xF3l.",
        messageLine2: "Nem fogsz tudni t\xF6bbet bejelentkezni ezzel az email c\xEDmmel.",
        successMessage: "{{emailAddress}} el lett t\xE1vol\xEDtva a fi\xF3kodb\xF3l.",
        title: "Email c\xEDm t\xF6rl\xE9se"
      },
      title: "Email c\xEDm hozz\xE1ad\xE1sa",
      verifyTitle: "Email c\xEDm visszaigazol\xE1sa"
    },
    formButtonPrimary__add: "Hozz\xE1ad\xE1s",
    formButtonPrimary__continue: "Folytat\xE1s",
    formButtonPrimary__finish: "Befejez\xE9s",
    formButtonPrimary__remove: "Elt\xE1vol\xEDt\xE1s",
    formButtonPrimary__save: "Ment\xE9s",
    formButtonReset: "M\xE9gsem",
    mfaPage: {
      formHint: "V\xE1lassz egy hiteles\xEDt\u0151 m\xF3dszert, amit hozz\xE1 szeretn\xE9l adni.",
      title: "K\xE9tl\xE9pcs\u0151s azonos\xEDt\xE1s bekapcsol\xE1sa"
    },
    mfaPhoneCodePage: {
      backButton: "L\xE9tez\u0151 sz\xE1m haszn\xE1lata",
      primaryButton__addPhoneNumber: "Telefonsz\xE1m hozz\xE1ad\xE1sa",
      removeResource: {
        messageLine1: "{{identifier}} nem fog t\xF6bbet visszaigazol\xF3 k\xF3dot kapni, amikor bel\xE9psz.",
        messageLine2: "A fi\xF3k\xF3d nem biztos, hogy olyan biztons\xE1gos lesz. Biztosan folytatod?",
        successMessage: "K\xE9tl\xE9pcs\u0151s SMS k\xF3d elt\xE1vol\xEDtva a {{mfaPhoneCode}} sz\xE1mhoz",
        title: "K\xE9tl\xE9pcs\u0151s azonos\xEDt\xE1s elt\xE1vol\xEDt\xE1sa"
      },
      subtitle__availablePhoneNumbers: "V\xE1lassz egy telefonsz\xE1mot, hogy regisztr\xE1ld az SMS k\xF3d k\xE9tl\xE9pcs\u0151s azonos\xEDt\xE1st, vagy adj hozz\xE1 egy \xFAjat.",
      subtitle__unavailablePhoneNumbers: "Nincs el\xE9rhet\u0151 telefonsz\xE1m, az SMS k\xF3d k\xE9tl\xE9pcs\u0151s azonos\xEDt\xE1shoz. K\xE9rlek adj hozz\xE1 egyet.",
      successMessage1: "Amikor bel\xE9psz, extra l\xE9p\xE9sk\xE9nt meg kell adnod a visszaigazol\xF3 k\xF3dot, amit elk\xFCld\xFCnk erre a telefonsz\xE1mra.",
      successMessage2: "T\xE1rold ezeket a tartal\xE9k k\xF3dokat, egy biztons\xE1gos helyen. Ha nem f\xE9rsz hozz\xE1 a hiteles\xEDt\u0151 eszk\xF6z\xF6dh\xF6z, ezekkel tudsz bel\xE9pni.",
      successTitle: "SMS visszaigazol\xF3 k\xF3d hozz\xE1adva",
      title: "SMS visszaigazol\xF3 k\xF3d hozz\xE1ad\xE1sa"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Ink\xE1bb olvasd be a QR k\xF3dot",
        buttonUnableToScan__nonPrimary: "Nem tudod beolvasni a QR k\xF3dot?",
        infoText__ableToScan: "\xC1ll\xEDts be egy \xFAj bel\xE9p\xE9si m\xF3dot, a hiteles\xEDt\u0151 alkalmaz\xE1sodban \xE9s olvasd a QR k\xF3dot, hogy \xF6sszek\xF6sd a fi\xF3koddal.",
        infoText__unableToScan: "\xC1ll\xEDts be egy \xFAj bejelentkez\xE9s m\xF3dot a hiteles\xEDt\u0151 alkalmaz\xE1sodban, \xEDrd be a kulcsot, amit lejjebb tal\xE1lsz.",
        inputLabel__unableToScan1: "Bizonyosodj meg, hogy a Time-based vagy a One-time passwords be van kapcsolva, majd fejezd be a fi\xF3k \xF6sszek\xF6t\xE9s\xE9t a k\xF6vetkez\u0151 kulccsal:",
        inputLabel__unableToScan2: "Alternat\xEDvak\xE9nt, ha az hiteles\xEDt\u0151 alkalmaz\xE1sod t\xE1mogatja a TOTP URI-kat, a teljes URI-t is be lehet m\xE1solni."
      },
      removeResource: {
        messageLine1: "Visszaigazol\xF3 k\xF3dok ebb\u0151l a hiteles\xEDt\u0151 alkalmaz\xE1sb\xF3l, m\xE1r nem fognak kelleni a bejelenetkez\xE9shez.",
        messageLine2: "El\u0151fordulhat, hogy a fi\xF3kod nem lesz olyan biztons\xE1gos. Biztonsan folytatni szeretn\xE9d?",
        successMessage: "K\xE9tl\xE9pcs\u0151s azonos\xEDt\xE1s hiteles\xEDt\u0151 alkalmaz\xE1ssal elt\xE1vol\xEDtva.",
        title: "K\xE9tl\xE9pcs\u0151s azonos\xEDt\xE1s elt\xE1vol\xEDt\xE1sa"
      },
      successMessage: "K\xE9tl\xE9pcs\u0151s azonos\xEDt\xE1st bekapcsolva. Extra l\xE9p\xE9sk\xE9nt, amikor bel\xE9psz, meg kell adnod a visszaigazol\xF3 k\xF3dot a hiteles\xEDt\u0151 alkalmaz\xE1sodb\xF3l.",
      title: "Hiteles\xEDt\u0151 alkalmaz\xE1s hozz\xE1ad\xE1sa",
      verifySubtitle: "\xCDrd be a visszaigazol\xF3 k\xF3dot, amit a hiteles\xEDt\u0151 alkalmaz\xE1sodb\xF3l.",
      verifyTitle: "Visszaigazol\xF3 k\xF3d"
    },
    mobileButton__menu: "Men\xFC",
    navbar: {
      account: "Profil",
      billing: void 0,
      description: "Fi\xF3k inform\xE1ci\xF3k kezel\xE9se",
      security: "Biztons\xE1g",
      title: "Fi\xF3k"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} el lesz t\xE1vol\xEDtva ebb\u0151l a fi\xF3kb\xF3l.",
        title: "Passkey t\xF6rl\xE9se"
      },
      subtitle__rename: "Megv\xE1ltoztathatod a passkey nev\xE9t, hogy k\xF6nnyebb legyen megtal\xE1lni.",
      title__rename: "Passkey \xE1tnevez\xE9se"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Aj\xE1nlott kijelentkeztetni az \xF6sszes olyan eszk\xF6zt, ami a r\xE9gi jelszavadat haszn\xE1lja.",
      readonly: "A jelszavad jelenleg nem m\xF3dos\xEDthat\xF3, mert csak v\xE1llalati kapcsolattal tudsz bel\xE9pni.",
      successMessage__set: "A jelszavad be\xE1ll\xEDtottuk.",
      successMessage__signOutOfOtherSessions: "Minden m\xE1s eszk\xF6z kijelentkeztetve.",
      successMessage__update: "A jelszavad friss\xEDt\xE9sre ker\xFClt.",
      title__set: "Jelsz\xF3 be\xE1ll\xEDt\xE1sa",
      title__update: "Jelsz\xF3 friss\xEDt\xE9se"
    },
    phoneNumberPage: {
      infoText: "Egy sz\xF6veges \xFCzenetet k\xFCld\xFCnk a visszaigazol\xF3 k\xF3ddal erre a sz\xE1mra. Az \xFCzenet \xE9s az adatforgalom d\xEDjai \xE9rv\xE9nyesek lehetnek.",
      removeResource: {
        messageLine1: "{{identifier}} el lesz t\xE1vol\xEDtva ebb\u0151l a fi\xF3kb\xF3l.",
        messageLine2: "Nem fogsz tudni t\xF6bbet bejelentkezni ezzel a telefonsz\xE1mmal.",
        successMessage: "{{phoneNumber}} el lett t\xE1vol\xEDtva a fi\xF3kodb\xF3l.",
        title: "Telefonsz\xE1m elt\xE1vol\xEDt\xE1sa"
      },
      successMessage: "{{identifier}} hozz\xE1 lett adva a fi\xF3kodhoz.",
      title: "Telefonsz\xE1m hozz\xE1ad\xE1sa",
      verifySubtitle: "\xCDrd be a visszaigaz\xF3l\xF3 k\xF3dot, amit a(z) {{identifier}} sz\xE1mra k\xFCldt\xFCnk.",
      verifyTitle: "Telefonsz\xE1m visszaigazol\xE1sa"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Aj\xE1nlott m\xE9ret 1:1, 10MB-ig.",
      imageFormDestructiveActionSubtitle: "Elt\xE1vol\xEDt\xE1s",
      imageFormSubtitle: "Felt\xF6lt\xE9s",
      imageFormTitle: "Profil k\xE9p",
      readonly: "A profilod adatai, v\xE1llalati kapcsolatb\xF3l sz\xE1rmaznak, \xEDgy nem m\xF3dos\xEDthat\xF3ak.",
      successMessage: "A profilod friss\xEDtve.",
      title: "Profil friss\xEDt\xE9se"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Kijelentkez\xE9s az eszk\xF6zr\u0151l",
        title: "Akt\xEDv eszk\xF6z\xF6k"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Pr\xF3b\xE1ld \xFAjra",
        actionLabel__reauthorize: "Enged\xE9lyezd most",
        destructiveActionTitle: "Elt\xE1vol\xEDt\xE1s",
        primaryButton: "Fi\xF3k \xF6sszek\xF6t\xE9se",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "A sz\xFCks\xE9ges hat\xE1sk\xF6r\xF6k megv\xE1ltozt\xE1k, el\u0151fordulhat, hogy limit\xE1lt funkcionalit\xE1st tapasztalhatsz. K\xE9rlek, \xFAjra enged\xE9lyezd az alkalmaz\xE1st, hogy elker\xFCld a hib\xE1kat.",
        title: "Kapcsolt fi\xF3kok"
      },
      dangerSection: {
        deleteAccountButton: "Fi\xF3k t\xF6rl\xE9se",
        title: "Fi\xF3k t\xF6rl\xE9se"
      },
      emailAddressesSection: {
        destructiveAction: "Email elt\xE1vol\xEDt\xE1sa",
        detailsAction__nonPrimary: "Be\xE1ll\xEDt\xE1s els\u0151dlegesk\xE9nt",
        detailsAction__primary: "Visszaigazol\xE1s befejez\xE9se",
        detailsAction__unverified: "Visszaigazol\xE1s",
        primaryButton: "Email c\xEDm hozz\xE1ad\xE1sa",
        title: "Email c\xEDmek"
      },
      enterpriseAccountsSection: {
        title: "V\xE1llalati fi\xF3kok"
      },
      headerTitle__account: "Profil adatok",
      headerTitle__security: "Biztons\xE1g",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "\xDAjra gener\xE1l\xE1s",
          headerTitle: "Tartal\xE9k k\xF3dok",
          subtitle__regenerate: "K\xE9rj egy \xFAj biztons\xE1gos tartal\xE9k k\xF3dokat. Az el\u0151z\u0151 k\xF3dok t\xF6rl\xE9sre ker\xFClnek, \xE9s nem lesznek haszn\xE1lhat\xF3k.",
          title__regenerate: "Tartal\xE9k k\xF3dok \xFAjragener\xE1l\xE1sa"
        },
        phoneCode: {
          actionLabel__setDefault: "Be\xE1ll\xEDt\xE1s alap\xE9rtelmezettk\xE9nt",
          destructiveActionLabel: "Elt\xE1vol\xEDt\xE1s"
        },
        primaryButton: "K\xE9tl\xE9pcs\u0151s azonos\xEDt\xE1s hozz\xE1ad\xE1sa",
        title: "K\xE9t l\xE9pcs\u0151s azonos\xEDt\xE1s",
        totp: {
          destructiveActionTitle: "Elt\xE1vol\xEDt\xE1s",
          headerTitle: "Hiteles\xEDt\u0151 alkalmaz\xE1s"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Elt\xE1vol\xEDt\xE1s",
        menuAction__rename: "\xC1tnevez\xE9s",
        primaryButton: void 0,
        title: "Passkey-k"
      },
      passwordSection: {
        primaryButton__setPassword: "Jelsz\xF3 be\xE1ll\xEDt\xE1sa",
        primaryButton__updatePassword: "Jelsz\xF3 friss\xEDt\xE9se",
        title: "Jelsz\xF3"
      },
      phoneNumbersSection: {
        destructiveAction: "Teleofnsz\xE1m elt\xE1vol\xEDt\xE1sa",
        detailsAction__nonPrimary: "Be\xE1ll\xEDt\xE1s els\u0151dlegesk\xE9nt",
        detailsAction__primary: "Visszaigazol\xE1s befejez\xE9se",
        detailsAction__unverified: "Teleofnsz\xE1m visszaigazol\xE1sa",
        primaryButton: "Telefonsz\xE1m hozz\xE1ad\xE1sa",
        title: "Telefonsz\xE1mok"
      },
      profileSection: {
        primaryButton: "Profil friss\xEDt\xE9se",
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "Felhaszn\xE1l\xF3n\xE9v be\xE1ll\xEDt\xE1sa",
        primaryButton__updateUsername: "Felhaszn\xE1l\xF3n\xE9v friss\xEDt\xE9se",
        title: "Felhaszn\xE1l\xF3n\xE9v"
      },
      web3WalletsSection: {
        destructiveAction: "T\xE1rca elt\xE1vol\xEDt\xE1sa",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 t\xE1rc\xE1k",
        title: "Web3 t\xE1rc\xE1k"
      }
    },
    usernamePage: {
      successMessage: "A felhaszn\xE1l\xF3neved friss\xEDtve.",
      title__set: "Felhaszn\xE1l\xF3n\xE9v be\xE1ll\xEDt\xE1sa",
      title__update: "Felaszn\xE1l\xF3n\xE9v friss\xEDt\xE9se"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} el lesz t\xE1vol\xEDtva ebb\u0151l a fi\xF3kb\xF3l.",
        messageLine2: "Nem fogod tudni haszn\xE1lni a bejelentkez\xE9shez, ezt a web3 t\xE1rc\xE1t.",
        successMessage: "{{web3Wallet}} elt\xE1vol\xEDtva a fi\xF3kodb\xF3l.",
        title: "Web3 t\xE1rca elt\xE1vol\xEDt\xE1sa"
      },
      subtitle__availableWallets: "V\xE1laszd ki a web3 t\xE1rc\xE1t, amit hozz\xE1 szeretn\xE9l adni a fi\xF3kodhoz.",
      subtitle__unavailableWallets: "Nincs el\xE9rhet\u0151 web3 t\xE1rca.",
      successMessage: "A t\xE1rca sikeresen hozz\xE1adva a fi\xF3kodhoz.",
      title: "Web3 t\xE1rca hozz\xE1ad\xE1sa",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  huHU
});
//# sourceMappingURL=hu-HU.js.map