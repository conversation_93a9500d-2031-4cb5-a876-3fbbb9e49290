{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40clerk/nextjs/dist/esm/app-router/server-actions.js"], "sourcesContent": ["\"use server\";\nimport { cookies } from \"next/headers\";\nasync function invalidateCacheAction() {\n  void (await cookies()).delete(`__clerk_invalidate_cache_cookie_${Date.now()}`);\n}\nexport {\n  invalidateCacheAction\n};\n"], "names": [], "mappings": ";;;;;AACA;;;;;AACA,eAAe;IACb,KAAK,CAAC,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD,GAAG,EAAE,MAAM,CAAC,CAAC,gCAAgC,EAAE,KAAK,GAAG,IAAI;AAC/E;;;;IAEE;;AAAA,+OAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/.next-internal/server/app/%5Blocale%5D/%28auth%29/%28center%29/sign-in/%5B%5B...sign-in%5D%5D/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {deleteKeylessAction as '7f4a02af96e1bb6aa73a8043a67d03e71bf5a0b1fc'} from 'ACTIONS_MODULE0'\nexport {createOrReadKeylessAction as '7f77a91b277141486faa446cc5de3827f9d835b4e4'} from 'ACTIONS_MODULE0'\nexport {syncKeylessConfigAction as '7ff46c643d4772dc8ee990c6d15e55cd365348ec79'} from 'ACTIONS_MODULE0'\nexport {invalidateCacheAction as '7f004e23488fbd4b4c13c8ce70e3fc1b5cfb4136dd'} from 'ACTIONS_MODULE1'\n"], "names": [], "mappings": ";AAAA;AAGA", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40clerk/nextjs/dist/esm/client-boundary/uiComponents.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const APIKeys = registerClientReference(\n    function() { throw new Error(\"Attempted to call APIKeys() from the server but APIKeys is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"APIKeys\",\n);\nexport const CreateOrganization = registerClientReference(\n    function() { throw new Error(\"Attempted to call CreateOrganization() from the server but CreateOrganization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"CreateOrganization\",\n);\nexport const GoogleOneTap = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoogleOneTap() from the server but GoogleOneTap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"GoogleOneTap\",\n);\nexport const OrganizationList = registerClientReference(\n    function() { throw new Error(\"Attempted to call OrganizationList() from the server but OrganizationList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"OrganizationList\",\n);\nexport const OrganizationProfile = registerClientReference(\n    function() { throw new Error(\"Attempted to call OrganizationProfile() from the server but OrganizationProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"OrganizationProfile\",\n);\nexport const OrganizationSwitcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call OrganizationSwitcher() from the server but OrganizationSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"OrganizationSwitcher\",\n);\nexport const PricingTable = registerClientReference(\n    function() { throw new Error(\"Attempted to call PricingTable() from the server but PricingTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"PricingTable\",\n);\nexport const SignIn = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignIn() from the server but SignIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"SignIn\",\n);\nexport const SignInButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignInButton() from the server but SignInButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"SignInButton\",\n);\nexport const SignInWithMetamaskButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignInWithMetamaskButton() from the server but SignInWithMetamaskButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"SignInWithMetamaskButton\",\n);\nexport const SignOutButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignOutButton() from the server but SignOutButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"SignOutButton\",\n);\nexport const SignUp = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignUp() from the server but SignUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"SignUp\",\n);\nexport const SignUpButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignUpButton() from the server but SignUpButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"SignUpButton\",\n);\nexport const UserButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserButton() from the server but UserButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"UserButton\",\n);\nexport const UserProfile = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserProfile() from the server but UserProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"UserProfile\",\n);\nexport const Waitlist = registerClientReference(\n    function() { throw new Error(\"Attempted to call Waitlist() from the server but Waitlist is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js <module evaluation>\",\n    \"Waitlist\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,qGACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,qGACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qGACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qGACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,qGACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,qGACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qGACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,qGACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qGACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,qGACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qGACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,qGACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qGACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,qGACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,qGACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,qGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40clerk/nextjs/dist/esm/client-boundary/uiComponents.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const APIKeys = registerClientReference(\n    function() { throw new Error(\"Attempted to call APIKeys() from the server but APIKeys is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"APIKeys\",\n);\nexport const CreateOrganization = registerClientReference(\n    function() { throw new Error(\"Attempted to call CreateOrganization() from the server but CreateOrganization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"CreateOrganization\",\n);\nexport const GoogleOneTap = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoogleOneTap() from the server but GoogleOneTap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"GoogleOneTap\",\n);\nexport const OrganizationList = registerClientReference(\n    function() { throw new Error(\"Attempted to call OrganizationList() from the server but OrganizationList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"OrganizationList\",\n);\nexport const OrganizationProfile = registerClientReference(\n    function() { throw new Error(\"Attempted to call OrganizationProfile() from the server but OrganizationProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"OrganizationProfile\",\n);\nexport const OrganizationSwitcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call OrganizationSwitcher() from the server but OrganizationSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"OrganizationSwitcher\",\n);\nexport const PricingTable = registerClientReference(\n    function() { throw new Error(\"Attempted to call PricingTable() from the server but PricingTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"PricingTable\",\n);\nexport const SignIn = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignIn() from the server but SignIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"SignIn\",\n);\nexport const SignInButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignInButton() from the server but SignInButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"SignInButton\",\n);\nexport const SignInWithMetamaskButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignInWithMetamaskButton() from the server but SignInWithMetamaskButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"SignInWithMetamaskButton\",\n);\nexport const SignOutButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignOutButton() from the server but SignOutButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"SignOutButton\",\n);\nexport const SignUp = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignUp() from the server but SignUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"SignUp\",\n);\nexport const SignUpButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignUpButton() from the server but SignUpButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"SignUpButton\",\n);\nexport const UserButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserButton() from the server but UserButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"UserButton\",\n);\nexport const UserProfile = registerClientReference(\n    function() { throw new Error(\"Attempted to call UserProfile() from the server but UserProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"UserProfile\",\n);\nexport const Waitlist = registerClientReference(\n    function() { throw new Error(\"Attempted to call Waitlist() from the server but Waitlist is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\",\n    \"Waitlist\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,iFACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,iFACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iFACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iFACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,iFACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,iFACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iFACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,iFACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iFACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,iFACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iFACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,iFACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iFACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,iFACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,iFACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,iFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/%40clerk/nextjs/src/client-boundary/uiComponents.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  OrganizationProfile as BaseOrganizationProfile,\n  SignIn as BaseSignIn,\n  SignUp as BaseSignUp,\n  UserProfile as BaseUserProfile,\n} from '@clerk/clerk-react';\nimport type { ComponentProps } from 'react';\nimport React from 'react';\n\nimport { useEnforceCorrectRoutingProps } from './hooks/useEnforceRoutingProps';\n\nexport {\n  CreateOrganization,\n  OrganizationList,\n  OrganizationSwitcher,\n  SignInButton,\n  SignInWithMetamaskButton,\n  SignOutButton,\n  SignUpButton,\n  UserButton,\n  GoogleOneTap,\n  Waitlist,\n  PricingTable,\n  APIKeys,\n} from '@clerk/clerk-react';\n\n// The assignment of UserProfile with BaseUserProfile props is used\n// to support the CustomPage functionality (eg UserProfile.Page)\n// Also the `typeof BaseUserProfile` is used to resolve the following error:\n// \"The inferred type of 'UserProfile' cannot be named without a reference to ...\"\nexport const UserProfile: typeof BaseUserProfile = Object.assign(\n  (props: ComponentProps<typeof BaseUserProfile>) => {\n    return <BaseUserProfile {...useEnforceCorrectRoutingProps('UserProfile', props)} />;\n  },\n  { ...BaseUserProfile },\n);\n\n// The assignment of OrganizationProfile with BaseOrganizationProfile props is used\n// to support the CustomPage functionality (eg OrganizationProfile.Page)\n// Also the `typeof BaseOrganizationProfile` is used to resolved the following error:\n// \"The inferred type of 'OrganizationProfile' cannot be named without a reference to ...\"\nexport const OrganizationProfile: typeof BaseOrganizationProfile = Object.assign(\n  (props: ComponentProps<typeof BaseOrganizationProfile>) => {\n    return <BaseOrganizationProfile {...useEnforceCorrectRoutingProps('OrganizationProfile', props)} />;\n  },\n  { ...BaseOrganizationProfile },\n);\n\nexport const SignIn = (props: ComponentProps<typeof BaseSignIn>) => {\n  return <BaseSignIn {...useEnforceCorrectRoutingProps('SignIn', props, false)} />;\n};\n\nexport const SignUp = (props: ComponentProps<typeof BaseSignUp>) => {\n  return <BaseSignUp {...useEnforceCorrectRoutingProps('SignUp', props, false)} />;\n};\n"], "names": [], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js"], "sourcesContent": ["import { cache } from 'react';\nimport { createTranslator } from 'use-intl/core';\n\nfunction getServerTranslatorImpl(config, namespace) {\n  return createTranslator({\n    ...config,\n    namespace\n  });\n}\nvar getServerTranslator = cache(getServerTranslatorImpl);\n\nexport { getServerTranslator as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,wBAAwB,MAAM,EAAE,SAAS;IAChD,OAAO,CAAA,GAAA,iLAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB,GAAG,MAAM;QACT;IACF;AACF;AACA,IAAI,sBAAsB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\nimport getServerTranslator from './getServerTranslator.js';\n\n// Maintainer note: `getTranslations` has two different call signatures.\n// We need to define these with function overloads, otherwise TypeScript\n// messes up the return type.\n\n// Call signature 1: `getTranslations(namespace)`\n\n// Call signature 2: `getTranslations({locale, namespace})`\n\n// Implementation\nasync function getTranslations(namespaceOrOpts) {\n  let namespace;\n  let locale;\n  if (typeof namespaceOrOpts === 'string') {\n    namespace = namespaceOrOpts;\n  } else if (namespaceOrOpts) {\n    locale = namespaceOrOpts.locale;\n    namespace = namespaceOrOpts.namespace;\n  }\n  const config = await getConfig(locale);\n  return getServerTranslator(config, namespace);\n}\nvar getTranslations$1 = cache(getTranslations);\n\nexport { getTranslations$1 as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,wEAAwE;AACxE,wEAAwE;AACxE,6BAA6B;AAE7B,iDAAiD;AAEjD,2DAA2D;AAE3D,iBAAiB;AACjB,eAAe,gBAAgB,eAAe;IAC5C,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,oBAAoB,UAAU;QACvC,YAAY;IACd,OAAO,IAAI,iBAAiB;QAC1B,SAAS,gBAAgB,MAAM;QAC/B,YAAY,gBAAgB,SAAS;IACvC;IACA,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD,EAAE;IAC/B,OAAO,CAAA,GAAA,8MAAA,CAAA,UAAmB,AAAD,EAAE,QAAQ;AACrC;AACA,IAAI,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;AAG9B,OAAO,MAAMG,eAAe;AAG5B,EAAC;;;;;;;;;;;;AAEuG,EAAC,uEAAA;AAEzG,UAAA,kDAA4D;AAC5D,MAAA,CAAO,MAAMK;IAAAA;IAAAA,SAAc,IAAIX,mBAAmB;YAChDY,QAAAA;YAAAA,GAAY;YAAA;wBACVC,IAAAA;oBAAAA,CAAMZ;oBAAAA,CAAUa,QAAQ;gCACxBC,IAAAA;4BAAAA,CAAM;4BAAA;wCACNC,IAAAA;oCAAAA,KAAU;oCAAA;gDACV,IAAA;4CAAA;4CAAA,oBAA2C;wDAC3CC,IAAAA;oDAAAA,OAAY;oDAAA;yDACZC,UAAU;8DACVC,IAAAA,CAAAA,GAAU;wDAAA,CAAE,OAAA;4DAAA,IAAA;4DAAA;yDAAA;;mDACd;;6CACAC,UAAU;sDACRC,IAAAA,CAAAA;4CAAAA;yCAAAA,CAAYnB;;qCACd;wCACF,CAAE,SAAA,CAAA;oCAAA;iCAAA", "ignoreList": [0], "debugId": null}}]}