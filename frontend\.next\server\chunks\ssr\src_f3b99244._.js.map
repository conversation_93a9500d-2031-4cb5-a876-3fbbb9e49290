{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/utils/AppConfig.ts"], "sourcesContent": ["import type { LocalizationResource } from '@clerk/types';\r\nimport type { LocalePrefixMode } from 'next-intl/routing';\r\nimport { enUS, frFR } from '@clerk/localizations';\r\n\r\nconst localePrefix: LocalePrefixMode = 'as-needed';\r\n\r\n// AppExtera configuration\r\nexport const AppConfig = {\r\n  name: 'AppExtera',\r\n  locales: ['en', 'fr'],\r\n  defaultLocale: 'en',\r\n  localePrefix,\r\n};\r\n\r\nconst supportedLocales: Record<string, LocalizationResource> = {\r\n  en: enUS,\r\n  fr: frFR,\r\n};\r\n\r\nexport const ClerkLocalizations = {\r\n  defaultLocale: enUS,\r\n  supportedLocales,\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;;AAEA,MAAM,eAAiC;AAGhC,MAAM,YAAY;IACvB,MAAM;IACN,SAAS;QAAC;QAAM;KAAK;IACrB,eAAe;IACf;AACF;AAEA,MAAM,mBAAyD;IAC7D,IAAI,0JAAA,CAAA,OAAI;IACR,IAAI,0JAAA,CAAA,OAAI;AACV;AAEO,MAAM,qBAAqB;IAChC,eAAe,0JAAA,CAAA,OAAI;IACnB;AACF", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/libs/I18nRouting.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\r\nimport { AppConfig } from '@/utils/AppConfig';\r\n\r\nexport const routing = defineRouting({\r\n  locales: AppConfig.locales,\r\n  localePrefix: AppConfig.localePrefix,\r\n  defaultLocale: AppConfig.defaultLocale,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,kOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,SAAS,yHAAA,CAAA,YAAS,CAAC,OAAO;IAC1B,cAAc,yHAAA,CAAA,YAAS,CAAC,YAAY;IACpC,eAAe,yHAAA,CAAA,YAAS,CAAC,aAAa;AACxC", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/libs/I18nNavigation.ts"], "sourcesContent": ["import { createNavigation } from 'next-intl/navigation';\r\nimport { routing } from './I18nRouting';\r\n\r\nexport const { usePathname } = createNavigation(routing);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8PAAA,CAAA,mBAAgB,AAAD,EAAE,0HAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/LocaleSwitcher.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ChangeEventHandler } from 'react';\r\nimport { useLocale } from 'next-intl';\r\nimport { useRouter } from 'next/navigation';\r\nimport { usePathname } from '@/libs/I18nNavigation';\r\nimport { routing } from '@/libs/I18nRouting';\r\n\r\nexport const LocaleSwitcher = () => {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const locale = useLocale();\r\n\r\n  const handleChange: ChangeEventHandler<HTMLSelectElement> = (event) => {\r\n    router.push(`/${event.target.value}${pathname}`);\r\n    router.refresh(); // Ensure the page takes the new locale into account related to the issue #395\r\n  };\r\n\r\n  return (\r\n    <select\r\n      defaultValue={locale}\r\n      onChange={handleChange}\r\n      className=\"border border-gray-300 font-medium focus:outline-hidden focus-visible:ring-3\"\r\n      aria-label=\"lang-switcher\"\r\n    >\r\n      {routing.locales.map(elt => (\r\n        <option key={elt} value={elt}>\r\n          {elt.toUpperCase()}\r\n        </option>\r\n      ))}\r\n    </select>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAQO,MAAM,iBAAiB;IAC5B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAsD,CAAC;QAC3D,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,KAAK,GAAG,UAAU;QAC/C,OAAO,OAAO,IAAI,8EAA8E;IAClG;IAEA,qBACE,8OAAC;QACC,cAAc;QACd,UAAU;QACV,WAAU;QACV,cAAW;kBAEV,0HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,oBACnB,8OAAC;gBAAiB,OAAO;0BACtB,IAAI,WAAW;eADL;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useTranslations } from 'next-intl';\nimport Link from 'next/link';\nimport { LocaleSwitcher } from '@/components/LocaleSwitcher';\nimport { AppConfig } from '@/utils/AppConfig';\n\nexport const Navigation = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const t = useTranslations('RootLayout');\n\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\n\n  return (\n    <nav className=\"bg-white/95 backdrop-blur-sm border-b border-secondary-200 sticky top-0 z-50\">\n      <div className=\"container-wide\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">A</span>\n              </div>\n              <span className=\"text-xl font-bold text-secondary-900\">\n                {AppConfig.name}\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-8\">\n              <Link\n                href=\"/\"\n                className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {t('home_link')}\n              </Link>\n              <Link\n                href=\"/features/\"\n                className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {t('features_link')}\n              </Link>\n              <Link\n                href=\"/pricing/\"\n                className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {t('pricing_link')}\n              </Link>\n              <Link\n                href=\"/about/\"\n                className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {t('about_link')}\n              </Link>\n              <Link\n                href=\"/blog/\"\n                className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {t('blog_link')}\n              </Link>\n              <Link\n                href=\"/contact/\"\n                className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n              >\n                {t('contact_link')}\n              </Link>\n            </div>\n          </div>\n\n          {/* Desktop Right Side */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <LocaleSwitcher />\n            <Link\n              href=\"/sign-in/\"\n              className=\"text-secondary-700 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\"\n            >\n              {t('sign_in_link')}\n            </Link>\n            <Link\n              href=\"/sign-up/\"\n              className=\"btn-primary text-sm\"\n            >\n              {t('sign_up_link')}\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={toggleMenu}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-secondary-700 hover:text-primary-600 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\"\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {!isMenuOpen ? (\n                <svg className=\"block h-6 w-6\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" aria-hidden=\"true\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              ) : (\n                <svg className=\"block h-6 w-6\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" aria-hidden=\"true\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-secondary-200\">\n              <Link\n                href=\"/\"\n                className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('home_link')}\n              </Link>\n              <Link\n                href=\"/features/\"\n                className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('features_link')}\n              </Link>\n              <Link\n                href=\"/pricing/\"\n                className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('pricing_link')}\n              </Link>\n              <Link\n                href=\"/about/\"\n                className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('about_link')}\n              </Link>\n              <Link\n                href=\"/blog/\"\n                className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('blog_link')}\n              </Link>\n              <Link\n                href=\"/contact/\"\n                className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('contact_link')}\n              </Link>\n              <div className=\"border-t border-secondary-200 pt-4\">\n                <div className=\"flex items-center px-3 space-x-3\">\n                  <LocaleSwitcher />\n                </div>\n                <Link\n                  href=\"/sign-in/\"\n                  className=\"text-secondary-700 hover:text-primary-600 block px-3 py-2 text-base font-medium transition-colors duration-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {t('sign_in_link')}\n                </Link>\n                <Link\n                  href=\"/sign-up/\"\n                  className=\"btn-primary block mx-3 mt-2 text-center\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {t('sign_up_link')}\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQO,MAAM,aAAa;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAK,WAAU;kDACb,yHAAA,CAAA,YAAS,CAAC,IAAI;;;;;;;;;;;;;;;;;sCAMrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;;;;;;;;;;;;sCAMT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,iBAAc;;;;;8CACf,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;8CAEL,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;;;;;;;sCAKP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,iBAAc;;kDAEd,8OAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,CAAC,2BACA,8OAAC;wCAAI,WAAU;wCAAgB,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;wCAAe,eAAY;kDAClI,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;6DAGvE,8OAAC;wCAAI,WAAU;wCAAgB,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;wCAAe,eAAY;kDAClI,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ9E,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;kDAEjB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAE5B,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAE5B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB", "debugId": null}}]}