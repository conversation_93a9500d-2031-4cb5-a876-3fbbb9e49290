"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadSnapshots = exports.detectSnapshots = exports.pullSnapshots = void 0;
const fsAsync = __importStar(require("node:fs/promises"));
const fs = __importStar(require("node:fs"));
const path = __importStar(require("node:path"));
const stream = __importStar(require("node:stream/promises"));
const api_1 = require("../rest/api");
const util_1 = require("./util");
async function pullSnapshots(basePath, snapshots) {
    if (!snapshots?.length) {
        return;
    }
    try {
        for (const snapshot of snapshots) {
            const fullPath = path.resolve(basePath, snapshot.path);
            if (!fullPath.startsWith(basePath)) {
                // The snapshot file should always be within the project, but we validate this just in case.
                throw new Error(`Detected invalid snapshot file ${fullPath}`);
            }
            await fsAsync.mkdir(path.dirname(fullPath), { recursive: true });
            const fileStream = fs.createWriteStream(fullPath);
            const { data: contentStream } = await api_1.checklyStorage.download(snapshot.key);
            contentStream.pipe(fileStream);
            await stream.finished(contentStream);
        }
    }
    catch (err) {
        throw new Error(`Error downloading snapshots: ${err.message}`);
    }
}
exports.pullSnapshots = pullSnapshots;
function detectSnapshots(projectBasePath, scriptFilePath) {
    // By default, PWT will store snapshots in the `script.spec.js-snapshots` directory.
    // Other paths can be configured, though, and we should add support for those as well.
    // https://playwright.dev/docs/api/class-testconfig#test-config-snapshot-path-template
    const snapshotFiles = (0, util_1.findFilesRecursively)(`${scriptFilePath}-snapshots`);
    return snapshotFiles.map(absolutePath => ({
        absolutePath,
        path: (0, util_1.pathToPosix)(path.relative(projectBasePath, absolutePath)),
    }));
}
exports.detectSnapshots = detectSnapshots;
async function uploadSnapshots(rawSnapshots) {
    if (!rawSnapshots?.length) {
        return [];
    }
    try {
        const snapshots = [];
        for (const rawSnapshot of rawSnapshots) {
            const snapshotStream = fs.createReadStream(rawSnapshot.absolutePath);
            const { data: { key } } = await api_1.checklyStorage.upload(snapshotStream);
            snapshots.push({ key, path: rawSnapshot.path });
        }
        return snapshots;
    }
    catch (err) {
        throw new Error(`Error uploading snapshots: ${err.message}`);
    }
}
exports.uploadSnapshots = uploadSnapshots;
//# sourceMappingURL=snapshot-service.js.map