{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/utils/Helpers.ts"], "sourcesContent": ["import { routing } from '@/libs/I18nRouting';\r\n\r\nexport const getBaseUrl = () => {\r\n  if (process.env.NEXT_PUBLIC_APP_URL) {\r\n    return process.env.NEXT_PUBLIC_APP_URL;\r\n  }\r\n\r\n  if (\r\n    process.env.VERCEL_ENV === 'production'\r\n    && process.env.VERCEL_PROJECT_PRODUCTION_URL\r\n  ) {\r\n    return `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`;\r\n  }\r\n\r\n  if (process.env.VERCEL_URL) {\r\n    return `https://${process.env.VERCEL_URL}`;\r\n  }\r\n\r\n  return 'http://localhost:3000';\r\n};\r\n\r\nexport const getI18nPath = (url: string, locale: string) => {\r\n  if (locale === routing.defaultLocale) {\r\n    return url;\r\n  }\r\n\r\n  return `/${locale}${url}`;\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,aAAa;IACxB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE;QACnC,OAAO,QAAQ,GAAG,CAAC,mBAAmB;IACxC;IAEA,IACE,QAAQ,GAAG,CAAC,UAAU,KAAK,gBACxB,QAAQ,GAAG,CAAC,6BAA6B,EAC5C;QACA,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,6BAA6B,EAAE;IAC/D;IAEA,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE;QAC1B,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE;IAC5C;IAEA,OAAO;AACT;AAEO,MAAM,cAAc,CAAC,KAAa;IACvC,IAAI,WAAW,0HAAA,CAAA,UAAO,CAAC,aAAa,EAAE;QACpC,OAAO;IACT;IAEA,OAAO,CAAC,CAAC,EAAE,SAAS,KAAK;AAC3B", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/app/%5Blocale%5D/%28auth%29/%28center%29/sign-in/%5B%5B...sign-in%5D%5D/page.tsx"], "sourcesContent": ["import { SignIn } from '@clerk/nextjs';\r\nimport { getTranslations, setRequestLocale } from 'next-intl/server';\r\nimport { getI18nPath } from '@/utils/Helpers';\r\n\r\ntype ISignInPageProps = {\r\n  params: Promise<{ locale: string }>;\r\n};\r\n\r\nexport async function generateMetadata(props: ISignInPageProps) {\r\n  const { locale } = await props.params;\r\n  const t = await getTranslations({\r\n    locale,\r\n    namespace: 'SignIn',\r\n  });\r\n\r\n  return {\r\n    title: t('meta_title'),\r\n    description: t('meta_description'),\r\n  };\r\n}\r\n\r\nexport default async function SignInPage(props: ISignInPageProps) {\r\n  const { locale } = await props.params;\r\n  setRequestLocale(locale);\r\n\r\n  return (\r\n    <SignIn path={getI18nPath('/sign-in', locale)} />\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;;;;;AAMO,eAAe,iBAAiB,KAAuB;IAC5D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAC9B;QACA,WAAW;IACb;IAEA,OAAO;QACL,OAAO,EAAE;QACT,aAAa,EAAE;IACjB;AACF;AAEe,eAAe,WAAW,KAAuB;IAC9D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,CAAA,GAAA,2QAAA,CAAA,mBAAgB,AAAD,EAAE;IAEjB,qBACE,8OAAC,sLAAA,CAAA,SAAM;QAAC,MAAM,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,YAAY;;;;;;AAE1C", "debugId": null}}]}