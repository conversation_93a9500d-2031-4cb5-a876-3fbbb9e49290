"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertChannelSubscriptionCodegen = void 0;
const codegen_1 = require("./internal/codegen");
class AlertChannelSubscriptionCodegen extends codegen_1.Codegen {
    describe(resource) {
        return 'Alert Channel Subscription';
    }
    prepare(logicalId, resource, context) {
        if (resource.checkId !== undefined) {
            context.registerAlertChannelCheckSubscription(resource.alertChannelId, resource.checkId);
        }
        if (resource.groupId !== undefined) {
            context.registerAlertChannelGroupSubscription(resource.alertChannelId, resource.groupId);
        }
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    gencode(logicalId, resource, context) {
        // Nothing to generate for this resource.
    }
}
exports.AlertChannelSubscriptionCodegen = AlertChannelSubscriptionCodegen;
//# sourceMappingURL=alert-channel-subscription-codegen.js.map